# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2019 GIG TECHNOLOGY NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG TECHNOLOGY NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG TECHNOLOGY NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.8@@

import base64
import dataclasses
import io
import json
import logging
import os
import pickle
import re
import ssl
import subprocess
import tarfile
import uuid
from datetime import datetime
from operator import itemgetter
from string import ascii_lowercase
from time import time
from typing import Any, Callable, Dict, List, Optional, Pattern, Sequence, Set, Tuple, Union
from urllib.parse import urlparse

import requests
import yaml
from dynaqueue.client.client import Client, TaskStatusResultFuture
from dynaqueue.models import Task, TaskStatus
from dynaqueue.models.task_queue_item import TaskQueueItem
from dynaqueue.scheduler import schedule
from dynaqueue.server import STATUS_FAILED
from dynaqueue.worker.context import get_context
from gevent import sleep
from gevent.pool import Pool
from jinja2.environment import Template
from minio import Minio
from minio.error import S3Error
from mongoengine import DoesNotExist
from netaddr import IPAddress, IPNetwork
from OpenSSL import crypto
from packaging import version as packaging_version
from redis import Redis
from requests import HTTPError

import meneja.business.vco.certificates as certificate_business
import meneja.business.vco.customer.ingress as ingress_business
from meneja.business.g8.cloudspace import get_vm_info
from meneja.business.g8.g8 import get_g8_jwt_from_db
from meneja.business.g8.g8_api import G8Client
from meneja.business.issue import file_issue
from meneja.business.meneja import get_task_logs, get_workflow_info
from meneja.business.security import SecurityValidationError, validate_not_expired
from meneja.business.validation import validate_email
from meneja.business.vco.customer import resource_domain_exists
from meneja.business.vco.customer.cloudspace import (
    add_dns_record,
    create_vm,
    decode_validate_cloudspace_id,
    delete_dns_records,
    list_remote_cloudspaces,
    remove_cloudspace_external_network,
    validate_storage_location,
)
from meneja.business.vco.customer.disks import create_disk_or_attach_to_vm
from meneja.business.vco.customer.portforwards import list_portforwards
from meneja.business.vco.customer.vms import validate_and_write_vm_file
from meneja.business.vco.iaas.virtual_machine import delete_virtual_machine
from meneja.business.vco.metadata.cloudspace import LoadBalancer as LoadBalancerModel
from meneja.business.vco.metadata.cloudspace import LoadBalancerBackEnd, LoadBalancerFrontEnd, LoadBalancerTLS
from meneja.business.vco.metadata.vm import VMMetadata
from meneja.jobs import job, re_raise_workflow_failure
from meneja.lib.connection import (
    DynaqueueConnection,
    ExternalMinioConnection,
    MinioConnection,
    MinioQaDevConnection,
    RedisConnection,
)
from meneja.lib.enumeration import (
    ClusterCertificateStatus,
    ClusterStatus,
    DiskTypes,
    EnvironmentName,
    IssueResourceTypes,
    ManagementClusterType,
    NodePoolRole,
    NodeProvisioningStatus,
    RaidLevel,
    SSLCertificateSource,
    StorageOnlyResources,
    SupportedDNSRecordType,
)
from meneja.lib.exceptions import IdenticalJobQueueingError
from meneja.lib.itsyouonline import ItsyouOnlineClient, OrganizationAlreadyExists
from meneja.lib.utils import (
    execute_without_failing,
    from_dict_with_enum,
    generate_password,
    get_host_by_name,
    get_meneja_url,
    normalize_virtual_machine_name,
    rocket_chat_channel,
    rocket_chat_message,
    send_email,
)
from meneja.model.dns_records import DnsRecord
from meneja.model.healthchecks import FailedUpdateClusterCertificate, UpdateClusterCertificate
from meneja.model.vco import VCO
from meneja.model.vco.certificate import CustomerSSLCertificate
from meneja.model.vco.customer import Customer
from meneja.model.vco.rancher import (
    ClusterHealthCheck,
    ClusterHealthChecks,
    ClusterNode,
    KubernetesCluster,
    LoadBalancer,
    LoadBalancerService,
    NodePool,
    NodeProvisionerStatus,
    NodeStatus,
    RancherAsAServiceSettings,
    RancherFailedJob,
    RancherManagementCluster,
    RancherVersions,
    TaskDetails,
    VirtualMachine,
)
from meneja.structs.meneja.dataclasses.logs import TransitionLogsStruct
from meneja.structs.vco.dataclasses.rke_cluster import (
    ClusterConnectedResources,
    CreateRancherManagementClusterKubernetesClusterStruct,
    LoadBalancerServiceStruct,
    NodeCreationStruct,
    NodeDriverInfoStruct,
    RancherAsAServiceSettingsStruct,
    RancherBridgeRequest,
    RancherBridgeResponse,
    RancherManagementClusterCreationStruct,
    RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct,
    RancherManagementClusterKubernetesClusterNodePoolNodeStruct,
    RancherManagementClusterKubernetesClusterNodePoolRoleStruct,
    RancherManagementClusterKubernetesClusterNodePoolStruct,
    RancherManagementClusterKubernetesClusterStruct,
    RancherManagementClusterNodeStatusStruct,
    RancherManagementClusterNodeStruct,
    RancherManagementClusterStruct,
    RancherSupportedKubernetesClusterVersionStruct,
    RancherVersionsGetStruct,
    RancherVersionStruct,
    SimpleKubernetesClusterNodeStruct,
    UpdateClusterJwtResult,
    VirtualMachineInfoStruct,
    VirtualMachineSimpleInfoStruct,
    VirtualMachineTemplateStruct,
)

TEMPLATES_PATH = os.path.join(os.path.dirname(__file__), "templates")
RANCHER_SETUP_PATH = os.path.join(TEMPLATES_PATH, "rancher_setup.sh.j2")
UPDATE_JWT_PATH = os.path.join(TEMPLATES_PATH, "update_jwt.py.j2")
REPORT_FAILURE_PATH = os.path.join(TEMPLATES_PATH, "report_failure.j2")
UPDATE_CERTIFICATE_PATH = os.path.join(TEMPLATES_PATH, "update_certificate.py.j2")

MGMT_RKE_PATH = os.path.join(TEMPLATES_PATH, "rke-management-cfg.yml.j2")
RANCHER_RESOURCES_PATH = os.path.join(TEMPLATES_PATH, "rancher.yaml.j2")
RANCHER_AGENT_PATH = os.path.join(TEMPLATES_PATH, "rancher-agent.yaml.j2")
NODE_DRIVER_PATH = os.path.join(TEMPLATES_PATH, "node_driver.yaml.j2")
VM_MOUNT_PATH = os.path.join(TEMPLATES_PATH, "mount_vm.sh.j2")
CREATE_RKE2_PATH = os.path.join(TEMPLATES_PATH, "create_rke2_cluster.yml.j2")
HELM_PATH = os.path.join(TEMPLATES_PATH, "helm.sh.j2")
INGRESS_VALUES_PATH = os.path.join(TEMPLATES_PATH, "ingress_values.yml.j2")
CERT_MGR_VALUES_PATH = os.path.join(TEMPLATES_PATH, "cert_mgr_values.yml.j2")
CERT_ISSUER_PATH = os.path.join(TEMPLATES_PATH, "cert_issuer.yml.j2")
CCM_ERROR_PATH = os.path.join(TEMPLATES_PATH, "ccm_error.md.j2")
CSI_PATH = os.path.join(TEMPLATES_PATH, "csi.yml.j2")
IP_PATTERN = re.compile(r"https://((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.|:)){4}")
K8S_AGENT_VERSION_PATTERN = re.compile(r"^Kubernetes-agent-(\d+)\.(\d+)\.(\d+).tar.gz$")
NODE_DRIVER_VERSIONED = re.compile(r"^docker-machine-driver_v(\d+)\.(\d+)\.(\d+).tgz$")
NODE_DRIVER_REQUEST_PATTERN = re.compile(r"^docker-machine-driver-(?P<vco_suffix>[\w]+)$")
NODE_DRIVER_UI_VERSIONED = re.compile(r"^node-driver-ui_v(\d+)\.(\d+)\.(\d+)/$")
K8S_RESOURCES_BUCKET = "kubernetes-agent"
CERT_MANAGER_VERSION = "1.7.1"
USER_DATA = {
    "disk_setup": {"/dev/vdb": {"layout": True, "table_type": "gpt", "overwrite": True}},
    "fs_setup": [{"label": "datadisk", "filesystem": "ext4", "device": "/dev/vdb", "partition": 1}],
}
DISK_SIZE = 20
DATA_DISKS = [50]
LOG_LINE_RE = re.compile(r'^time="\d+-\d+-\d+T\d+:\d+:\d+Z" level=(?P<level>\w+) msg="(?P<msg>.*)"$')
KUB_CLUSTER_NAME = re.compile(r"^[a-z0-9-]+$")
KUB_NODE_POOL_NAME = re.compile(r"^[a-z0-9-]+$")
BITNAMI_CHARTS = "https://charts.bitnami.com/bitnami"
JETSTACK_CHARTS = "https://charts.jetstack.io"
KUBERNETES_CHARTS = "https://kubernetes.github.io/ingress-nginx"
INGRESS_CONTROLLER_CHART = "ingress-nginx"
CERT_MANAGER_CHART = "cert-manager"
KUBE_CONFIG_PATH = "/etc/rke/kube_config_rke-cfg.yml"
logger = logging.getLogger(__name__)
MONITOR_EMAIL = os.environ.get("K8s_MONITOR_EMAIL", "<EMAIL>")
CERT_NAMESPACE = "cattle-system"
CERT_SECRET_NAME = "cattle-keys-ingress"
GET_CERT_CMD = f"kubectl --kubeconfig {KUBE_CONFIG_PATH} get secret {CERT_SECRET_NAME} -n {CERT_NAMESPACE} -o json"


def _report_failure(*_, **kwargs) -> None:
    workflow_id = kwargs.get("workflow_id")

    if not workflow_id:
        task_queue_item: TaskQueueItem = get_context()
        workflow_id = task_queue_item.status.id
    url = get_meneja_url()
    title = task_queue_item.status.title
    failed_job = RancherFailedJob(
        title=title,
        workflow_id=workflow_id,
        timestamp=time(),
    )
    failed_job.save()
    if EnvironmentName.current() in (EnvironmentName.DEV, EnvironmentName.TEST):
        return
    with open(REPORT_FAILURE_PATH, "r", encoding="utf8") as file_handle:
        template = Template(file_handle.read())
    message = template.render(meneja_url=url, workflow_id=workflow_id)
    if EnvironmentName.current() not in (EnvironmentName.DEV, EnvironmentName.TEST):
        send_email(MONITOR_EMAIL, "K8s failure", message, send_from="<EMAIL>")


PRIVATE_NETWORKS = (IPNetwork("***********/16"), IPNetwork("**********/12"), IPNetwork("10.0.0.0/8"))


def create_management_cluster(
    jwt: str,
    customer_id: str,
    cluster_creation_struct: RancherManagementClusterCreationStruct,
    vco_id: str,
    iam_key: str,
    user_name: str = None,
) -> str:
    """Creates a Management cluster

    Args:
        jwt (str): current jwt
        customer_id (str): Customer ID
        cluster_creation_struct(ClusterCreationStruct): Struct containing information needed for cluster creation
        vco_id (str): VCO ID
        iam_key (str): IAM public key

    Returns:
        str: management_cluster_id
    """
    # Validate input

    if not KUB_CLUSTER_NAME.match(cluster_creation_struct.name):
        raise ValueError("The cluster name can only contain lowercase, digits and hyphens!")
    if RancherManagementCluster.check_cluster_name_exists(cluster_creation_struct.name, customer_id):
        raise ValueError("A cluster with the same name already exists")
    try:
        Customer.get_and_validate_vco(customer_id=customer_id, vco_id=vco_id, only=["customer_id"])
    except DoesNotExist as error:
        raise KeyError(f"Customer with id {customer_id} does not exist.") from error
    if cluster_creation_struct is None:
        raise ValueError("Cluster creation input is required!")
    if not cluster_creation_struct.cloudspace_id or not cluster_creation_struct.cloudspace_id.strip():
        raise ValueError("cloudspace_id is a required field!")
    location, g8_cloudspace_id = decode_validate_cloudspace_id(cluster_creation_struct.cloudspace_id)
    validate_storage_location(location, StorageOnlyResources.MANAGEMENT_CLUSTER)
    try:
        cloudspace = G8Client(location, jwt).get_cloudspace_info(g8_cloudspace_id)
        if cloudspace["cloudspace_mode"] == "private":
            raise ValueError("Cannot create Management Cluster using private cloudspace")
    except HTTPError as ex:
        if ex.response.status_code == 404:
            raise KeyError(f"Cloudspace with id {cluster_creation_struct.cloudspace_id} does not exist") from ex
        else:
            raise ex
    if not cluster_creation_struct.domain_name or not cluster_creation_struct.domain_name.strip():
        raise ValueError("domain_name is a required field!")
    if cluster_creation_struct.ssl_certificate_source == SSLCertificateSource.CERTIFICATE_STORE:
        the_certificate = _get_matching_certificate(customer_id, cluster_creation_struct)
    else:
        validate_email(cluster_creation_struct.letsencrypt_email)
        for priv_net in PRIVATE_NETWORKS:
            if IPAddress(cluster_creation_struct.external_network_ip) in priv_net:
                cluster_creation_struct.letsencrypt_email = ""
                try:
                    _get_matching_certificate(customer_id, cluster_creation_struct, True)
                except KeyError:
                    try:
                        certificate_business.add_ssl_lets_encrypt_cert(
                            customer_id=customer_id,
                            vco_id=vco_id,
                            domain=cluster_creation_struct.domain_name,
                            user_name=user_name,
                        )
                    except ValueError as error:
                        if resource_domain_exists(customer_id, cluster_creation_struct.domain_name):
                            raise ValueError(
                                f"Could not create certificate for domain {cluster_creation_struct.domain_name} "
                                "that resolves to this private ipaddress.\n"
                                "This is only supported for domains created via the builtin DNS feature.\n"
                                f"Reason: {error}"
                            ) from error
                        else:
                            raise
                the_certificate = _get_matching_certificate(customer_id, cluster_creation_struct, True)
                break
        else:
            if not (cluster_creation_struct.letsencrypt_email and cluster_creation_struct.letsencrypt_email.strip()):
                raise ValueError("The letsencrypt email address is required for the use of letsencrypt.")
            the_certificate = None

    current_settings = RancherAsAServiceSettings.get_settings()
    if current_settings.default_version_tag == "not set":
        raise ValueError("Rancher default version tag setting should be set before creating a Management Cluster")
    if not cluster_creation_struct.tag:
        cluster_creation_struct.tag = current_settings.default_version_tag
    else:
        supported_versions = RancherVersions.list()
        for version in supported_versions:
            if version.tag == cluster_creation_struct.tag:
                break
        else:
            raise ValueError(f"Rancher version {cluster_creation_struct.tag} is not currently supported")
    if RancherManagementCluster.check_cluster_hostname_exists(cluster_creation_struct.domain_name):
        raise ValueError(f"Domain name {cluster_creation_struct.domain_name} is already in use")
    if RancherManagementCluster.check_cluster_ip_exists(cluster_creation_struct.external_network_ip):
        raise ValueError(f"External network IP {cluster_creation_struct.external_network_ip} is already in use")
    location, decoded_cloudspace_id = decode_validate_cloudspace_id(cluster_creation_struct.cloudspace_id)
    external_networks = G8Client(location, jwt=jwt).get_cloudspace_external_networks(
        cloudspace_id=decoded_cloudspace_id
    )
    for external_network in external_networks["external_networks"]:
        if cluster_creation_struct.external_network_ip == external_network["external_network_ip"].split("/")[0]:
            break
    else:
        raise ValueError("IP address does not exist in cloudspace external networks")
    _validate_ports(jwt, cluster_creation_struct)
    if len(cluster_creation_struct.domain_name) > 255:
        raise ValueError("Domain name cannot exceed 255 chars")

    if any(map(lambda label: len(label) > 63, cluster_creation_struct.domain_name.split("."))):
        raise ValueError("Domain name labels cannot exceed 63 chars")

    # Alright, go
    vco = VCO.get_by_id(vco_id)
    tech_auth_org, api_key_secret = _setup_iam(customer_id, cluster_creation_struct, vco)
    api_token_id = generate_password(5).lower()
    api_token = generate_password(54).lower()
    management_cluster = RancherManagementCluster(
        name=cluster_creation_struct.name,
        domain_name=cluster_creation_struct.domain_name,
        cloudspace_id=cluster_creation_struct.cloudspace_id,
        cluster_type=cluster_creation_struct.cluster_type.value,
        customer_id=customer_id,
        status=ClusterStatus.MODELED.value,
        ssl_certificate_source=cluster_creation_struct.ssl_certificate_source.value,
        letsencrypt_emailaddress=cluster_creation_struct.letsencrypt_email,
        api_token_id=api_token_id,
        api_token=api_token,
        technical_authorization_organization=tech_auth_org,
        vco_id=vco_id,
        external_network_ip=cluster_creation_struct.external_network_ip,
        physical_storage=cluster_creation_struct.physical_storage,
    )
    management_cluster.save()
    _create_management_cluster(
        jwt,
        customer_id,
        cluster_creation_struct,
        vco_id,
        str(management_cluster.management_cluster_id),
        iam_key,
        the_certificate,
        cluster_creation_struct.letsencrypt_email,
        api_token_id,
        api_token,
        tech_auth_org,
        api_key_secret,
        vco.iam_domain,
    )
    return management_cluster.management_cluster_id


def _validate_ports(jwt: str, cluster_creation_struct: RancherManagementClusterCreationStruct):
    # List cloudspace PFs, check PFs with the same ip, check ports
    needed_ports = (80, 443, 6443)
    location, g8_cloudspace_id = decode_validate_cloudspace_id(cluster_creation_struct.cloudspace_id)
    pfs = list_portforwards(jwt, location, g8_cloudspace_id)
    for pf in pfs:
        if pf.public_ip == cluster_creation_struct.external_network_ip and pf.public_port in needed_ports:
            raise ValueError(f"Port {pf.public_port} is in use by portforward {pf.portforward_id}")
    # Repeat with LBs and RPs
    lbs = ingress_business.list_load_balancers(location, jwt, g8_cloudspace_id)
    rps = ingress_business.list_reverse_proxies(location, jwt, g8_cloudspace_id)
    for lb in lbs:
        if (
            lb["front_end"]["ip_address"] == cluster_creation_struct.external_network_ip
            and lb["front_end"]["port"] in needed_ports
        ):
            raise ValueError(f"Port {lb['front_end']['port']} is in use by loadbalancer {lb['loadbalancer_id']}")
    for rp in rps:
        if rp["front_end"]["ip_address"] != cluster_creation_struct.external_network_ip:
            continue
        for port in (rp["front_end"]["http_port"], rp["front_end"]["http_port"]):
            if port in needed_ports:
                raise ValueError(f"Port {port} is in use by reverseproxy {rp['reverseproxy_id']}")


def delete_management_cluster(customer_id: str, jwt: str, management_cluster_id: str, vco_id: str) -> None:
    """Deletes a management cluster by id

    Args:
        customer_id (str): Customer ID
        jwt (str): Current jwt
        management_cluster_id (str): Management cluster id to be deleted
        vco_id (str): VCO ID
    """
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if not management_cluster.customer_id == customer_id:
        raise KeyError("Cluster not found.")
    if management_cluster.status == ClusterStatus.DELETED.value:
        logger.info("Attempted to delete a management cluster that is already deleted")
        return

    location, decoded_cloudspace_id = decode_validate_cloudspace_id(management_cluster.cloudspace_id)

    init_deletion_task = Task("Delete Management Cluster")
    init_deletion_task.set_workload(_init_deletion, management_cluster_id, init_deletion_task.id)

    cleanup_lbs_task = Task("Delete Loadbalancers")
    cleanup_lbs_task.set_workload(
        _cleanup_loadbalancers, management_cluster_id, customer_id, location, jwt, decoded_cloudspace_id
    )

    init_deletion_task.on_success = cleanup_lbs_task
    init_deletion_task.on_failure = cleanup_lbs_task

    cleanup_sps_task = Task("Delete Serverpool")
    cleanup_sps_task.set_workload(
        _cleanup_serverpools, management_cluster_id, customer_id, location, jwt, decoded_cloudspace_id
    )
    cleanup_lbs_task.on_success = cleanup_sps_task
    cleanup_lbs_task.on_failure = cleanup_sps_task

    cleanup_vms_task = Task("Delete Virtual Machines")
    cleanup_vms_task.set_workload(
        _cleanup_vms, management_cluster.name, management_cluster_id, customer_id, location, jwt, decoded_cloudspace_id
    )
    cleanup_sps_task.on_success = cleanup_vms_task
    cleanup_sps_task.on_failure = cleanup_vms_task

    cleanup_nodes_task = Task("Delete Nodes")
    cleanup_nodes_task.set_workload(_cleanup_nodes, management_cluster_id, jwt, customer_id)
    cleanup_vms_task.on_success = cleanup_nodes_task
    cleanup_vms_task.on_failure = cleanup_nodes_task

    cleanup_ingress_resources_task = Task("Cleanup Ingress resources")
    cleanup_ingress_resources_task.set_workload(
        _cleanup_ingress_resources, customer_id, jwt, management_cluster_id, vco_id
    )
    cleanup_nodes_task.on_success = cleanup_ingress_resources_task
    cleanup_nodes_task.on_failure = cleanup_ingress_resources_task

    cleanup_iam_task = Task("Cleanup IAM")
    cleanup_iam_task.set_workload(_cleanup_iam, management_cluster_id)
    cleanup_ingress_resources_task.on_success = cleanup_iam_task
    cleanup_ingress_resources_task.on_failure = cleanup_iam_task

    finalize_deletion_task = Task("Finalize deletion status")
    finalize_deletion_task.set_workload(_finalize_deletion_status, management_cluster_id, init_deletion_task.id)
    cleanup_iam_task.on_success = finalize_deletion_task
    cleanup_iam_task.on_failure = finalize_deletion_task

    client: Client = DynaqueueConnection.get_client()
    client.submit_task_async(init_deletion_task)


def get_pipeline_logs(
    customer_id: str,
    cluster_id: str,
    transition_id: str,
    include_debug_logs: bool = False,
) -> List[TransitionLogsStruct]:
    """Get logs for a specific pipeline

    Args:
        customer_id: Customer ID
        cluster_id (str): Management cluster ID
        transition_id {int} -- Workflow object.
        include_debug_logs {bool} -- Flag to whether to include debug log records

    Raises:
        KeyError: Does not exist

    Returns:
        List[TaskLogsStruct]: List containing logs for each task in the pipeline
    """
    cluster = _check_mgmt_cluster(customer_id, cluster_id)
    tasks = cluster.transition_tasks_details
    for task in tasks:
        if task.transition_id == transition_id:
            break
    else:
        raise KeyError(f"Workflow {transition_id} does not exist for cluster {cluster_id}")
    workflow_info = get_workflow_info(transition_id, include_debug_logs=include_debug_logs)
    return _get_workflow_logs(transition_id, workflow_info, [])


def _create_vm_and_save_id(
    management_cluster_id: str,
    location: str,
    jwt: str,
    decoded_cloudspace_id: int,
    cloudspace_id: str,
    account_id: int,
    vco_id: str,
    vm_payload: dict,
    customer_id: str,
    physical_storage: bool = False,
    node_pool_identifier: str = None,
    master_node: bool = False,
):
    # This line validates that this node is still a part of the cluster and was not deleted by node driver
    RancherManagementCluster.set_cluster_node_vm_id(
        management_cluster_id=management_cluster_id, cloudspace_id=cloudspace_id, node_name=vm_payload["name"], vm_id=-1
    )
    logger.info("Creating VM %s in CS %s in location %s", vm_payload["name"], cloudspace_id, location)
    vm_id = create_vm(
        customer_id=customer_id,
        location=location,
        jwt=jwt,
        cloudspace_id=decoded_cloudspace_id,
        account_id=account_id,
        vco_id=vco_id,
        **vm_payload,
    )["vm_id"]
    if master_node:
        g8_client = G8Client(name=location, jwt=jwt)
        try:
            anti_affinity_groups = g8_client.list_cloudspace_anti_affinity_groups(cloudspace_id=decoded_cloudspace_id)
            if not any(anti_affinity["group_id"] == node_pool_identifier for anti_affinity in anti_affinity_groups):
                g8_client.set_cloudspace_anti_affinity_group(decoded_cloudspace_id, -1, node_pool_identifier)
            g8_client.add_vm_anti_affinity_group(vm_id, node_pool_identifier)
        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception("Failed to add vm to anti affinity group, deleting VM!")
            delete_virtual_machine(
                jwt=jwt,
                customer_id=customer_id,
                location=location,
                cloudspace_id=decoded_cloudspace_id,
                vm_id=vm_id,
                permanently=True,
                force=True,
            )
            logger.info("VM successfully deleted!")
            raise
    if physical_storage:
        try:
            create_disk_or_attach_to_vm(
                customer_id=customer_id,
                account_id=account_id,
                location=location,
                jwt=jwt,
                name="cluster-physical-node",
                description="mgmt-cluster-disk",
                disk_type=DiskTypes.PHYSICAL,
                disk_size=DATA_DISKS[0],
                vm_id=vm_id,
                iops=2000,
                raid_level=RaidLevel.NO_RAID.value,
            )
            g8_client.start_vm(vm_id)
        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception("Failed to add physical disk to machine, deleting VM!")
            delete_virtual_machine(
                jwt=jwt,
                customer_id=customer_id,
                location=location,
                cloudspace_id=decoded_cloudspace_id,
                vm_id=vm_id,
                permanently=True,
                force=True,
            )
            logger.info("VM successfully deleted!")
            raise

    logger.info("[+] VM successfully created ID %s", vm_id)
    logger.info("Saving VM id on the model")
    try:
        RancherManagementCluster.set_cluster_node_vm_id(
            management_cluster_id=management_cluster_id,
            cloudspace_id=cloudspace_id,
            node_name=vm_payload["name"],
            vm_id=vm_id,
        )
    except KeyError:
        delete_virtual_machine(
            jwt=jwt,
            customer_id=customer_id,
            location=location,
            cloudspace_id=decoded_cloudspace_id,
            vm_id=vm_id,
            permanently=True,
            force=True,
        )
        raise
    return vm_id


def _node_provisioning(
    management_cluster_id: str,
    location: str,
    jwt: str,
    cloudspace_id: str,
    vco_id: str,
    node_struct: NodeCreationStruct,
    iam_key: str,
    customer_id: str,
):
    this: TaskQueueItem = get_context()
    vm_id = this.status.result_value
    logger.info("Configuring node vm id %s", vm_id)
    _configure_node(jwt, node_struct, location, management_cluster_id, vm_id)

    logger.info("Waiting for vm agent to be RUNNING")
    _wait_vm_agent_ready(location=location, jwt=jwt, vm_id=vm_id)

    logger.info("Mount config file")
    _mount_vm_file(
        location=location,
        jwt=jwt,
        vm_id=vm_id,
        vco_id=vco_id,
        iam_key=iam_key,
        customer_id=customer_id,
        cloudspace_id=cloudspace_id,
        management_cluster_id=management_cluster_id,
        physical_storage=node_struct.physical_storage,
    )

    logger.info("Provisioning is complete, update provisioner status")
    RancherManagementCluster.set_cluster_node_provisioner_status(
        management_cluster_id=management_cluster_id,
        node_name=node_struct.node_name,
        cloudspace_id=cloudspace_id,
        status=NodeProvisioningStatus.DONE,
    )
    _set_provisioner_status_in_redis(
        management_cluster_id, cloudspace_id, node_struct.node_name, NodeProvisioningStatus.DONE
    )


def _clean_up_node_model(management_cluster_id: str, node_name: str, cloudspace_id: str):
    execute_without_failing(_report_failure)
    execute_without_failing(
        RancherManagementCluster.delete_node_by_name,
        management_cluster_id=management_cluster_id,
        node_name=node_name,
        cloudspace_id=cloudspace_id,
    )
    execute_without_failing(
        _set_provisioner_status_in_redis,
        management_cluster_id=management_cluster_id,
        cloudspace_id=cloudspace_id,
        node_name=node_name,
        status=NodeProvisioningStatus.FAILED,
    )
    re_raise_workflow_failure()


def _create_and_provision_node_vm_async(
    management_cluster_id: str,
    location: str,
    jwt: str,
    decoded_cloudspace_id: int,
    account_id: int,
    vco_id: str,
    vm_payload: dict,
    node_struct: NodeCreationStruct,
    iam_key: str,
    customer_id: str,
    node_pool_identifier: str = None,
    master_node: bool = False,
):
    vm_creation_task = Task(
        f"Provisioning RKE(2) node on cluster ID {management_cluster_id}: Create VM", timeout=3600, retry_count=1
    )
    vm_creation_task.set_workload(
        _create_vm_and_save_id,
        management_cluster_id=management_cluster_id,
        customer_id=customer_id,
        location=location,
        jwt=jwt,
        decoded_cloudspace_id=decoded_cloudspace_id,
        cloudspace_id=node_struct.cloudspace_id,
        account_id=account_id,
        vco_id=vco_id,
        vm_payload=vm_payload,
        physical_storage=node_struct.physical_storage,
        node_pool_identifier=node_pool_identifier,
        master_node=master_node,
    )
    vm_creation_task.object_type = "add_rancher_node"
    vm_creation_task.object_id = management_cluster_id

    cleanup_vm_creation = Task("Report VM creation failure, clean up node model")
    cleanup_vm_creation.set_workload(
        _clean_up_node_model,
        management_cluster_id=management_cluster_id,
        node_name=node_struct.node_name,
        cloudspace_id=node_struct.cloudspace_id,
    )

    node_provisioning_task = Task("Provisioning RKE(2) node: Provisioning", timeout=3600, retry_count=1)
    node_provisioning_task.set_workload(
        _node_provisioning,
        jwt=jwt,
        node_struct=node_struct,
        iam_key=iam_key,
        location=location,
        management_cluster_id=management_cluster_id,
        customer_id=customer_id,
        vco_id=vco_id,
        cloudspace_id=node_struct.cloudspace_id,
    )

    cleanup_vm = Task("Deleting VM")
    cleanup_vm.set_workload(
        _fail_node_provisioning,
        jwt,
        customer_id=customer_id,
        location=location,
        cloudspace_id=node_struct.cloudspace_id,
        node_name=node_struct.node_name,
        decoded_cloudspace_id=decoded_cloudspace_id,
        management_cluster_id=management_cluster_id,
    )

    vm_creation_task.on_success = node_provisioning_task
    vm_creation_task.on_failure = cleanup_vm_creation

    node_provisioning_task.on_failure = cleanup_vm

    client: Client = DynaqueueConnection.get_client()
    client.submit_task_async(vm_creation_task)


def create_node(
    customer_id: str,
    management_cluster_id: str,
    node_struct: NodeCreationStruct,
    jwt: str,
    vco_id: str,
    iam_key: str,
) -> int:
    """Creates a node in rke cluster

    Args:
        customer_id (str): Customer Id
        management_cluster_id (str): Management Cluster Id
        NodeStruct (NodeCreationStruct): Struct containing node creation specs
        jwt (str): current jwt
        vco_id (str): VCO ID
        iam_key (str): IAM public key

    Returns:
        int: Virtual Machine Id
    """
    _check_mgmt_cluster(customer_id, management_cluster_id)
    location, decoded_cloudspace_id = decode_validate_cloudspace_id(node_struct.cloudspace_id)
    g8_client = G8Client(location, jwt=jwt)
    image = g8_client.get_image(g8_client.list_images(tags=os.environ.get("UBUNTU_IMAGE_TAG"))[0]["image_id"])
    account_id = Customer.get_location(customer_id=customer_id, location=location).g8_account_id
    node_struct.user_data.update(USER_DATA)
    vm_payload = dict(
        name=node_struct.node_name,
        description="RKE cluster Node",
        vcpus=node_struct.vcpus,
        memory=node_struct.memory,
        disk_size=DISK_SIZE,
        data_disks=[] if node_struct.physical_storage else DATA_DISKS,
        image_id=image["image_id"],
        boot_type=image["boot_type"],
        enable_vm_agent=True,
        start_vm=not node_struct.physical_storage,
        user_data=yaml.dump(node_struct.user_data),
    )
    _add_node_to_management_cluster(management_cluster_id, node_struct.cloudspace_id, -1, node_struct.node_name)
    _create_and_provision_node_vm_async(
        management_cluster_id=management_cluster_id,
        location=location,
        jwt=jwt,
        decoded_cloudspace_id=decoded_cloudspace_id,
        account_id=account_id,
        vco_id=vco_id,
        vm_payload=vm_payload,
        node_struct=node_struct,
        iam_key=iam_key,
        customer_id=customer_id,
        node_pool_identifier=node_struct.node_pool_identifier,
        master_node=node_struct.master_node,
    )
    try:
        _scan_for_clusters_that_need_integration_components(jwt, management_cluster_id)
    except IdenticalJobQueueingError:
        pass
    return -1


def _get_provisioner_redis_key_name(management_cluster_id, cloudspace_id, node_name):
    return f"provisioner-cluster{management_cluster_id}-{cloudspace_id}-{node_name}"


def _get_provisioner_status_from_redis(management_cluster_id, cloudspace_id, node_name, timeout=60):
    redis_client = RedisConnection.get_client()
    key = _get_provisioner_redis_key_name(management_cluster_id, cloudspace_id, node_name)

    redis_value = redis_client.brpoplpush(key, key, timeout)
    if not redis_value:
        raise TimeoutError(
            "Provisioning status for node {node_name}: in rke {management_cluster_id} was not set in redis"
        )
    status = pickle.loads(redis_value)
    return status


def _set_provisioner_status_in_redis(management_cluster_id, cloudspace_id, node_name, status: NodeProvisioningStatus):
    redis_client = RedisConnection.get_client()
    key = _get_provisioner_redis_key_name(management_cluster_id, cloudspace_id, node_name)
    pipeline = redis_client.pipeline()
    pipeline.lpush(key, pickle.dumps(status.value))
    pipeline.expire(key, 24 * 3600)
    pipeline.execute()


def poll_node_provisioning(
    customer_id: str, management_cluster_id: str, cloudspace_id: int, node_name: str, wait: int = 60
) -> Tuple[NodeProvisioningStatus, int]:
    """Polls the node provisioning for its completion.
    This method returns immediately if the provisioning
    has finished but blocks for 'wait' seconds maximally if not.

    Args:
        customer_id (str): VCO Customer ID
        management_cluster_id (str): Rancher Management Cluster ID
        cloudspace_id (str): Cloudspace ID
        vm_id (int): Virtual Machine ID
        wait (int, optional): Number of seconds to block to wait for the completion. Defaults to 60.

    Returns:
        Tuple[NodeProvisioningStatus, int]
    """
    if wait < 60:
        raise ValueError("Minimum wait period is 1 minute.")
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id)
    try:
        cluster_node: ClusterNode = cluster.get_cluster_node(node_name=node_name, cloudspace_id=cloudspace_id)
    except KeyError as exp:
        raise RuntimeError(
            "Node {node_name} was not found. Perhaps it was deleted as a result of failed provisioning"
        ) from exp
    if cluster_node.provisioner_status.status == NodeProvisioningStatus.DONE:
        return NodeProvisioningStatus.DONE, cluster_node.vm_id
    try:
        provisioner_status = _get_provisioner_status_from_redis(management_cluster_id, cloudspace_id, node_name)
    except TimeoutError:
        return NodeProvisioningStatus.INPROGRESS, cluster_node.vm_id
    return provisioner_status, cluster_node.vm_id


@job(
    (
        "Delete management cluster {management_cluster_id} node"
        " {cloudspace_id}:{node_name}:{vm_id} for customer{customer_id}"
    ),
    block=True,
    timeout=3600,
    object_type="delete_management_cluster_node",
    object_id="{management_cluster_id}:{cloudspace_id}:{node_name}:{vm_id}",
    cleanup=_report_failure,
)
def delete_node(
    management_cluster_id: str,
    cloudspace_id: str,
    jwt: str,
    customer_id: str,
    vm_id: Optional[int] = None,
    node_name: Optional[str] = None,
) -> Dict:
    """Deletes a node in cluster by node_name

    Args:
        management_cluster_id (str): Management Cluster Id
        cloudspace_id (str): Cloudspace Id
        jwt (str): Current Jwt
        customer_id (str): Customer Id
        vm_id (int): Virtual Machine Id
        node_name (str): Virtual Machine Name

    Raises:
        KeyError: If node does not exist
        RuntimeError: Raised on failed deletion of node

    Returns:
        dict: success model
    """
    if vm_id is None and node_name is None:
        raise ValueError("")

    def predicate(node: ClusterNode) -> bool:
        return node.cloudspace_id == cloudspace_id and (
            node.vm_id == vm_id if vm_id is not None else node.name == node_name
        )

    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if management_cluster.customer_id != customer_id:
        raise KeyError("Cluster not found!")
    try:
        node: ClusterNode = next(node for node in management_cluster.cluster_nodes if predicate(node))
    except StopIteration:
        return dict(success=True)

    location, decoded_cloudspace_id = decode_validate_cloudspace_id(cloudspace_id)
    try:
        _delete_vm(jwt, customer_id, location, decoded_cloudspace_id, node.vm_id)
    except HTTPError as exc:
        if exc.response.status_code != 404:
            raise
    RancherManagementCluster.delete_node(management_cluster_id, node.vm_id)
    _scan_for_clusters_that_need_integration_components(jwt, management_cluster_id)
    return dict(success=True)


def get_node_driver_binary(vco_id: str, version: str, driver_binary_path: str) -> bytes:
    """Fetch latest Node Driver Binary

    Args:
        vco_id (str): VCO ID
        version (str): Driver version
        driver_binary_path (str): Filename of Driver

    Returns:
        bytes: driver binary
    """
    match = NODE_DRIVER_REQUEST_PATTERN.match(driver_binary_path)
    if not match:
        raise KeyError("Object request not found")
    vco_suffix = match.group("vco_suffix")
    vco = VCO.get_by_id(vco_id)
    if vco_suffix != _get_node_driver_name(vco):
        raise KeyError("File not found")

    try:
        driver_binary = _get_versioned_node_driver(version)
    except S3Error as err:
        if err.code == "NoSuchKey":
            raise KeyError("File not found") from err
        else:
            raise err
    return driver_binary


def get_node_driver_ui_resource(
    ui_resource: str, vco_id: Optional[str] = None, cluster_domain: Optional[str] = None
) -> bytes:
    """Fetch latest Node Driver Resource

    Args:
        ui_resource (str): UI resource (css/js/favicon)

    Raises:
        KeyError: _description_

    Returns:
        bytes: UI Resource
    """
    if not (vco_id or cluster_domain):
        raise RuntimeError("Neither VCO ID nor domain set")
    if cluster_domain:
        cluster = RancherManagementCluster.get_by_domain_name(cluster_domain)
        vco_id = cluster.vco_id
    if ui_resource == "logo.png":
        return VCO.get_logo(vco_id)
    vco: VCO = VCO.get_by_id(vco_id)
    minio_client = MinioConnection.get_client()
    latest_package, _ = _get_latest_resource(minio_client, NODE_DRIVER_UI_VERSIONED)
    try:
        response = minio_client.get_object(K8S_RESOURCES_BUCKET, f"{latest_package}{ui_resource}")
    except S3Error as err:
        if err.code == "NoSuchKey":
            raise KeyError(f"File not found for the requested resource {ui_resource}") from err
        else:
            raise err
    result: bytes = response.data
    if ui_resource.endswith(".js") or ui_resource.endswith(".css"):
        result = result.replace(b"vco", _get_node_driver_name(vco).encode("utf-8"))
    return result


def set_rancher_as_a_service_settings(settings: RancherAsAServiceSettingsStruct) -> None:
    """Updates the Rancher settings to the database

    Args:
        settings (RancherAsAServiceSettingsStruct): New settings
    """
    current_settings = RancherAsAServiceSettings.get_settings()
    for field in dataclasses.fields(settings):
        setattr(current_settings, field.name, getattr(settings, field.name))
    current_settings.save()


def get_kube_config(customer_id: str, cluster_id: str, jwt: str) -> str:
    """Returns the kube config file

    Args:
        customer_id (str): Customer ID
        management_cluster_id (str): Management cluster id
        jwt (str): current jwt
    """
    cluster = _check_mgmt_cluster(customer_id=customer_id, management_cluster_id=cluster_id)
    vm_id = cluster.management_cluster_nodes[0].vm_id
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    kube_config = G8Client(location, jwt=jwt).read_vm_file(vm_id=vm_id, size=100000, filepath=KUBE_CONFIG_PATH)
    return IP_PATTERN.sub(f"https://{cluster.external_network_ip}:", base64.b64decode(kube_config["result"]).decode())


def update_health_status(
    management_cluster_id: str,
    customer_id: str,
    cloudspace_id: str,
    vm_id: str,
    status: RancherManagementClusterNodeStatusStruct,
) -> None:
    """Updates the health status of a node

    Args:
        management_cluster_id (str): Management cluster id
        customer_id (str): Customer id
        cloudspace_id (str): Cloudspace id
        vm_id (str): Virtual machine id
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id)
    node_status = NodeStatus(**dataclasses.asdict(status))
    node_status.timestamp = int(time())
    if cluster.cloudspace_id == cloudspace_id:
        vm: VirtualMachine = None
        for vm in cluster.management_cluster_nodes:
            if vm.vm_id == vm_id:
                RancherManagementCluster.update_management_cluster_node_health_status(
                    management_cluster_id, cloudspace_id, vm_id, node_status
                )
                return
    RancherManagementCluster.update_cluster_node_health_status(management_cluster_id, cloudspace_id, vm_id, node_status)


def get_kubernetes_clusters(
    management_cluster_id: str, customer_id: str
) -> List[RancherManagementClusterKubernetesClusterStruct]:
    """Gets the list of kubernetes clusters managed by the management cluster

    Args:
        management_cluster_id (str): Management cluster id
        customer_id (str): Customer id

    Returns:
        List[RancherManagementClusterKubernetesCluster]
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id)

    url = "/v3/clusters?internal=false"
    result = []
    for cluster in _rancher_api_get(cluster, url)["data"]:
        result.append(
            RancherManagementClusterKubernetesClusterStruct(
                cluster["id"],
                cluster["name"],
                cluster["state"],
                cluster["nodeCount"],
                cluster["provider"],
                cluster["version"]["gitVersion"] if cluster.get("version") else "Not available",
            )
        )
    return result


def get_kubernetes_cluster(
    vco_id: str, management_cluster_id: str, customer_id: str, kubernetes_cluster_id: str
) -> RancherManagementClusterKubernetesClusterStruct:
    """Gets the list of kubernetes clusters managed by the management cluster

    Args:
        jwt (str): JSON Web Token
        vco_id (str): Virtual Cloud Operator id
        management_cluster_id (str): Management cluster id
        customer_id (str): Customer id
        kubernetes_cluster_id (str): Kubernetes cluster id

    Returns:
        RancherManagementClusterKubernetesCluster
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    url = f"/v3/clusters/{kubernetes_cluster_id}"
    cluster = _rancher_api_get(cluster, url)
    return RancherManagementClusterKubernetesClusterStruct(
        cluster["id"],
        cluster["name"],
        cluster["state"],
        cluster["nodeCount"],
        cluster["provider"],
        cluster["version"]["gitVersion"] if cluster.get("version") else "Not available",
    )


def create_kubernetes_cluster(
    jwt: str,
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    settings: CreateRancherManagementClusterKubernetesClusterStruct,
) -> RancherManagementClusterKubernetesClusterStruct:
    """Starts the deployment of a new kubernetes cluster

    Args:
        jwt (str): JSON Web Token
        vco_id (str): Virtual Cloud Operator id
        customer_id (str): Customer id
        management_cluster_id (str): Rancher management cluster id
        input (CreateRancherManagementClusterKubernetesCluster): Kubernetes cluster creation details

    Returns:
        RancherManagementClusterKubernetesCluster
    """
    mgmt_cluster = _check_mgmt_cluster(customer_id, management_cluster_id)
    if mgmt_cluster.status not in ClusterStatus.get_running_statuses():
        raise ValueError(
            f"Cluster {management_cluster_id} is not running yet, current status is: {mgmt_cluster.status.value}"
        )
    cloudspace_ids = set(np.cloudspace_id for np in settings.node_pools)
    cloudspace_ids.add(mgmt_cluster.cloudspace_id)
    if not _validate_clousdpaces_connection(jwt, customer_id, cloudspace_ids):
        raise ValueError("All cloudspaces in node pool and management cluster cloudspace must be connected!")
    if mgmt_cluster.vco_id != vco_id:
        raise KeyError("Cluster not found")
    if not KUB_CLUSTER_NAME.match(settings.name):
        raise ValueError("The cluster name can only contain lowercase, digits and hyphens!")
    for version in list_supported_kubernetes_versions(vco_id, customer_id, management_cluster_id):
        if version.version == version.version:
            break
    else:
        raise ValueError(f"{version.version} is not a supported kubernetes version.")
    master_nodes = 0
    node_pool_names = set()
    for node_pool in settings.node_pools or []:
        if node_pool.name in node_pool_names:
            raise ValueError("Node pool names must be unique!")
        node_pool_names.add(node_pool.name)
        location, g8_cloudspace_id = decode_validate_cloudspace_id(node_pool.cloudspace_id)
        validate_storage_location(location, StorageOnlyResources.KUBERNETES_CLUSTER)
        try:
            cloudspace = G8Client(location, jwt).get_cloudspace_info(g8_cloudspace_id)
            if cloudspace["cloudspace_mode"] == "private":
                raise ValueError("Cannot create Management Cluster using private cloudspace")
        except HTTPError as ex:
            if ex.response.status_code == 404:
                raise KeyError(f"Cloudspace with id {node_pool.cloudspace_id} does not exist") from ex
            else:
                raise ex

        if node_pool.master:
            master_nodes += node_pool.node_count
        else:
            if node_pool.physical_storage:
                raise ValueError("Can only use physical storage for master node pools")
        if not KUB_NODE_POOL_NAME.match(node_pool.name):
            raise ValueError("The node pool name can only contain lowercase, digits and hyphens!")
        if len(node_pool.name) > 44:
            raise ValueError("The node pool name can have a max length of 44")
        if node_pool.cpus < 1:
            raise ValueError("Need at least 1 VCPU in a node!")
        if not node_pool.memory or node_pool.memory % 128 != 0:
            raise ValueError("Invalid amount of memory. It should be a multiple of 128!")
        if node_pool.node_count < 1:
            raise ValueError("Need at least 1 node in a node pool!")
    if master_nodes % 2 != 1:
        raise ValueError("The amount of master nodes should be uneven")
    vco = VCO.get_by_id(vco_id)
    machine_pools = []
    for node_pool in settings.node_pools:
        machine_pools.append(_create_rke2_machine_pool(customer_id, settings, mgmt_cluster, node_pool, vco))

    cloud_credential = _get_or_create_cloud_credential(mgmt_cluster, vco)

    _create_rke2_cluster(settings, mgmt_cluster, machine_pools, cloud_credential, vco)

    url = "/v3/clusters?internal=false"
    for _ in range(10):
        for cluster in _rancher_api_get(mgmt_cluster, url)["data"]:
            if cluster["name"] == settings.name:
                return RancherManagementClusterKubernetesClusterStruct(
                    cluster["id"],
                    cluster["name"],
                    cluster["state"],
                    cluster["nodeCount"],
                    cluster.get("provider"),
                    cluster["version"]["gitVersion"] if cluster.get("version") else "Not available",
                )
        sleep(1)
    raise RuntimeError("Could not find the cluster id of the deployed kub cluster")


def delete_kubernetes_cluster(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str
) -> None:
    """Delete a kubernetes cluster

    Args:
        jwt: JSON Web Token
        vco_id (str): Virtual Cloud Operator id
        customer_id (str): Customer id
        management_cluster_id (str): Rancher management cluster id
        kubernetes_cluster_id (str): Kubernetes cluster id
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    _rancher_api_delete(cluster, f"/v3/clusters/{kubernetes_cluster_id}")
    db_cluster: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
    if db_cluster:
        lbs: LoadBalancerService
        for lbs in db_cluster.load_balancer_services:
            _delete_ingress_resources(customer_id, jwt, lbs)


def get_rancher_management_cluster_dangling_nodes(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str
) -> List[SimpleKubernetesClusterNodeStruct]:
    """Vacuums a rancher management cluster for nodes that can be deleted

    Args:
        jwt (str): JWT
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID

    Raises:
        KeyError: Cluster not found
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    rancher_registered_hosts = set()
    url = "/v3/clusters"
    nodes = []
    for kub_cluster in _rancher_api_get(cluster, url)["data"]:
        url = urlparse(kub_cluster["links"]["nodes"])
        for kub_node in _rancher_api_get(cluster, url.path)["data"]:
            rancher_registered_hosts.add(kub_node.get("hostname", kub_node["requestedHostname"]))
    vco_registered_hosts: Dict[str, ClusterNode] = dict()
    node: ClusterNode = None
    for node in cluster.cluster_nodes:
        vco_registered_hosts[node.name.rsplit("-", 2)[0]] = node
    g8_clients: Dict[str, G8Client] = {}
    for host_name in set(vco_registered_hosts.keys()).difference(rancher_registered_hosts):
        node = vco_registered_hosts[host_name]
        location, cs_id = decode_validate_cloudspace_id(node.cloudspace_id)
        if location in g8_clients:
            g8_client = g8_clients[location]
        else:
            g8_client = G8Client(location, jwt)
            g8_clients[location] = g8_client
        if node.vm_id == -1:
            # Check for nodes that are falsely cleaned up during creation
            vms = g8_client.list_vms(cs_id)
            for vm in vms:
                if vm["name"] == node.name:
                    node.vm_id = vm["vm_id"]
        try:
            g8_client.get_vm_info(node.vm_id)
        except HTTPError as error:
            if error.response.status_code == 404:
                cluster.update(pull__cluster_nodes=node)
                continue
            raise error
        nodes.append(node)
    cluster.save()
    return nodes


def delete_dangling_nodes(
    jwt: str,
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    selected_nodes: List[SimpleKubernetesClusterNodeStruct],
) -> None:
    """Delete a rancher management cluster nodes that can be deleted

    Args:
        jwt (str): JWT
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID
        nodes (SimpleKubernetesClusterNodeListStruct)
    Raises:
        KeyError: Cluster not found
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    valid_nodes = get_rancher_management_cluster_dangling_nodes(jwt, vco_id, customer_id, management_cluster_id)
    g8_clients: Dict[str, G8Client] = {}
    for node in valid_nodes:
        location, decoded_cloudspace_id = decode_validate_cloudspace_id(node.cloudspace_id)
        if location in g8_clients:
            g8_client = g8_clients[location]
        else:
            g8_client = G8Client(location, jwt)
            g8_clients[location] = g8_client
        if not any(node.vm_id == selected_node.vm_id for selected_node in selected_nodes):
            continue
        vm_info = g8_client.get_vm_info(node.vm_id)
        if vm_info["status"] in ["RUNNING", "HALTED"]:
            _delete_vm(jwt, customer_id, location, decoded_cloudspace_id, node.vm_id)
        cluster.update(pull__cluster_nodes=node)


def list_node_pools(
    vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str
) -> List[RancherManagementClusterKubernetesClusterNodePoolStruct]:
    """Lists a kubernetes cluster node pool

    Args:
        vco_id (str): Virtual cloud operator id
        customer_id (str): Customer id
        management_cluster_id (str): Rancher management cluster id
        kubernetes_cluster_id (str): Kubernetes cluster id

    Raises:
        NotImplementedError: Raised when trying to list node pools on a cluster provider which is not supported (yet)

    Returns:
        List[RancherManagementClusterKubernetesClusterNodePool]
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    provider = kluster["provider"]
    result: List[RancherManagementClusterKubernetesClusterNodePoolStruct] = []
    if provider == "rke":
        node_pool_result = _get_nodepools_rke1(kubernetes_cluster_id, cluster)
        for node_pool_data in node_pool_result:
            roles: List[NodePoolRole] = []
            if node_pool_data["controlPlane"]:
                roles.append(RancherManagementClusterKubernetesClusterNodePoolRoleStruct(NodePoolRole.CONTROL_PLANE))
            if node_pool_data["worker"]:
                roles.append(RancherManagementClusterKubernetesClusterNodePoolRoleStruct(NodePoolRole.WORKER))
            result.append(
                RancherManagementClusterKubernetesClusterNodePoolStruct(
                    node_pool_data["id"],
                    node_pool_data["driver"],
                    node_pool_data["name"],
                    node_pool_data["state"],
                    node_pool_data["quantity"],
                    node_pool_data["hostnamePrefix"],
                    roles,
                    None,
                )
            )
    elif provider == "rke2":
        url = f"/v1/provisioning.cattle.io.clusters/fleet-default/{kluster['name']}"
        kluster_v1 = _rancher_api_get(cluster, url)
        kluster_name = kluster["name"]
        machine_sets = _get_machine_sets(cluster, kluster_name)
        for machine_pool in kluster_v1["spec"]["rkeConfig"]["machinePools"]:
            roles: List[NodePoolRole] = []
            if machine_pool.get("controlPlaneRole"):
                roles.append(RancherManagementClusterKubernetesClusterNodePoolRoleStruct(NodePoolRole.CONTROL_PLANE))
            if machine_pool.get("workerRole"):
                roles.append(RancherManagementClusterKubernetesClusterNodePoolRoleStruct(NodePoolRole.WORKER))
            result.append(
                RancherManagementClusterKubernetesClusterNodePoolStruct(
                    machine_pool["machineConfigRef"]["name"],
                    machine_pool["machineConfigRef"]["kind"][:-6].lower(),
                    machine_pool["name"],
                    machine_sets[machine_pool["name"]]["status"],
                    machine_pool["quantity"],
                    machine_sets[machine_pool["name"]]["hostname_prefix"],
                    roles,
                    None,
                )
            )
    else:
        raise NotImplementedError()
    db_cluster: KubernetesCluster = cluster.kubernetes_clusters.get(kluster["id"])
    db_node_pool: NodePool = None
    if db_cluster is not None:
        for node_pool in result:
            for db_node_pool in db_cluster.node_pools:
                if db_node_pool.node_pool_id == node_pool.id:
                    node_pool.cloudspace_id = db_node_pool.cloudspace_id
                    break
    return result


def get_node_pool(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str, node_pool_id: str
) -> RancherManagementClusterKubernetesClusterNodePoolStruct:
    """Get node pool

    Args:
        jwt (str): JWT
        vco_id (str): Virtual Cloud Operator id
        customer_id (str): Customer id
        management_cluster_id (str): Rancher Management Cluster id
        kubernetes_cluster_id (str): Kubernetes Cluster id
        node_pool_id (str): Node pool id

    Returns:
        RancherManagementClusterKubernetesClusterNodePoolStruct
    """
    node_pool: RancherManagementClusterKubernetesClusterNodePoolStruct = None
    for node_pool in list_node_pools(vco_id, customer_id, management_cluster_id, kubernetes_cluster_id):
        if node_pool.id == node_pool_id:
            break
    else:
        raise KeyError("Node pool not found!")
    if not node_pool.cloudspace_id:
        for node in list_node_pool_nodes(
            jwt, vco_id, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool.id
        ):
            if node.cloudspace_id:
                node_pool.cloudspace_id = node.cloudspace_id
                break
    classes_url = f"/k8s/clusters/{kubernetes_cluster_id}/v1/networking.k8s.io.ingressclasses"
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    ing_classes = _rancher_api_get(cluster, classes_url)["data"]
    matching_nodes = [ing["metadata"]["name"] for ing in ing_classes if ing.get("id") == f"{node_pool.name}-pool"]
    ingress_class_name = matching_nodes[0] if matching_nodes else None
    services_url = f"/k8s/clusters/{kubernetes_cluster_id}/v1/services"
    services = _rancher_api_get(cluster, services_url)["data"]
    matching_services = [pod for pod in services if pod["metadata"]["name"] == f"{node_pool.name}-ingrctrl"]
    ingress_controller_service = matching_services[0] if matching_services else None

    if not ingress_controller_service:
        return node_pool

    node_pool.ingress_ctrl_status = ingress_controller_service["metadata"]["state"]["name"]
    node_pool.loadbalancer_ip = (
        ingress_controller_service.get("status", {})
        .get("loadBalancer", {})
        .get("ingress", [{"ip": "<pending>"}])[0]["ip"]
    )
    node_pool.ingress_class = ingress_class_name
    node_pool.ssl_issuer = f"{_get_node_pool_ingress_app_name(node_pool.name)}-cert-issuer"
    return node_pool


def install_ingress(
    jwt: str,
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    node_pool_ids: List[str],
) -> None:
    """Installs an ingress controller for a specific node pool

    Args:
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID
        node_pool_ids (List[str]): Node Pool ID
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    vco = VCO.get_by_id(vco_id)
    node_pool_names = _get_node_pool_names(
        jwt, vco_id, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_ids
    )
    app_name = _get_node_pool_ingress_app_name(node_pool_names)
    cloudspace_id = None
    if len(node_pool_ids) == 1:
        kub = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
        if kub:
            node_pool: NodePool = None
            for node_pool in kub.node_pools:
                if node_pool.node_pool_id == node_pool_ids[0]:
                    cloudspace_id = node_pool.cloudspace_id
                    break
    namespace = _get_system_namespace(vco)
    logger.info("Installing ingress controller")
    with open(INGRESS_VALUES_PATH, "r", encoding="utf-8") as file_handler:
        template = Template(file_handler.read())
    ingress_class_name = f"{node_pool_names}-pool"
    values = yaml.safe_load(
        template.render(
            NODE_POOL_IDS=",".join(node_pool_ids),
            APP_NAME=app_name,
            INGRESS_NAME=ingress_class_name,
            CLOUDSPACE_ID=cloudspace_id,
        )
    )
    # pylint: disable=expression-not-assigned
    _install_helm_package(
        jwt,
        management_cluster_id,
        kubernetes_cluster_id,
        namespace,
        app_name,
        INGRESS_CONTROLLER_CHART,
        values,
        KUBERNETES_CHARTS,
    ).get_result(timeout=7200).result_value
    logger.info("Installing cert manager")
    with open(CERT_MGR_VALUES_PATH, "r", encoding="utf-8") as file_handler:
        template = Template(file_handler.read())
    values = yaml.safe_load(template.render())
    with open(CERT_ISSUER_PATH, "r", encoding="utf-8") as file_handler:
        template = Template(file_handler.read())
    extra_yaml = template.render(
        NAME=f"{app_name}-cert-issuer", EMAIL=cluster.letsencrypt_emailaddress, INGRESS_NAME=ingress_class_name
    )
    _install_helm_package(
        jwt,
        management_cluster_id,
        kubernetes_cluster_id,
        namespace,
        "cert-manager",
        CERT_MANAGER_CHART,
        values,
        JETSTACK_CHARTS,
        extra_yaml=extra_yaml,
        version=CERT_MANAGER_VERSION,
    ).get_result(timeout=7200).result_value
    if len(node_pool_ids) == 1:
        kub: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
        if kub is not None:
            db_node_pool: NodePool = None
            for db_node_pool in kub.node_pools:
                if db_node_pool.node_pool_id == node_pool_ids[0]:
                    db_node_pool.ingress_controller_installed = True
                    cluster.save()
                    break


def scale_node_pool(
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    node_pool_id: str,
    new_size: int,
) -> None:
    """Scale node pool

    Args:
        vco_id (str): Virtual Cloud Operator id
        customer_id (str): Customer id
        management_cluster_id (str): Rancher management cluster id
        kubernetes_cluster_id (str): Kubernetes cluster id
        node_pool_id (str): Node pool id
        new_size (int): Desired number of nodes
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    provider = kluster["provider"]

    if provider == "rke":
        url = f"/v3/nodePools/{node_pool_id}"
        node_pool = _rancher_api_get(cluster, url)
        node_pool["quantity"] = new_size
        _rancher_api_put(cluster, url, node_pool)
    elif provider == "rke2":
        url = f"/v1/provisioning.cattle.io.clusters/fleet-default/{kluster['name']}"
        kluster_v1 = _rancher_api_get(cluster, url)
        for machine_pool in kluster_v1["spec"]["rkeConfig"]["machinePools"]:
            if machine_pool["machineConfigRef"]["name"] != node_pool_id:
                continue
            machine_pool["quantity"] = new_size
            break
        else:
            raise KeyError("Node pool not found!")
        _rancher_api_put(cluster, url, kluster_v1)
    else:
        raise NotImplementedError()


def add_node_pool(
    jwt: str,
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    node_pool_definition: RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct,
) -> RancherManagementClusterKubernetesClusterNodePoolStruct:
    """Adds a node pool to a kubernetes cluster

    Args:
        jwt (str): JWT
        vco_id (str): Virtual Cloud Operator id
        customer_id (str): Customer id
        management_cluster_id (str): Rancher Management Cluster id
        kubernetes_cluster_id (str): Kubernetes cluster id
        node_pool_definition (RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct): Node pool details

    Returns:
        RancherManagementClusterKubernetesClusterNodePoolStruct
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    if node_pool_definition.physical_storage and not node_pool_definition.master:
        raise ValueError("Only master node pool can have physical storage!")
    if not _validate_clousdpaces_connection(
        jwt, customer_id, [node_pool_definition.cloudspace_id, cluster.cloudspace_id]
    ):
        raise ValueError("All cloudspaces in node pool and management cluster cloudspace must be connected!")
    location, _ = decode_validate_cloudspace_id(node_pool_definition.cloudspace_id)
    validate_storage_location(location, StorageOnlyResources.NODE_POOL)
    if not KUB_CLUSTER_NAME.match(node_pool_definition.name):
        raise ValueError("The node pool name can only contain lowercase, digits and hyphens!")

    if kluster["provider"] != "rke2":
        raise NotImplementedError("Adding node pools is not supported for this type of kubernetes cluster.")
    if len(node_pool_definition.name) > 44:
        raise ValueError("The node pool name can have a max length of 44")
    node_pools = list_node_pools(vco_id, customer_id, management_cluster_id, kubernetes_cluster_id)
    if any(
        re.sub("[^a-z]", "", node_pool.name.lower()) == re.sub("[^a-z]", "", node_pool_definition.name.lower())
        for node_pool in node_pools
    ):
        raise ValueError(f"Node pool with name {node_pool_definition.name} already exists")
    settings = CreateRancherManagementClusterKubernetesClusterStruct(
        kluster["name"],
        [node_pool_definition],
        RancherSupportedKubernetesClusterVersionStruct(
            kluster["version"]["gitVersion"] if kluster.get("version") else "Not available"
        ),
    )
    machine_pool = _create_rke2_machine_pool(
        customer_id, settings, cluster, node_pool_definition, VCO.get_by_id(vco_id)
    )
    if (
        node_pool_definition.master
        and (
            sum(
                node_pool.node_count
                for node_pool in node_pools
                if any(role.role == NodePoolRole.CONTROL_PLANE for role in node_pool.roles)
            )
            + node_pool_definition.node_count
        )
        % 2
        == 0
    ):
        raise ValueError("The sum of nodes for all master node pools should be odd")

    kluster_v1_url = f"/v1/provisioning.cattle.io.clusters/fleet-default/{kluster['name']}"
    kluster_v1 = _rancher_api_get(cluster, kluster_v1_url)
    kluster_v1["spec"]["rkeConfig"]["machinePools"].append(machine_pool)
    _rancher_api_put(cluster, kluster_v1_url, kluster_v1)
    exception = None
    for _ in range(12):
        try:
            return get_node_pool(
                jwt,
                vco_id,
                customer_id,
                management_cluster_id,
                kubernetes_cluster_id,
                machine_pool["machineConfigRef"]["name"],
            )
        except KeyError as error:
            sleep(5)
            exception = error
    raise exception


def delete_node_pool(
    vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str, node_pool_id: str
) -> None:
    """Deletes a node pool

    Args:
        jwt (str): JSON Web Token
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID
        node_pool_id (str): Nodepool ID
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    kluster_v1_url = f"/v1/provisioning.cattle.io.clusters/fleet-default/{kluster['name']}"
    kluster_v1 = _rancher_api_get(cluster, kluster_v1_url)
    machine_pools = kluster_v1["spec"]["rkeConfig"]["machinePools"]
    for machine_pool in machine_pools:
        if machine_pool["machineConfigRef"]["name"] == node_pool_id:
            break
    else:
        raise KeyError("Node pool not found")
    machine_pools.remove(machine_pool)
    _rancher_api_put(cluster, kluster_v1_url, kluster_v1)
    vco = VCO.get_by_id(vco_id)
    url = f"/v1/rke-machine-config.cattle.io.{_get_node_driver_name(vco)}configs/fleet-default/{node_pool_id}"
    _rancher_api_delete(cluster, url)


def list_node_pool_nodes(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str, node_pool_id: str
) -> List[RancherManagementClusterKubernetesClusterNodePoolNodeStruct]:
    """Lists the nodes that are part of a node pool

    Args:
        jwt (str): JWT
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer
        management_cluster_id (str): Rancher Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID
        node_pool_id (str): Node pool ID

    Raises:
        NotImplementedError: Raised when listing node pools in a non RKE2 cluster
        KeyError: Raised when something is not found

    Returns:
        List[RancherManagementClusterKubernetesClusterNodePoolNodeStruct]
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    node_pool: RancherManagementClusterKubernetesClusterNodePoolStruct = None
    for node_pool in list_node_pools(vco_id, customer_id, management_cluster_id, kubernetes_cluster_id):
        if node_pool.id == node_pool_id:
            break
    else:
        raise KeyError("Node pool not found!")
    machines_v1_url = "/v1/cluster.x-k8s.io.machines"
    machines = _rancher_api_get(cluster, machines_v1_url)["data"]

    machines = [
        machine
        for machine in machines
        if machine["spec"]["clusterName"] == kluster["name"]
        and machine["metadata"]["labels"]["rke.cattle.io/rke-machine-pool-name"] == node_pool.name
    ]
    result: List[RancherManagementClusterKubernetesClusterNodePoolNodeStruct] = []
    for machine in machines:
        if (
            machine["spec"]["clusterName"] != kluster["name"]
            or machine["metadata"]["labels"]["rke.cattle.io/rke-machine-pool-name"] != node_pool.name
        ):
            continue
        name = machine["spec"]["infrastructureRef"]["name"]
        for node in cluster.cluster_nodes:
            if node.name.startswith(name):
                vm_id = node.vm_id
                cloudspace_id = node.cloudspace_id
                break
        else:
            vm_id = None
            cloudspace_id = None
        result.append(
            RancherManagementClusterKubernetesClusterNodePoolNodeStruct(
                machine["id"], name, machine["status"]["phase"], vm_id, None, cloudspace_id, None
            )
        )
    if cloudspace_id is not None:
        g8_name, cloudspace_id = decode_validate_cloudspace_id(cloudspace_id)
        g8_client = G8Client(g8_name, jwt)
        cloud_space = g8_client.get_cloudspace_info(cloudspace_id)
        vms = g8_client.list_vms(cloudspace_id)
        for node in result:
            node.cloudspace_name = cloud_space["name"]
            for vm in vms:
                if vm["vm_id"] == node.vm_id:
                    node.vm_name = vm["name"]
                    break
    return result


def install_integration_components(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str
) -> None:
    """Install the csi driver

    Args:
        jwt (str): JSON Web Token
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID

    Raises:
        ValueError: Raised when the CSI driver is not supported for this kubernetes cluster
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    vco = VCO.get_by_id(vco_id)
    node_driver_name = _get_node_driver_name(vco)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    provider = kluster["provider"]
    if provider == "rke":
        for node_pool in _get_nodepools_rke1(kubernetes_cluster_id, cluster):
            if node_pool["driver"] == node_driver_name:
                break
        else:
            raise ValueError("Not supported for this kubernetes cluster")
    elif provider == "rke2":
        rke2_machine_template_name = f"{node_driver_name}machinetemplate"
        for machine_set in _rancher_api_get(cluster, "/v1/cluster.x-k8s.io.machinesets")["data"]:
            if machine_set["spec"]["clusterName"] != kluster["name"]:
                continue
            if (
                machine_set["spec"]["template"]["spec"]["infrastructureRef"]["kind"].lower()
                == rke2_machine_template_name
            ):
                break
        else:
            raise ValueError("Not supported for this kubernetes cluster")
    else:
        raise ValueError("Not supported for this kubernetes cluster")
    with open(CSI_PATH, "r", encoding="utf8") as file:
        template = Template(file.read())
    provisioner_csi = ".".join(vco_id.split(".")[-1:0:-1])
    dev_env_ip_address = os.environ.get("DEV_ENV_VPN_IP_ADDRESS")
    csi_resources = template.render(
        PROVIDER_ID=_get_provider_name(vco),
        CLUSTER_ID=kubernetes_cluster_id,
        CUSTOMER_ID=base64.b64encode(customer_id.encode()).decode(),
        JWT=base64.b64encode(jwt.encode()).decode(),
        PROVISIONER=provisioner_csi,
        VCO_DOMAIN=vco.domain,
        IS_DEV=EnvironmentName.current() == EnvironmentName.DEV,
        DEV_ENV_VPN_IP_ADDRESS=dev_env_ip_address,
    )
    project_id = _get_or_create_system_project_id(cluster, kubernetes_cluster_id)
    for resource in yaml.load_all(csi_resources, Loader=yaml.SafeLoader):
        yml = yaml.dump(resource, default_flow_style=False)
        logger.info("Importing:\n%s", yml)
        _rancher_api_post(
            cluster,
            f"/v3/clusters/{kubernetes_cluster_id}?action=importYaml",
            {
                "defaultNamespace": None,
                "namespace": None,
                "projectId": project_id if resource["kind"] == "Namespace" else None,
                "yaml": yml,
            },
        )


def download_cluster_kubeconfig(
    vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str
) -> str:
    """Get kubernetes cluster kubeconfig

    Args:
        vco_id (str): Virtual cloud operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher management cluster ID
        kubernetes_cluster_id (str): Kubernetes cluster ID

    Returns:
        str
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    result = _rancher_api_post(cluster, f"/v3/clusters/{kubernetes_cluster_id}?action=generateKubeconfig", None)
    return result["config"]


def list_nodes(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str
) -> List[RancherManagementClusterNodeStruct]:
    """List kubernetes nodes provisioned by the VCO node driver

    Args:
        jwt (str): JWT
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID

    Returns:
        List[RancherManagementClusterNodeStruct]
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    provider_name = _get_provider_name(VCO.get_by_id(vco_id))
    kube_node_map = {}
    for kube_node in _rancher_api_get(cluster, "/v3/nodes")["data"]:
        cloudspace_id = kube_node["labels"].get(f"{provider_name}/cloudspace-id")
        vm_id = kube_node["labels"].get(f"{provider_name}/vm-id")
        if None in (cloudspace_id, vm_id):
            continue
        kube_node_map[f"{cloudspace_id}:{vm_id}"] = kube_node
    result: List[RancherManagementClusterNodeStruct] = []
    node: ClusterNode = None
    cloudspace_ids: Set[str] = set()
    for node in cluster.cluster_nodes:
        cloudspace_ids.add(node.cloudspace_id)
        kube_node = kube_node_map.get(f"{node.cloudspace_id}:{node.vm_id}")
        node_status_struct = (
            from_dict_with_enum(RancherManagementClusterNodeStatusStruct, node.status.to_mongo())
            if node.status is not None
            else None
        )
        result.append(
            RancherManagementClusterNodeStruct(
                node.cloudspace_id,
                None,
                node.vm_id,
                node.name,
                node_status_struct,
                kube_node["clusterId"] if kube_node else None,
                kube_node["annotations"]["cluster.x-k8s.io/cluster-name"] if kube_node else None,
                kube_node["hostname"] if kube_node else None,
            )
        )
    node: RancherManagementClusterNodeStruct = None
    g8_clients = {}
    for cloudspace_id in cloudspace_ids:
        g8_name, cloudspace_int_id = decode_validate_cloudspace_id(cloudspace_id)
        if g8_name in g8_clients:
            g8_client = g8_clients[g8_name]
        else:
            g8_client = G8Client(g8_name, jwt)
            g8_clients[g8_name] = g8_client
        cloudspace = g8_client.get_cloudspace_info(cloudspace_int_id)
        for node in result:
            if node.cloudspace_id == cloudspace_id:
                node.cloudspace_name = cloudspace["name"]
    return result


def list_supported_kubernetes_versions(
    vco_id: str, customer_id: str, management_cluster_id: str
) -> List[RancherSupportedKubernetesClusterVersionStruct]:
    """List available kubernetes versions for creating and upgrading clusters

    Args:
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher management cluster ID

    Returns:
        List[RancherSupportedKubernetesClusterVersion]
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    versions = [version["id"] for version in _rancher_api_get(cluster, "/v1-rke2-release/releases")["data"]]
    settings = RancherAsAServiceSettings.get_settings()
    minimum_version = packaging_version.Version(settings.minimum_kubernetes_version)

    major_minor_set = set()
    for version in versions:
        major_minor = ".".join(version.split(".")[:2])
        major_minor_set.add(major_minor)

    major_minor_versions = [
        packaging_version.Version(v) for v in major_minor_set if packaging_version.Version(v) >= minimum_version
    ]

    sorted_major_minor_versions = sorted(major_minor_versions, reverse=True)

    result = []
    for major_minor_version in sorted_major_minor_versions:
        highest_micro = 0
        for version in versions:
            if version.startswith(f"v{major_minor_version.base_version}"):
                version_obj = packaging_version.Version(version)
                if version_obj.micro > highest_micro:
                    highest_version = version
        result.append(RancherSupportedKubernetesClusterVersionStruct(highest_version))

    return result


def upgrade_kubernetes_cluster(
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    version: RancherSupportedKubernetesClusterVersionStruct,
) -> None:
    """Upgrade a kubernetes cluster to a higher version

    Args:
        vco_id (str): Virtual cloud operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher management cluster ID
        kubernetes_cluster_id (str): Kubernetes cluster ID
        version: (RancherSupportedKubernetesClusterVersionStruct): Kubernetes version to upgrade to
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    kluster = _get_cluster(kubernetes_cluster_id, cluster)
    kluster_name = kluster["name"]
    _validate_cluster_nodes_health(cluster)
    _validate_k8_cluster_nodes_health(cluster, kubernetes_cluster_id)
    _pods_are_running(cluster, "deployment", "cattle", "cattle-system")
    resource_url_path = f"/v1/provisioning.cattle.io.clusters/fleet-default/{kluster_name}"
    fleet_spec = _rancher_api_get(cluster, resource_url_path)
    current_version = fleet_spec["spec"]["kubernetesVersion"]
    target_version = version.version
    available_versions = list_supported_kubernetes_versions(vco_id, customer_id, management_cluster_id)
    if packaging_version.parse(target_version) not in [
        packaging_version.parse(av_version.version) for av_version in available_versions
    ]:
        raise ValueError(f"Target version {target_version} is not in cluster available versions!")
    if packaging_version.parse(current_version) > packaging_version.parse(target_version):
        raise ValueError("Can only upgrade to higher versions of kubernetes. Upgrade request aborted.")
    fleet_spec["spec"]["kubernetesVersion"] = version.version
    _rancher_api_put(cluster, resource_url_path, fleet_spec)


def report_ccm_error(error_message: str, management_cluster_id: str, cluster_id: str, customer_id: str, vco_id: str):
    """Report errors coming from CCM

    Args:
        error_message (str): Error message
        management_cluster_id (str): Management Cluster ID
        cluster_id (str): Kubernetes cluster ID
        customer_id (str): Customer ID
        vco_id (str): VCO ID
    """
    _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    with open(CCM_ERROR_PATH, "r", encoding="utf-8") as fh:
        template = Template(fh.read())
    message = template.render(
        vco_id=vco_id,
        customer_id=customer_id,
        cluster_id=cluster_id,
        management_cluster_id=management_cluster_id,
        error_message=error_message,
    )
    mgmt_cluster = RancherManagementCluster.get_by_id(management_cluster_id, True)
    if message is not None:
        metadata = dict(
            customer_id=customer_id,
            vco_id=vco_id,
            management_cluster_id=management_cluster_id,
            kubernetes_cluster_id=cluster_id,
            cloudspace_id=mgmt_cluster.cloudspace_id,
        )
        file_issue(error_message, IssueResourceTypes.RANCHER_K8S, metadata, vco_id, customer_id)
        rocket_chat_message("kaas-errors", message)


def get_rancher_api_requests(vco_id: str, customer_id: str, management_cluster_id: str) -> List[RancherBridgeRequest]:
    """Gets queued rancher api requests

    Args:
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Management Cluster ID

    Returns:
        List[RancherBridgeRequest]
    """
    _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    queue_name = _get_rancher_request_queue(vco_id, customer_id, management_cluster_id)
    redis: Redis = RedisConnection.get_client()
    result = []
    items = redis.blpop([queue_name], 30)
    if items:
        result.append(pickle.loads(items[1]))
        extra = redis.lpop(queue_name)
        while extra:
            result.append(pickle.loads(extra))
            extra = redis.lpop(queue_name)
    return result


def queue_rancher_api_result(
    vco_id: str, customer_id: str, management_cluster_id: str, response: RancherBridgeResponse
) -> None:
    """Queue the result to the listener queue

    Args:
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        management_cluster_id (str): Rancher Management Cluster ID
        response (RancherBridgeResponse): Response received from the Rancher API server
    """
    _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    queue_name = _get_rancher_response_queue(vco_id, customer_id, management_cluster_id, response.id)
    redis: Redis = RedisConnection.get_client()
    pipeline = redis.pipeline()
    pipeline.rpush(queue_name, pickle.dumps(response))
    pipeline.expire(queue_name, 60)
    pipeline.execute()


def _get_rancher_response_queue(vco_id, customer_id, management_cluster_id, request_id):
    return f"rancher-api-bridge-response-{vco_id}-{customer_id}-{management_cluster_id}-{request_id}"


def _get_rancher_request_queue(vco_id, customer_id, management_cluster_id):
    return f"rancher-api-bridge-requests-{vco_id}-{customer_id}-{management_cluster_id}"


def _get_node_pool_ingress_app_name(node_pool_names: str) -> str:
    # Enforce max length of 44
    # see https://github.com/kubernetes/ingress-nginx/issues/7442#issuecomment-917402076
    return node_pool_names[:44]


def _get_node_pool_names(
    jwt: str,
    vco_id: str,
    customer_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    node_pool_ids: List[str],
) -> str:
    node_pool_names = []
    for node_pool_id in node_pool_ids:
        node_pool = get_node_pool(jwt, vco_id, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id)
        node_pool_names.append(node_pool.name)
    node_pool_name = "+".join(node_pool_names)
    return node_pool_name


def _get_system_namespace(vco: VCO) -> str:
    return f"{_get_provider_name(vco)}-system"


def _delete_vm(jwt: str, customer_id: str, location: str, cloudspace_id: int, vm_id: int) -> None:
    g8_client = G8Client(location, jwt)
    vm = g8_client.get_vm_info(vm_id)
    for disk in vm["disks"]:
        if disk["disk_type"] == "B":
            continue
        if disk["disk_type"] == "D" and disk["disk_name"] == "Disk nr 1":
            continue
        if disk["disk_type"] == "P" and disk["disk_name"] == "cluster-physical-node":
            continue
        g8_client.detach_disk_vm(vm_id, disk["disk_id"])
    delete_virtual_machine(jwt, customer_id, location, cloudspace_id, vm_id, False, force=True)


def _get_machine_sets(cluster: RancherManagementCluster, kluster_name: str) -> Dict:
    machine_sets = dict()
    for machine_set in _rancher_api_get(cluster, "/v1/cluster.x-k8s.io.machinesets")["data"]:
        if machine_set["spec"]["clusterName"] != kluster_name:
            continue
        machine_sets[machine_set["metadata"]["labels"]["rke.cattle.io/rke-machine-pool-name"]] = {
            "id": machine_set["id"],
            "status": machine_set["metadata"]["state"]["name"],
            "hostname_prefix": machine_set["spec"]["template"]["spec"]["infrastructureRef"]["name"],
        }

    return machine_sets


@job(
    (
        "Install cloud integration resources on kubernetes cluster "
        "{kubernetes_cluster_name} ({kubernetes_cluster_id}) @ {vco_id}:{customer_id}"
    ),
    block=False,
    timeout=3600,
    object_type="install_cloud_integration_resources",
    object_id="{rancher_management_cluster_id}:{kubernetes_cluster_id}",
    retry_count=1,
    cleanup=_report_failure,
    is_single=True,
)
def _install_cloud_integration_components(
    jwt: str,
    rancher_management_cluster_id: str,
    kubernetes_cluster_id: str,
    kubernetes_cluster_name: str,
    vco_id: str,
    customer_id: str,
) -> None:
    cluster = RancherManagementCluster.get_by_id(rancher_management_cluster_id)
    while True:
        kluster = _get_cluster(kubernetes_cluster_id, cluster)
        if kluster["state"] == "active":
            break
        logger.info("Cluster %s (%s) is not ready yet. Waiting ...", kubernetes_cluster_name, kubernetes_cluster_id)
        sleep(30)
    install_integration_components(
        jwt, cluster.vco_id, cluster.customer_id, rancher_management_cluster_id, kubernetes_cluster_id
    )
    _install_node_pool_ingress_controller(
        jwt, rancher_management_cluster_id, kubernetes_cluster_id, vco_id, customer_id
    )


@job(
    "Scan for clusters who need integration components {rancher_management_cluster_id}",
    object_type="scan_for_clusters_that_need_integration_components",
    object_id="{rancher_management_cluster_id}",
    timeout=1800,
    cleanup=_report_failure,
    is_single_queued=True,
)
def _scan_for_clusters_that_need_integration_components(jwt: str, rancher_management_cluster_id: str) -> None:
    logger.info("Waiting for 30 seconds before scanning to include any potential newly created kubernetes cluster")
    sleep(30)
    cluster = RancherManagementCluster.get_by_id(rancher_management_cluster_id)
    vco = VCO.get_by_id(cluster.vco_id)
    installs = []
    for kluster in _rancher_api_get(cluster, "/v3/clusters?internal=false")["data"]:
        kluster_id = kluster["id"]
        logger.info(
            "Checking if cluster %s (%s) has all cloud integration components installed ...",
            kluster["name"],
            kluster_id,
        )
        for _ in range(10):
            if kluster["provider"]:
                break
            logger.info('Provider is not known yet. Sleeping 30" ...')
            sleep(30)
            kluster = _get_cluster(kluster_id, cluster)
        else:
            # pylint: disable=raising-format-tuple
            raise ValueError(
                "Provider for %s (%s), management cluster %s (%s) not known after 5 mins, exiting",
                kluster["name"],
                kluster_id,
                cluster.name,
                rancher_management_cluster_id,
            )

        provider = kluster["provider"]
        if provider != "rke2":
            logger.info("Provider %s is not supported. Skipping ...", provider)
            continue

        kub = cluster.kubernetes_clusters.get(kluster_id)
        if kub is None:
            kub = KubernetesCluster()
            kub.kubernetes_cluster_id = kluster_id
            cluster.kubernetes_clusters[kluster_id] = kub
            cluster.save()

        def install_cloud_integration_components(kluster):
            _install_cloud_integration_components(
                jwt, rancher_management_cluster_id, kluster["id"], kluster["name"], cluster.vco_id, cluster.customer_id
            )

        def install_ingress_controller(kluster):
            _install_node_pool_ingress_controller(
                jwt, rancher_management_cluster_id, kluster["id"], cluster.vco_id, cluster.customer_id
            )

        system_project_id = _get_or_create_system_project_id(cluster, kluster["id"], False)
        if system_project_id is None:
            logger.info("No system project in place: triggering installation of cloud integration resources")
            installs.append((install_cloud_integration_components, kluster))
        else:
            config_maps = _rancher_api_get(cluster, f"/v3/projects/{system_project_id}/configmaps")["data"]
            for config_map in config_maps:
                if config_map["name"] == "cluster-csi-info":
                    if config_map["data"]["installed"] == "true":
                        break
            else:
                logger.info("CSI driver is not installed: triggering installation of cloud integration resources")
                installs.append((install_cloud_integration_components, kluster))
        kub_pool: RancherManagementClusterKubernetesClusterNodePoolStruct = None
        trigger_install_ingress_controller = False
        node_pools = list_node_pools(vco.vco_id, cluster.customer_id, rancher_management_cluster_id, kluster_id)
        pool: NodePool = None
        for kub_pool in node_pools:
            for pool in kub.node_pools:
                if pool.node_pool_id == kub_pool.id:
                    if not pool.cloudspace_id and kub_pool.cloudspace_id:
                        pool.cloudspace_id = kub_pool.cloudspace_id
                        cluster.save()
                    break
            else:
                pool = _add_node_pool_db_kluster(
                    jwt, rancher_management_cluster_id, cluster, vco, kluster_id, kub, kub_pool
                )
            if not pool.ingress_controller_installed:
                for role in kub_pool.roles:
                    if role.role == NodePoolRole.WORKER:
                        trigger_install_ingress_controller = True
                        break
        db_node_pool: NodePool = None
        node_pools_to_delete_from_kubernetes_document = []
        for db_node_pool in kub.node_pools:
            for node_pool in node_pools:
                if db_node_pool.node_pool_id == node_pool.id:
                    break
            else:
                node_pools_to_delete_from_kubernetes_document.append(db_node_pool)
        for node_pool in node_pools_to_delete_from_kubernetes_document:
            _remove_node_pool(jwt, cluster, kub, node_pool, vco)
        if trigger_install_ingress_controller:
            installs.append((install_ingress_controller, kluster))
        logger.info("Cluster %s (%s) has all cloud integration components installed", kluster["name"], kluster["id"])
    for install_cloud_integration_components, kluster in installs:
        install_cloud_integration_components(kluster)


def _remove_node_pool(
    jwt: str, cluster: RancherManagementCluster, kub: KubernetesCluster, node_pool: NodePool, vco: VCO
):
    cluster.update(**{f"pull__kubernetes_clusters__{kub.kubernetes_cluster_id}__node_pools": node_pool})
    if node_pool.ingress_controller_installed:
        _uninstall_helm_package(
            jwt,
            cluster.management_cluster_id,
            kub.kubernetes_cluster_id,
            _get_system_namespace(vco),
            _get_node_pool_ingress_app_name(node_pool.name),
            BITNAMI_CHARTS,
        )


def _add_node_pool_db_kluster(
    jwt: str,
    rancher_management_cluster_id: str,
    cluster: RancherManagementCluster,
    vco: VCO,
    kluster_id: str,
    kub: KubernetesCluster,
    kub_pool: RancherManagementClusterKubernetesClusterNodePoolStruct,
) -> NodePool:
    pool = NodePool()
    pool.node_pool_id = kub_pool.id
    pool.name = kub_pool.name
    for node in list_node_pool_nodes(
        jwt, vco.vco_id, cluster.customer_id, rancher_management_cluster_id, kluster_id, kub_pool.id
    ):
        if node.cloudspace_id:
            pool.cloudspace_id = node.cloudspace_id
            break
    cluster.update(**{f"push__kubernetes_clusters__{kub.kubernetes_cluster_id}__node_pools": pool})
    return pool


@job(
    "Install node pool ingress controller on {kubernetes_cluster_id}@{vco_id}:{customer_id}",
    block=False,
    timeout=3600 * 24,
    object_type="install_node_pool_ingress_controller",
    object_id="{rancher_management_cluster_id}:{kubernetes_cluster_id}",
    retry_count=1,
    cleanup=_report_failure,
    is_single=True,
)
def _install_node_pool_ingress_controller(
    jwt: str,
    rancher_management_cluster_id: str,
    kubernetes_cluster_id: str,
    vco_id: str,
    customer_id: str,
) -> None:
    start = time()
    cluster = RancherManagementCluster.get_by_id(rancher_management_cluster_id)
    kub: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
    vco = VCO.get_by_id(vco_id)
    for node_pool in list_node_pools(vco_id, customer_id, rancher_management_cluster_id, kubernetes_cluster_id):
        logger.info("Examining node pool %s (%s)", node_pool.name, node_pool.id)
        # Only install on worker node pools
        for role in node_pool.roles:
            if role.role == NodePoolRole.WORKER:
                break
        else:
            logger.info("Skipping non-worker node pool %s (%s)", node_pool.name, node_pool.id)
            continue
        # Check if the ingress controller for this node pool was already installed
        logger.info("Checking if node pool %s has an ingress controller installed ...", node_pool.name)
        while (node_pool.status != "active" or not node_pool.cloudspace_id) and time() - start < 1800:
            logger.info("Waiting for the node pool %s to become active ...", node_pool.name)
            sleep(30)
            node_pool = get_node_pool(
                jwt, vco_id, customer_id, rancher_management_cluster_id, kubernetes_cluster_id, node_pool.id
            )
        if node_pool.status != "active":
            logger.error("The node pool did not become active within 30 minutes. Skipping ...")
            continue
        for db_node_pool in kub.node_pools:
            if db_node_pool.node_pool_id == node_pool.id:
                break
        else:
            db_node_pool = _add_node_pool_db_kluster(
                jwt, rancher_management_cluster_id, cluster, vco, customer_id, kub, node_pool
            )
        if not db_node_pool.cloudspace_id and node_pool.cloudspace_id:
            db_node_pool.cloudspace_id = node_pool.cloudspace_id
            cluster.save()
        if db_node_pool.ingress_controller_installed:
            logger.info("An ingress controller was installed for this node pool before.")
            continue
        install_ingress(jwt, vco_id, customer_id, rancher_management_cluster_id, kubernetes_cluster_id, [node_pool.id])


@job(
    "Install helm chart {chart}@{repo} with name {app_name} on {rancher_management_cluster_id}/{kubernetes_cluster_id}",
    object_type="rancher_helm_operations",
    object_id="{kubernetes_cluster_id}",
    block=False,
    timeout=3600,
    cleanup=_report_failure,
)
def _install_helm_package(
    jwt: str,
    rancher_management_cluster_id: str,
    kubernetes_cluster_id: str,
    namespace: str,
    app_name: str,
    chart: str,
    values: Dict,
    repo: str,
    extra_yaml: str = None,
    version: str = None,
) -> None:
    cluster = RancherManagementCluster.get_by_id(rancher_management_cluster_id)
    kube_config = download_cluster_kubeconfig(
        cluster.vco_id, cluster.customer_id, rancher_management_cluster_id, kubernetes_cluster_id
    )
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    g8_client = G8Client(location, jwt)
    values_yaml = yaml.dump(values)
    with open(HELM_PATH, "r", encoding="utf-8") as file_handler:
        template = Template(file_handler.read())
    installation_id = str(uuid.uuid4())
    data = template.render(
        ACTION="install",
        INSTALLATION_ID=installation_id,
        KUBECONFIG=kube_config,
        NAMESPACE=namespace,
        VALUES=values_yaml,
        HELM_REPO=repo,
        APP_NAME=app_name,
        CHART=chart,
        EXTRA_YAML=extra_yaml,
        CLUSTER_DOMAIN=cluster.domain_name,
        VERSION=version,
    )
    vm_id = cluster.management_cluster_nodes[0].vm_id
    content = base64.b64encode(data.encode("ascii")).decode()
    script = "/root/install_helm_chart_{{ INSTALLATION_ID }}_schedule.sh"
    g8_client.write_vm_file(vm_id, content, script)
    g8_client.execute_vm_command(vm_id, "/bin/bash", [script])

    # Forward the logs
    try:
        logs = ""
        while True:
            current_logs = base64.b64decode(
                _try_action(
                    12,
                    5,
                    g8_client.read_vm_file,
                    vm_id,
                    2 * 1024 * 1024,
                    f"/root/install_helm_chart_{installation_id}.logs",
                )["result"]
            )
            if logs == current_logs:
                exit_status = base64.b64decode(
                    _try_action(
                        12,
                        5,
                        g8_client.read_vm_file,
                        vm_id,
                        2 * 1024 * 1024,
                        f"/root/install_helm_chart_{installation_id}.result",
                    )["result"]
                ).strip()
                if exit_status:
                    if int(exit_status) == 0:
                        break
                    raise RuntimeError(f"Helm installation failed with {exit_status}")
                else:
                    sleep(5)
            else:
                for line in current_logs[len(logs) :].splitlines():
                    logger.info(line.decode())
                logs = current_logs
                sleep(5)
    finally:
        g8_client.execute_vm_command(vm_id, "rm", ["-f", f"/root/install_helm_chart_{installation_id}*"])


@job(
    (
        "Uninstall helm chart {repo} with name {app_name} "
        "from {rancher_management_cluster_id}/{kubernetes_cluster_id}"
    ),
    object_type="rancher_helm_operations",
    object_id="{kubernetes_cluster_id}",
    block=False,
    cleanup=_report_failure,
)
def _uninstall_helm_package(
    jwt: str,
    rancher_management_cluster_id: str,
    kubernetes_cluster_id: str,
    namespace: str,
    app_name: str,
    repo: str,
) -> None:
    cluster = RancherManagementCluster.get_by_id(rancher_management_cluster_id)
    kube_config = download_cluster_kubeconfig(
        cluster.vco_id, cluster.customer_id, rancher_management_cluster_id, kubernetes_cluster_id
    )
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    g8_client = G8Client(location, jwt)
    with open(HELM_PATH, "r", encoding="utf-8") as file_handler:
        template = Template(file_handler.read())
    installation_id = str(uuid.uuid4())
    data = template.render(
        ACTION="remove",
        INSTALLATION_ID=installation_id,
        KUBECONFIG=kube_config,
        NAMESPACE=namespace,
        HELM_REPO=repo,
        APP_NAME=app_name,
    )
    vm_id = cluster.management_cluster_nodes[0].vm_id
    content = base64.b64encode(data.encode("ascii")).decode()
    script = "/root/install_helm_chart_{{ INSTALLATION_ID }}_schedule.sh"
    g8_client.write_vm_file(vm_id, content, script)
    g8_client.execute_vm_command(vm_id, "/bin/bash", [script])

    # Forward the logs
    try:
        logs = ""
        while True:
            current_logs = base64.b64decode(
                _try_action(
                    12,
                    5,
                    g8_client.read_vm_file,
                    vm_id,
                    2 * 1024 * 1024,
                    f"/root/install_helm_chart_{installation_id}.logs",
                )["result"]
            )
            if logs == current_logs:
                exit_status = base64.b64decode(
                    _try_action(
                        12,
                        5,
                        g8_client.read_vm_file,
                        vm_id,
                        2 * 1024 * 1024,
                        f"/root/install_helm_chart_{installation_id}.result",
                    )["result"]
                ).strip()
                if exit_status:
                    if int(exit_status) == 0:
                        break
                    raise RuntimeError(f"Helm installation failed with {exit_status}")
                else:
                    sleep(5)
            else:
                for line in current_logs[len(logs) :].splitlines():
                    logger.info(line.decode())
                logs = current_logs
                sleep(5)
    finally:
        g8_client.execute_vm_command(vm_id, "rm", ["-f", f"/root/install_helm_chart_{installation_id}*"])


def _get_or_create_system_project_id(
    cluster: RancherManagementCluster, kubernetes_cluster_id: str, create_if_not_exists: bool = True
) -> str:
    project_name = f"{cluster.vco_id}-system"
    projects_uri = "/v3/projects"
    for project in _rancher_api_get(cluster, projects_uri)["data"]:
        if project["name"] == project_name and project["clusterId"] == kubernetes_cluster_id:
            break
    else:
        if not create_if_not_exists:
            return None
        project = _rancher_api_post(
            cluster,
            projects_uri,
            {
                "clusterId": kubernetes_cluster_id,
                "containerDefaultResourceLimit": None,
                "description": "cloud integration resources",
                "enableProjectMonitoring": False,
                "name": project_name,
                "namespaceDefaultResourceQuota": None,
                "namespaceId": "",
                "resourceQuota": None,
            },
        )
    return project["id"]


def _get_cluster(kubernetes_cluster_id: str, cluster: RancherManagementCluster) -> Dict:
    return _rancher_api_get(cluster, f"/v3/clusters/{kubernetes_cluster_id}")


def _get_nodepools_rke1(kubernetes_cluster_id, cluster):
    return _rancher_api_get(cluster, f"/v3/clusters/{kubernetes_cluster_id}/nodepools")["data"]


def _add_node_to_management_cluster(management_cluster_id: str, cloudspace_id: str, vm_id: int, node_name: str) -> None:
    cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    provisioner_status = NodeProvisionerStatus(start=int(time()), status=NodeProvisioningStatus.INPROGRESS.value)
    cluster.update(
        push__cluster_nodes=ClusterNode(
            cloudspace_id=cloudspace_id, vm_id=vm_id, provisioner_status=provisioner_status, name=node_name
        )
    )


def _create_rke2_cluster(
    settings: CreateRancherManagementClusterKubernetesClusterStruct,
    cluster: RancherManagementCluster,
    machine_pools: List[Dict],
    cloud_credential: Dict,
    vco: VCO,
) -> None:
    url = "/v1/provisioning.cattle.io.clusters"
    cluster_data = _apply_template_to_yaml(
        CREATE_RKE2_PATH,
        cluster_name=settings.name,
        cloud_credential_secret_name=cloud_credential["id"],
        IS_DEV=EnvironmentName.current() == EnvironmentName.DEV,
        DEV_ENV_VPN_IP_ADDRESS=os.environ.get("DEV_ENV_VPN_IP_ADDRESS"),
        VCO_DOMAIN=vco.domain,
        IMAGE_TAG="latest",
        PROVIDER_NAME=_get_provider_name(vco),
        version=settings.version,
    )
    cluster_data["spec"]["rkeConfig"]["machinePools"] = machine_pools
    _rancher_api_post(cluster, url, cluster_data)


def _get_provider_name(vco: VCO) -> str:
    if EnvironmentName.current() == EnvironmentName.DEV:
        return "console-cloudbuilders-be"
    return vco.domain.replace(".", "-")


def _get_or_create_cloud_credential(cluster: RancherManagementCluster, vco: VCO):
    url = "/v3/cloudcredentials"
    credential_config_name = f"{_get_node_driver_name(vco)}credentialConfig"
    for cloud_credential in _rancher_api_get(cluster, url)["data"]:
        if cloud_credential.get(credential_config_name) is not None:
            break
    else:
        data = {
            credential_config_name: {},
            "description": "Default credential",
            "name": "default",
        }
        cloud_credential = _rancher_api_post(cluster, url, data)
    return cloud_credential


def _create_rke2_machine_pool(
    customer_id: str,
    settings: CreateRancherManagementClusterKubernetesClusterStruct,
    cluster: RancherManagementCluster,
    node_pool: RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct,
    vco: VCO,
) -> Dict:
    pool = {
        "cloudspaceId": node_pool.cloudspace_id,
        "customerId": customer_id,
        "metadata": {
            "annotations": None,
            "finalizers": [],
            "generateName": f"nc-{settings.name}-{node_pool.name}-",
            "labels": None,
            "managedFields": [],
            "namespace": "fleet-default",
            "ownerReferences": [],
        },
        "vmCpus": f"{node_pool.cpus}",
        "vmMem": f"{node_pool.memory}",
        "pinCpus": node_pool.pin_cpus,
        "physicalStorage": node_pool.physical_storage,
        "nodePoolIdentifier": f"{settings.name}-{node_pool.name}",
        "masterNode": node_pool.master,
        "type": f"rke-machine-config.cattle.io.{_get_node_driver_name(vco)}config",
    }
    url = f"/v1/rke-machine-config.cattle.io.{_get_node_driver_name(vco)}configs/fleet-default"
    response = _rancher_api_post(cluster, url, pool)
    return {
        "name": node_pool.name,
        "etcdRole": node_pool.master,
        "controlPlaneRole": node_pool.master,
        "workerRole": not node_pool.master,
        "hostnamePrefix": f"{settings.name}-{node_pool.name}-",
        "labels": {},
        "quantity": node_pool.node_count,
        "unhealthyNodeTimeout": "0m",
        "machineConfigRef": {
            "kind": f"{_get_node_driver_name(vco).capitalize()}Config",
            "name": response["metadata"]["name"],
        },
    }


def _rancher_api_get(cluster: RancherManagementCluster, path: str) -> Dict:
    return _rancher_api(cluster, path, "get")


def _rancher_api_post(cluster: RancherManagementCluster, path: str, data: Dict) -> Dict:
    return _rancher_api(cluster, path, "post", data)


def _rancher_api_put(cluster: RancherManagementCluster, path: str, data: Dict) -> Dict:
    return _rancher_api(cluster, path, "put", data)


def _rancher_api_delete(cluster: RancherManagementCluster, path: str) -> None:
    _rancher_api(cluster, path, "delete")


def _rancher_api(cluster: RancherManagementCluster, path: str, method: str, data: Dict = None) -> Dict:
    queue_name = _get_rancher_request_queue(cluster.vco_id, cluster.customer_id, cluster.management_cluster_id)
    redis: Redis = RedisConnection.get_client()
    items = redis.lrange(queue_name, 0, 1)
    if items:
        request: RancherBridgeRequest = pickle.loads(items[0])
        if time() - request.timestamp > 60:
            raise RuntimeError("The Rancher API bridge is not listening")
    request = RancherBridgeRequest(
        str(uuid.uuid4()),
        method,
        cluster.api_token_id,
        cluster.api_token,
        path,
        base64.b64encode(json.dumps(data).encode()).decode() if data else None,
        int(time()),
    )
    redis.rpush(queue_name, pickle.dumps(request))
    response = redis.blpop(
        _get_rancher_response_queue(cluster.vco_id, cluster.customer_id, cluster.management_cluster_id, request.id), 300
    )
    if not response:
        raise TimeoutError("Timed out waiting for response from the Rancher API bridge")
    response: RancherBridgeResponse = pickle.loads(response[1])
    if response.exception:
        raise RuntimeError(response.exception)
    if response.status >= 400:
        logger.error(response.text)
        raise HTTPError(request=request, response=response)
    if not response.content:
        return
    return json.loads(base64.b64decode(response.content.encode()))


def _fail_node_provisioning(
    jwt: str,
    customer_id: str,
    location: str,
    cloudspace_id: str,
    node_name: str,
    decoded_cloudspace_id: int,
    management_cluster_id: str,
) -> None:
    this = get_context()
    vm_id = this.status.result_value
    logger.info("Cleaning up node vm id %s", vm_id)
    execute_without_failing(_report_failure)
    execute_without_failing(
        delete_virtual_machine,
        jwt=jwt,
        customer_id=customer_id,
        location=location,
        cloudspace_id=decoded_cloudspace_id,
        vm_id=vm_id,
        permanently=True,
        force=True,
    )
    execute_without_failing(
        RancherManagementCluster.delete_node, management_cluster_id=management_cluster_id, vm_id=vm_id
    )
    execute_without_failing(
        _set_provisioner_status_in_redis,
        management_cluster_id=management_cluster_id,
        cloudspace_id=cloudspace_id,
        node_name=node_name,
        status=NodeProvisioningStatus.FAILED,
    )
    re_raise_workflow_failure()


def _check_mgmt_cluster(customer_id: str, management_cluster_id: str, vco_id: str = None) -> RancherManagementCluster:
    cluster = RancherManagementCluster.get_by_id(management_cluster_id, only=["customer_id"])
    if cluster.customer_id != customer_id or (vco_id is not None and cluster.vco_id != vco_id):
        raise KeyError("Cluster not found!")
    return cluster


def _configure_node(jwt: str, node_struct: NodeCreationStruct, location: str, management_cluster_id: str, vm_id: str):
    g8_client = G8Client(location, jwt=jwt)
    logger.info("[+] Adding metadata")
    vm_metadata = VMMetadata.load(g8_client=g8_client, resource_id=vm_id)
    vm_metadata.rancher_management_cluster_id = management_cluster_id
    vm_metadata.save()
    if node_struct.pin_cpus:
        logger.info("[+] Pinning the node's VCPUs")
        g8_client.pin_vm_cpus(vm_id)


def _get_node_driver_name(vco: VCO) -> str:
    return "".join(char for char in vco.domain.lower() if char in ascii_lowercase)


def _apply_template_to_yaml(path: str, **kwargs) -> Union[Dict, Sequence[Any]]:
    is_rancher_resources = kwargs.pop("is_rancher_resources", None)
    with open(path, "r", encoding="utf8") as file:
        template = Template(file.read())
    data = template.render(**kwargs)
    if is_rancher_resources:
        return yaml.safe_load_all(data)
    return yaml.safe_load(data)


def _delete_vms(vms: List[VirtualMachine], jwt: str, location: str, customer_id: str, cloudspace_id: int) -> None:
    pool: Pool = Pool(size=len(vms))
    for vm in vms:
        pool.spawn(
            delete_virtual_machine,
            jwt=jwt,
            customer_id=customer_id,
            location=location,
            cloudspace_id=cloudspace_id,
            vm_id=vm.vm_id,
            permanently=True,
            force=True,
        )
    pool.join()


def _init_creation(management_cluster_id: str, wf_id: str) -> None:
    cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if cluster.status != ClusterStatus.MODELED.value:
        raise RuntimeError("Trying to create a cluster from a status other than MODELED")
    cluster.update(
        push__transition_tasks_details=TaskDetails(
            transition_id=wf_id,
            from_state=cluster.status,
            current_state=ClusterStatus.CREATING.value,
            start_timestamp=time(),
        )
    )
    cluster.status = ClusterStatus.CREATING.value
    cluster.save()


def _init_deletion(cluster_id: str, wf_id: str) -> None:
    cluster = RancherManagementCluster.get_by_id(cluster_id)
    cluster.update(
        push__transition_tasks_details=TaskDetails(
            transition_id=wf_id, from_state=ClusterStatus.RUNNING.value, current_state=ClusterStatus.DELETING.value
        )
    )
    cluster.status = ClusterStatus.DELETING.value
    cluster.save()


def _create_server_pool(
    location: str,
    jwt: str,
    decoded_cloudspace_id: int,
    cluster_creation_struct: RancherManagementClusterCreationStruct,
    management_cluster_id: str,
) -> str:
    logger.info("Creating server pool")
    serverpool_id = ingress_business.create_server_pool(
        location=location,
        jwt=jwt,
        cloudspace_id=decoded_cloudspace_id,
        name=f"{cluster_creation_struct.name}-server-pool",
        description="Management cluster server pool",
    )

    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    management_cluster.serverpool_id = serverpool_id
    management_cluster.save()
    logger.info("Serverpool created")
    return serverpool_id


def _create_load_balancers(
    location: str,
    jwt: str,
    customer_id: str,
    decoded_cloudspace_id: int,
    cluster_creation_struct: RancherManagementClusterCreationStruct,
    wf_id: str,
    sp_task_id: str,
    management_cluster_id: str,
) -> None:
    dq_client: Client = DynaqueueConnection.get_client()
    serverpool_id = dq_client.get_task_status(wf_id)[sp_task_id].result_value
    logger.info("Create Loadbalancers")
    loadbalancers_data = [
        LoadBalancerModel(
            name=f"{cluster_creation_struct.name}-LB-{port}",
            description=None,
            loadbalancer_id=None,
            type="TCP",
            front_end=LoadBalancerFrontEnd(
                port=port,
                ip_address=cluster_creation_struct.external_network_ip,
                tls=LoadBalancerTLS(is_enabled=False, domain=None, tls_termination=False),
            ),
            back_end=LoadBalancerBackEnd(serverpool_id=f"{serverpool_id}", target_port=port),
        )
        for port in [80, 443, 6443]
    ]

    loadbalancers = [
        LoadBalancer(
            loadbalancer_id=ingress_business.create_load_balancer(
                customer_id=customer_id,
                location=location,
                jwt=jwt,
                cloudspace_id=decoded_cloudspace_id,
                load_balancer=loadbalancer,
            )
        )
        for loadbalancer in loadbalancers_data
    ]
    logger.info("Created Loadbalancers")
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    management_cluster.loadbalancers = loadbalancers
    management_cluster.save()


def _wait_vm_agent_ready(location: str, jwt: str, vm_id: int, retries: int = 100, delay: int = 3) -> None:
    g8_client = G8Client(location, jwt=jwt)
    for _ in range(retries):
        vm_info = g8_client.get_vm_info(vm_id)
        if vm_info["agent_status"] == "RUNNING":
            return True
        sleep(delay)
    raise TimeoutError("Timeout waiting for VM Agent to be ready")


def _create_vm(
    customer_id: str,
    name: str,
    decoded_cloudspace_id: int,
    location: str,
    jwt: str,
    account_id: int,
    image_id: str,
    boot_type: str,
    template: VirtualMachineTemplateStruct,
    vco_id: str,
    management_cluster_id: str,
    physical_storage: bool = False,
) -> VirtualMachine:
    g8_client = G8Client(location, jwt=jwt)
    vm_payload = dict(
        name=name,
        description="Management cluster vm",
        vcpus=template.vcpus,
        memory=template.memory,
        disk_size=template.disk_size,
        data_disks=[] if physical_storage else template.data_disks,
        image_id=image_id,
        boot_type=boot_type,
        enable_vm_agent=True,
        user_data=yaml.dump(USER_DATA),
        start_vm=not physical_storage,
    )
    logger.info("Creating Virtual machine")
    vm_id = create_vm(
        customer_id=customer_id,
        location=location,
        jwt=jwt,
        cloudspace_id=decoded_cloudspace_id,
        account_id=account_id,
        vco_id=vco_id,
        **vm_payload,
    )["vm_id"]
    logger.info("Virtual machine created")
    vm_ip = get_vm_info(g8_name=location, jwt=jwt, vm_id=vm_id)["network_interfaces"][0]["ip_address"]
    vm_metadata = VMMetadata.load(g8_client=g8_client, resource_id=vm_id)
    vm_metadata.rancher_management_cluster_id = management_cluster_id
    vm_metadata.save()
    return VirtualMachine(vm_id=vm_id, vm_ip=vm_ip)


def _create_mgm_cluster_vms(
    customer_id: str,
    cluster_creation_struct: RancherManagementClusterCreationStruct,
    decoded_cloudspace_id: int,
    location: str,
    jwt: str,
    account_id: int,
    image_id: str,
    boot_type: str,
    management_cluster_id: str,
    template: VirtualMachineTemplateStruct,
    vco_id: str,
) -> list:
    vm_count = 1 if cluster_creation_struct.cluster_type == ManagementClusterType.SINGLE else 3
    pool: Pool = Pool(size=vm_count)
    normalized_name = normalize_virtual_machine_name(cluster_creation_struct.name)
    vm_data = [
        pool.spawn(
            _create_vm,
            customer_id,
            f"{normalized_name}-node-{index}",
            decoded_cloudspace_id,
            location,
            jwt,
            account_id,
            image_id,
            boot_type,
            template,
            vco_id,
            management_cluster_id,
            cluster_creation_struct.physical_storage,
        )
        for index in range(1, vm_count + 1)
    ]
    pool.join(raise_error=True)
    vms = []
    for vm_datum in vm_data:
        vms.append(vm_datum.get())
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    management_cluster.management_cluster_nodes = vms
    management_cluster.save()
    g8_client: G8Client = None
    if vm_count > 1:
        g8_client = G8Client(location, jwt=jwt)
        g8_client.set_cloudspace_anti_affinity_group(decoded_cloudspace_id, -1, normalized_name)
        for vm in vms:
            g8_client.add_vm_anti_affinity_group(vm_id=vm.vm_id, group_id=normalized_name)
    if cluster_creation_struct.physical_storage:
        for vm in vms:
            create_disk_or_attach_to_vm(
                customer_id=customer_id,
                account_id=account_id,
                location=location,
                jwt=jwt,
                name="cluster-physical-node",
                description="mgmt-cluster-disk",
                disk_type=DiskTypes.PHYSICAL,
                disk_size=template.data_disks[0],
                vm_id=vm.vm_id,
                iops=2000,
                raid_level=RaidLevel.NO_RAID.value,
            )
            if not g8_client:
                g8_client = G8Client(location, jwt=jwt)

            g8_client.start_vm(vm.vm_id)
    for vm in vms:
        pool.spawn(_wait_vm_agent_ready, location, jwt, vm.vm_id)
    pool.join(raise_error=True)
    return vms


def _get_latest_resource(minio_client: Minio, pattern: Pattern) -> Tuple[str, Tuple[int, int, int]]:
    bucket_objects = minio_client.list_objects(K8S_RESOURCES_BUCKET)
    correct_objects = []
    for bucket_object in bucket_objects:
        is_match = pattern.match(bucket_object.object_name)
        if is_match:
            version_tuple = tuple(int(version) for version in is_match.groups())
            correct_objects.append((bucket_object.object_name, version_tuple))
    return max(correct_objects, key=itemgetter(1))


def _get_agent_info() -> Tuple[str, str, str]:
    minio_class = MinioQaDevConnection if EnvironmentName.current() == EnvironmentName.DEV else ExternalMinioConnection
    minio_client = minio_class.get_client()
    latest_package, _ = _get_latest_resource(minio_client, K8S_AGENT_VERSION_PATTERN)
    package_url: str = minio_client.presigned_get_object(K8S_RESOURCES_BUCKET, latest_package)
    return package_url, latest_package, minio_class.URL


def _get_versioned_node_driver(version) -> bytes:
    minio_client = MinioConnection.get_client()
    package = f"docker-machine-driver_{version}.tgz"
    response = minio_client.get_object(K8S_RESOURCES_BUCKET, package)
    file_like_object = io.BytesIO(response.data)
    tar = tarfile.open(fileobj=file_like_object)
    binary_io = tar.extractfile(tar.getmembers()[0])
    binary = binary_io.read()
    return binary


def get_latest_node_driver_version_sha():
    """Get latest node driver version's sha digest"""
    minio_client = MinioConnection.get_client()
    latest_package, latest_version = _get_latest_resource(minio_client, NODE_DRIVER_VERSIONED)
    response = minio_client.get_object(K8S_RESOURCES_BUCKET, f"{latest_package}.sha256")
    sha_digest = response.data.split()[0].decode()
    return "v{}.{}.{}".format(*latest_version), sha_digest  # pylint: disable=consider-using-f-string


def check_cluster_hostname_exists(domain: str) -> bool:
    """Check that domain belongs to a management cluster

    Args:
        domain (str): Management cluster domain

    Returns:
        bool: exists
    """
    return RancherManagementCluster.check_cluster_hostname_exists(domain)


def register_load_balancer_service(
    customer_id: str,
    vco_id: str,
    jwt: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    lb_service: LoadBalancerServiceStruct,
) -> None:
    """Register LoadBalancer Service and its resources

    Args:
        customer_id (str): Customer d
        vco_id (str): VCO ID
        jwt (str): JWT
        management_cluster_id (str): Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID
        lb_service (LoadBalancerServiceStruct): LB Service metadata

    Raises:
        KeyError: Not Found
    """
    if lb_service.ingress_config is not None:
        remove_load_balancer_service(
            customer_id,
            vco_id,
            management_cluster_id,
            kubernetes_cluster_id,
            lb_service.service_uid,
            jwt,
            lb_service.cloudspace_id,
        )
        location, g8_cloudspace_id = decode_validate_cloudspace_id(lb_service.cloudspace_id)
        config = ingress_business.get_ingress_config(jwt, location, g8_cloudspace_id)
        config.load_balancers.extend(lb_service.ingress_config.load_balancers)
        config.server_pools.extend(lb_service.ingress_config.server_pools)
        ingress_business.apply_ingress_config(jwt, location, g8_cloudspace_id, dataclasses.asdict(config), customer_id)
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    db_cluster: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
    if db_cluster is None:
        raise KeyError(f"Cluster with ID {kubernetes_cluster_id} not found")
    lb_service_dict = dataclasses.asdict(lb_service)
    lb_service_dict.pop("ingress_config")
    lbs = LoadBalancerService(**lb_service_dict)
    cluster.update(**{f"push__kubernetes_clusters__{db_cluster.kubernetes_cluster_id}__load_balancer_services": lbs})


def ensure_load_balancer_service(
    customer_id: str,
    vco_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    jwt: str,
    lb_service: LoadBalancerServiceStruct,
) -> None:
    """Ensure LoadBalancer Service and its resources

    Args:
        customer_id (str): Customer ID
        vco_id (str): VCO ID
        management_cluster_id (str): Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID
        jwt (str): JWT
        lb_service (LoadBalancerServiceStruct): LB Service metadata

    Raises:
        KeyError: Not Found
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    db_cluster: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
    if db_cluster is None:
        raise KeyError(f"Cluster with ID {kubernetes_cluster_id} not found")
    db_lbs: LoadBalancerService
    for db_lbs in db_cluster.load_balancer_services:
        if db_lbs.service_uid == lb_service.service_uid:
            break
    else:
        raise KeyError(f"LoadBalancer Service with UID {lb_service.service_uid} is not registered")
    # Check for dangling LoadBalancers/ServicePools that hold the same UID
    location, g8_cloudspace_id = decode_validate_cloudspace_id(lb_service.cloudspace_id)
    lbs = ingress_business.list_load_balancers(location, jwt, g8_cloudspace_id)
    for lb in lbs:
        if lb["name"].endswith(lb_service.service_uid) and lb["loadbalancer_id"] not in lb_service.loadbalancer_ids:
            try:
                ingress_business.get_load_balancer(location, jwt, g8_cloudspace_id, lb["loadbalancer_id"])
                ingress_business.delete_load_balancer(
                    customer_id, location, jwt, g8_cloudspace_id, lb["loadbalancer_id"]
                )
            except KeyError:
                logger.warning("Loadbalancer %s not found, passing", lb["loadbalancer_id"])
    sps = ingress_business.list_server_pools(location, jwt, g8_cloudspace_id)
    for sp in sps:
        if sp.name.endswith(lb_service.service_uid) and sp.serverpool_id != lb_service.serverpool_id:
            ingress_business.delete_server_pool(customer_id, location, jwt, g8_cloudspace_id, sp.serverpool_id)


def remove_load_balancer_service(
    customer_id: str,
    vco_id: str,
    management_cluster_id: str,
    kubernetes_cluster_id: str,
    service_uid: str,
    jwt: str,
    cloudspace_id: Optional[str] = None,
) -> None:
    """Remove and delete LoadBalancer Service resources

    Args:
        customer_id (str): Customer ID
        vco_id (str): VCO ID
        management_cluster_id (str): Management Cluster ID
        kubernetes_cluster_id (str): Kubernetes Cluster ID
        service_uid (str): LB Service UID
        jwt (str): JWT Token
        cloudspace_id (str): Cloudspace Cluster ID

    Raises:
        KeyError: Entry not found
    """
    try:
        cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
        db_cluster: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
        if db_cluster is None:
            raise KeyError(f"Cluster with ID {kubernetes_cluster_id} not found")
        lbs: LoadBalancerService
        for lbs in db_cluster.load_balancer_services:
            if lbs.service_uid == service_uid:
                break
        else:
            logger.info("LoadBalancer Service %d not found, returning", service_uid)
            return
        _delete_ingress_resources(customer_id, jwt, lbs)
        cluster.update(
            **{
                f"pull__kubernetes_clusters__{db_cluster.kubernetes_cluster_id}"
                "__load_balancer_services__service_uid": service_uid
            }
        )
    finally:
        # Cleanup any dangling resources
        if cloudspace_id:
            _delete_dangling_ingress_resources(customer_id, jwt, cloudspace_id, service_uid)


def _mount_vm_file(
    location: str,
    jwt: str,
    vm_id: int,
    vco_id: str,
    iam_key: str,
    customer_id: str,
    cloudspace_id: str,
    management_cluster_id: str,
    is_cluster: bool = False,
    physical_storage: bool = False,
) -> None:
    logger.info("Init vm %s: Starting vm initialization", vm_id)
    g8_client = G8Client(location, jwt=jwt)
    package_url, package_name, _ = _get_agent_info()
    with open(VM_MOUNT_PATH, "r", encoding="utf-8") as file_handler:
        template = Template(file_handler.read())
    vco = VCO.get_by_id(vco_id)
    vco_scheme = "http" if EnvironmentName.current() == EnvironmentName.DEV else "https"
    vco_api_url = f"{vco_scheme}://{vco.domain}/api/1"
    disks = g8_client.get_vm_info(vm_id)["disks"]
    data_disk = None
    boot_disk = None
    for disk in disks:
        if disk["disk_type"] == "B":
            boot_disk = disk["disk_id"]
        elif disk["disk_type"] in ("D", "P"):
            data_disk = disk["disk_id"]
    if not boot_disk or not data_disk:
        raise RuntimeError("Couldn't get VM disk ids")
    data = template.render(
        PACKAGE_NAME=package_name,
        PACKAGE_URL=package_url,
        VCO_ID=vco_id,
        JWT=jwt,
        IAM_PUB_KEY=iam_key,
        IAM_URL=f"https://{vco.iam_domain}",
        DIR_NAME=package_name[:-7],
        VCO_API_URL=vco_api_url,
        LOCATION=location,
        CUSTOMER_ID=customer_id,
        IS_CLUSTER=is_cluster,
        VM_ID=vm_id,
        MANAGEMENT_CLUSTER_ID=management_cluster_id,
        CLOUDSPACE_ID=cloudspace_id,
        DATA_DISK=data_disk,
        BOOT_DISK=boot_disk,
        IS_DEV=EnvironmentName.current() == EnvironmentName.DEV,
        DEV_ENV_VPN_IP_ADDRESS=os.environ.get("DEV_ENV_VPN_IP_ADDRESS"),
        IAM_CERTIFICATE=_get_dev_iam_certificate() if EnvironmentName.current() == EnvironmentName.DEV else None,
        PHYSICAL_STORAGE=physical_storage,
    )
    content = base64.b64encode(data.encode("ascii")).decode()
    script = "/root/schedule_init.sh"
    g8_client.write_vm_file(vm_id, content, script)
    g8_client.execute_vm_command(vm_id, "/bin/bash", [script])

    # Forward the logs
    logs = ""
    while True:
        current_logs = base64.b64decode(
            _try_action(12, 5, g8_client.read_vm_file, vm_id, 2 * 1024 * 1024, "/root/init.logs")["result"]
        )
        if logs == current_logs:
            exit_status = base64.b64decode(
                _try_action(12, 5, g8_client.read_vm_file, vm_id, 2 * 1024 * 1024, "/root/init.result")["result"]
            ).strip()
            if exit_status:
                if int(exit_status) == 0:
                    break
                raise RuntimeError(f"VM init failed with {exit_status}")
            else:
                sleep(5)
        else:
            for line in current_logs[len(logs) :].splitlines():
                logger.info("Init vm %s: %s", vm_id, line.decode())
            logs = current_logs
            sleep(5)
    logger.info("Init vm %s: Rebooting", vm_id)
    if not is_cluster:
        g8_client.write_vm_file(
            vm_id, base64.b64encode(b"CATTLE_AGENT_STRICT_VERIFY='false'").decode(), "/etc/environment", True
        )

    if not is_cluster:
        g8_client.write_vm_file(
            vm_id, base64.b64encode(b"CATTLE_AGENT_STRICT_VERIFY='false'").decode(), "/etc/environment", True
        )

    g8_client.reboot_vm(vm_id)
    logger.info("Init vm %s: Waiting for the machine to be ready", vm_id)
    sleep(20)  # Sleeping 20 secs to allow the machine to boot
    _wait_vm_agent_ready(location, jwt, vm_id)
    logger.info("Init vm %s: Ready initializing", vm_id)


def _mount_var_disk(
    location: str,
    jwt: str,
    wf_id: str,
    vm_creation_task_id: str,
    vco_id: str,
    iam_key: str,
    customer_id: str,
    cloudspace_id: str,
    management_cluster_id: str,
    physical_storage: bool = False,
) -> None:
    dq_client = DynaqueueConnection.get_client()
    vms = dq_client.get_task_status(wf_id)[vm_creation_task_id].result_value
    pool: Pool = Pool(size=len(vms))
    for vm in vms:
        pool.spawn(
            _mount_vm_file,
            location,
            jwt,
            vm.vm_id,
            vco_id,
            iam_key,
            customer_id,
            cloudspace_id,
            management_cluster_id,
            True,
            physical_storage,
        )
    pool.join(raise_error=True)


def _transfer_rke_config(
    path: str,
    location: str,
    jwt: str,
    cloudspace_ip: str,
    name: str,
    wf_id: str,
    vm_creation_task_id: str,
    domain_name: str,
    vco_id: str,
    certificate: str,
    private_key: str,
    management_cluster_id: str,
    customer_id: str,
    tag: str,
) -> None:
    dq_client = DynaqueueConnection.get_client()
    vms = dq_client.get_task_status(wf_id)[vm_creation_task_id].result_value
    # Parse rke config yaml and send it to the VM, and save a copy in the db
    master_vm_id = vms[0].vm_id

    rke_config = _apply_template_to_yaml(
        path,
        VM_IPS=[vm.vm_ip for vm in vms],
        CLOUDSPACE_IP=cloudspace_ip,
        CLUSTER_NAME=name,
        domain_name=domain_name,
        certificate=True if certificate else False,
    )

    logger.info("Transferring cluster configuration")

    validate_and_write_vm_file(
        location,
        jwt,
        vm_id=master_vm_id,
        content=base64.b64encode(yaml.dump(rke_config).encode("ascii")).decode(),
        filepath="/etc/rke/rke-cfg.yml",
    )

    vco = VCO.get_by_id(vco_id)

    if EnvironmentName.current() == EnvironmentName.DEV:
        iam_cert = _get_dev_iam_certificate()
        dev_env_vpn_ip_address = os.environ.get("DEV_ENV_VPN_IP_ADDRESS")
    else:
        iam_cert = None
        dev_env_vpn_ip_address = None

    is_dev = EnvironmentName.current() == EnvironmentName.DEV
    rancher_resources = _apply_template_to_yaml(
        RANCHER_RESOURCES_PATH,
        domain_name=domain_name,
        replicas=len(vms),
        is_rancher_resources=True,
        vco_domain=vco.domain,
        iam_domain=vco.iam_domain,
        tag=tag,
        is_dev=is_dev,
        certificate=base64.b64encode(certificate.encode()).decode() if certificate else None,
        private_key=base64.b64encode(private_key.encode()).decode() if certificate else None,
        iam_certificate=iam_cert,
        dev_env_vpn_ip_address=dev_env_vpn_ip_address,
        system_namespace=_get_system_namespace(vco),
        jwt=jwt,
        management_cluster_id=management_cluster_id,
        customer_id=customer_id,
    )

    validate_and_write_vm_file(
        location,
        jwt,
        vm_id=master_vm_id,
        content=base64.b64encode(yaml.dump_all(rancher_resources).encode("ascii")).decode(),
        filepath="/etc/rke/rancher.yaml",
    )

    version, sha_digest = get_latest_node_driver_version_sha()

    node_driver_config = _apply_template_to_yaml(
        NODE_DRIVER_PATH,
        name=vco.name,
        domain=domain_name,
        vco_domain=vco.domain,
        node_driver_name=_get_node_driver_name(vco),
        node_driver_display_name=vco.name,
        version=version,
        scheme="http" if is_dev else "https",
        checksum=sha_digest,
    )
    validate_and_write_vm_file(
        location,
        jwt,
        vm_id=master_vm_id,
        content=base64.b64encode(yaml.dump(node_driver_config).encode("ascii")).decode(),
        filepath="/etc/rke/node_driver.yaml",
    )
    logger.info("Transferred")


def _get_dev_iam_certificate():
    iam_cert = subprocess.run(
        "openssl s_client -connect iam.cairo-cloud.eg.local:443 </dev/null 2>/dev/null | openssl x509 -outform PEM",
        shell=True,
        check=True,
        stdout=subprocess.PIPE,
    ).stdout.decode()
    return iam_cert


def _add_vms_to_sp(
    jwt: str, customer_id: str, location: str, decoded_cloudspace_id: int, wf_id: str, sp_task_id: str, vms_task_id: str
) -> None:
    dq_client = DynaqueueConnection.get_client()
    serverpool_id = dq_client.get_task_status(wf_id)[sp_task_id].result_value
    vms = dq_client.get_task_status(wf_id)[vms_task_id].result_value
    logger.info("Adding virtual machines to server pool")
    for vm in vms:
        ingress_business.add_host_to_server_pool(
            customer_id=customer_id,
            location=location,
            jwt=jwt,
            cloudspace_id=decoded_cloudspace_id,
            serverpool_id=serverpool_id,
            address=vm.vm_ip,
        )
    logger.info("Added virtual machines to server pool %s", serverpool_id)


def _register_dns_domain_name(
    domain_name: str, vco_id: str, customer_id: str, location: str, cloudspace_id: int, jwt: str, cloudspace_ip: str
) -> None:
    if not domain_name:
        return
    if (
        DnsRecord.list(domain_name=domain_name, record_type=SupportedDNSRecordType.A.value, value=cloudspace_ip).count()
        > 0
    ):
        logger.info("The domain name record exists as one of the customer's domain names.")
        return
    logger.info("Checking if we need to create the domain name record ...")
    if resource_domain_exists(customer_id, domain_name):
        logger.info("Creating the domain name record using the cloud providers DNS infrastructure ...")
        add_dns_record(vco_id, customer_id, location, cloudspace_id, jwt, domain_name, value=cloudspace_ip)
        return
    logger.info(
        "There is no cloud provider domain name infrastructure for this domain. "
        "Validating if the domain name exists ..."
    )
    ip = get_host_by_name(domain_name)
    logger.info("This domain is hosted by an external DNS server, and resolves to %s!", ip)


def _cleanup_dns_record(domain_name: str, cloudspace_id: int) -> None:
    if not domain_name:
        return
    delete_dns_records(
        DnsRecord.list(domain_name=domain_name, cloudspace_id=cloudspace_id, record_type=SupportedDNSRecordType.A.value)
    )


def _deploy_rke_cluster(
    path: str,
    location: str,
    jwt: str,
    customer_id: str,
    vco_id: str,
    wf_id: str,
    vm_creation_task_id: str,
    domain_name: str,
    letsencrypt_email: str,
    api_token_id: str,
    api_token: str,
    iam_organization: str,
    iam_organization_api_key: str,
    iam_domain: str,
    cluster_name: str,
) -> None:
    dq_client = DynaqueueConnection.get_client()
    vms = dq_client.get_task_status(wf_id)[vm_creation_task_id].result_value
    master_vm_id = vms[0].vm_id

    vco = VCO.get_by_id(vco_id, only=["branding.logo", "branding.color_scheme.primary"])
    logo_base64 = base64.b64encode(vco.branding.logo).decode()
    pc = vco.branding.color_scheme.primary
    primary_color_rgb = f"{int(pc[1:3],base=16)}, {int(pc[3:5],base=16)}, {int(pc[5:7],base=16)}"

    def _log_to_workflow(logs, current_logs):
        for line in current_logs[len(logs) :].splitlines():
            match = LOG_LINE_RE.match(line.decode())
            if match and hasattr(logger, match.group("level")):
                getattr(logger, match.group("level"))(match.group("msg"))
            else:
                logger.info(line.decode())

    def _read_logs_from_vm(master_vm_id, g8_client):
        return base64.b64decode(
            _try_action(12, 5, g8_client.read_vm_file, master_vm_id, 2 * 1024 * 1024, "/etc/rke/logs.txt")["result"]
        )

    with open(path, "r", encoding="utf8") as fh:
        template = Template(fh.read())
    rancher_setup_dot_sh = template.render(
        CLIENT_JWT=jwt,
        CUSTOMER_ID=customer_id,
        VCO_URL=vco_id,
        DOMAIN_NAME=domain_name,
        EMAIL=letsencrypt_email,
        API_TOKEN_ID=api_token_id,
        API_TOKEN=api_token,
        IAM_ORG=iam_organization,
        IAM_ORG_KEY=iam_organization_api_key,
        IAM_DOMAIN=iam_domain,
        VCO_LOGO_BASE64=logo_base64,
        VCO_PRIMARY_COLOR_RGB=primary_color_rgb,
        CLUSTER_NAME=cluster_name,
    )

    g8_client = G8Client(location, jwt)
    g8_client.write_vm_file(
        master_vm_id, base64.b64encode(rancher_setup_dot_sh.encode()).decode(), "/etc/rke/rancher_setup.sh"
    )
    g8_client.execute_vm_command(master_vm_id, "bash", args=["/etc/rke/rancher_setup.sh"])

    logs = ""
    while True:
        current_logs = _read_logs_from_vm(master_vm_id, g8_client)
        if logs == current_logs:
            exit_status = base64.b64decode(
                _try_action(
                    12, 5, g8_client.read_vm_file, master_vm_id, 2 * 1024 * 1024, "/etc/rke/setup_rancher.result"
                )["result"]
            ).strip()
            if exit_status:
                current_logs = _read_logs_from_vm(master_vm_id, g8_client)
                _log_to_workflow(logs, current_logs)
                if int(exit_status) == 0:
                    return
                raise RuntimeError(f"Deployment failed with exit code {exit_status}")
            else:
                sleep(5)
        else:
            _log_to_workflow(logs, current_logs)
            logs = current_logs
            sleep(5)


def _rename_cluster_in_rancher(management_cluster_id: str):
    cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    exc: Exception = Exception()
    max_retries = 10
    for _ in range(max_retries):
        try:
            rancher_cluster = _rancher_api_get(cluster, "/v3/clusters/local")
            rancher_cluster["name"] = cluster.name
            _rancher_api_put(cluster, "/v3/clusters/local?_replace=true", rancher_cluster)
            break
        except (requests.exceptions.HTTPError, RuntimeError) as err:
            exc = err
            sleep(10)
    else:
        raise exc


def _try_action(times: int, interruption: int, action: Callable, *args, **kwargs) -> Any:
    failure = None
    for _ in range(times):
        try:
            return action(*args, **kwargs)
        except Exception as error:  # pylint: disable=broad-exception-caught
            failure = error
            sleep(interruption)
    raise failure


def _cleanup_loadbalancers(
    management_cluster_id: str, customer_id: str, location: str, jwt: str, cloudspace_id: int
) -> None:
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    for lb in management_cluster.loadbalancers:
        try:
            ingress_business.get_load_balancer(location, jwt, cloudspace_id, lb.loadbalancer_id)
            ingress_business.delete_load_balancer(customer_id, location, jwt, cloudspace_id, lb.loadbalancer_id)
        except KeyError:
            logger.warning("Loadbalancer %s not found, passing", lb.loadbalancer_id)


def _cleanup_serverpools(
    management_cluster_id: str, customer_id: str, location: str, jwt: str, cloudspace_id: int
) -> None:
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if management_cluster.serverpool_id:
        try:
            ingress_business.delete_server_pool(
                jwt=jwt,
                serverpool_id=management_cluster.serverpool_id,
                customer_id=customer_id,
                location=location,
                cloudspace_id=cloudspace_id,
            )
        except KeyError:
            logger.warning("Serverpool %s not found, passing", management_cluster.serverpool_id)


def _cleanup_vms(
    cluster_name: str, management_cluster_id: str, customer_id: str, location: str, jwt: str, cloudspace_id: int
) -> None:
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if not management_cluster.management_cluster_nodes:
        return
    _delete_vms(
        vms=management_cluster.management_cluster_nodes,
        jwt=jwt,
        location=location,
        customer_id=customer_id,
        cloudspace_id=cloudspace_id,
    )
    if management_cluster.cluster_type == ManagementClusterType.HA:
        group_id = normalize_virtual_machine_name(cluster_name)
        G8Client(location, jwt=jwt).delete_cloudspace_anti_affinity_group(
            cloudspace_id=cloudspace_id, group_id=group_id
        )


def _cleanup_nodes(management_cluster_id: str, jwt: str, customer_id: str) -> None:
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    for node in management_cluster.cluster_nodes:
        location, decoded_cloudspace_id = decode_validate_cloudspace_id(node.cloudspace_id)
        try:
            delete_virtual_machine(jwt, customer_id, location, decoded_cloudspace_id, node.vm_id, True, True)
        except HTTPError as exc:
            if exc.response.status_code == 404:
                logger.warning("Virtual machine %s not found, passing", node.vm_id)
                continue
            raise


def _finalize_status(management_cluster_id: str, success: bool, wf_id: str, status: ClusterStatus) -> None:
    cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if cluster.status != status:
        raise RuntimeError(f"Invalid status for {status} cluster in {cluster.status} status")
    target_status = ClusterStatus.RUNNING if success else ClusterStatus.DELETED
    cluster.status = target_status.value
    if not success and status == ClusterStatus.CREATING:
        cluster.deleted = True
        cluster.status = ClusterStatus.DELETED.value
        target_status = ClusterStatus.DELETED
    for task in cluster.transition_tasks_details:
        if task.transition_id == wf_id:
            task.final_state = target_status.value
            task.end_timestamp = time()
            cluster.save()
            break
    else:
        cluster.save()
        raise ValueError("Failed to find task")
    if not success:
        _report_failure(wf_id)
        raise RuntimeError("Deployment of the Rancher management cluster failed.")


def _cleanup_ingress_resources(customer_id: str, jwt: str, management_cluster_id: str, vco_id: str):
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    db_cluster: KubernetesCluster
    for db_cluster in cluster.kubernetes_clusters.values():
        for lbs in db_cluster.load_balancer_services:
            _delete_ingress_resources(customer_id, jwt, lbs)


def _finalize_deletion_status(management_cluster_id: str, wf_id: str) -> None:
    cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    if cluster.status != ClusterStatus.DELETING.value:
        raise RuntimeError(f"Invalid status for DELETING cluster {cluster.status}")
    target_status = ClusterStatus.DELETED
    cluster.status = target_status.value
    for task in cluster.transition_tasks_details:
        if task.transition_id == wf_id:
            task.final_state = target_status.value
            task.end_timestamp = time()
            cluster.deleted = True
            cluster.save()
            break
    else:
        cluster.deleted = True
        cluster.save()
        _report_failure(workflow_id=wf_id)
        raise ValueError("Failed to find task")
    this: TaskQueueItem = get_context()
    status: TaskStatus = this.status
    while status.parent is not None:
        status = status.parent
        if status.status == STATUS_FAILED:
            _report_failure(workflow_id=wf_id)
            raise ValueError("Something failed during cleanup")


def _setup_iam(
    customer_id: str, cluster_creation_struct: RancherManagementClusterCreationStruct, vco: VCO
) -> Tuple[str, str]:
    customer = Customer.get_by_id(customer_id)
    is_https = EnvironmentName.current() != EnvironmentName.TEST
    iam_client = ItsyouOnlineClient.new(
        vco.iam_domain, vco.iam_root_organization, vco.iam_root_organization_api_key, is_https
    )

    try:
        iam_client.create_suborganization(f"{vco.iam_root_organization}.customers.{customer_id}.kaas")
    except OrganizationAlreadyExists:
        pass
    tech_auth_org = cluster_creation_struct.domain_name.replace(".", "-")
    tech_auth_org = f"{vco.iam_root_organization}.customers.{customer_id}.kaas.{tech_auth_org}"
    try:
        iam_client.create_suborganization(tech_auth_org, orgmembers=[customer.organization])
    except OrganizationAlreadyExists:
        iam_client.delete_organization(tech_auth_org)
        iam_client.create_suborganization(tech_auth_org, orgmembers=[customer.organization])
    api_key_secret = iam_client.generate_api_key(
        tech_auth_org, label="Authentication", callback_url=f"https://{cluster_creation_struct.domain_name}/verify-auth"
    )

    return tech_auth_org, api_key_secret


def _cleanup_iam(cluster_id: str) -> None:
    cluster = RancherManagementCluster.get_by_id(
        cluster_id, only=["customer_id", "technical_authorization_organization"]
    )
    customer = Customer.get_by_id(cluster.customer_id, only=["vco"])
    vco = VCO.get_by_id(customer.vco, only=["iam_domain", "iam_root_organization", "iam_root_organization_api_key"])
    is_https = EnvironmentName.current() != EnvironmentName.TEST
    iam_client = ItsyouOnlineClient.new(
        vco.iam_domain, vco.iam_root_organization, vco.iam_root_organization_api_key, is_https
    )
    iam_client.delete_organization(cluster.technical_authorization_organization)


def _get_matching_certificate(
    customer_id: str, cluster_creation_struct: RancherManagementClusterCreationStruct, lets_encrypt: bool = None
) -> CustomerSSLCertificate:
    if cluster_creation_struct.letsencrypt_email and cluster_creation_struct.letsencrypt_email.strip():
        raise ValueError("When providing the ssl certificate, the letsencrypt email address should not be set")
    now = time()
    the_end = 0.0
    the_certificate = None
    for certificate in certificate_business.get_certificates_for_domain(
        customer_id,
        cluster_creation_struct.domain_name,
        source=cluster_creation_struct.ssl_certificate_source if lets_encrypt else None,
    ):
        interface, _ = certificate.crt.split(ssl.PEM_FOOTER, 1)
        interface += ssl.PEM_FOOTER
        cert = crypto.load_certificate(crypto.FILETYPE_PEM, interface)
        start = datetime.strptime(cert.get_notBefore().decode(), "%Y%m%d%H%M%SZ").timestamp()
        end = datetime.strptime(cert.get_notAfter().decode(), "%Y%m%d%H%M%SZ").timestamp()
        if now < start or now > end:
            continue
        if end > the_end:
            the_certificate = certificate
            the_end = end
    if the_certificate is None:
        raise KeyError(f"No valid certificates are registered for domain {cluster_creation_struct.domain_name}")
    return the_certificate  # pylint: disable=no-member


def _log_management_cluster_details(
    vco_id: str, customer_id: str, cluster_creation_struct: RancherManagementClusterCreationStruct
) -> None:
    vco = VCO.get_by_id(vco_id, only=["name"])
    customer = Customer.get_by_id(customer_id, only=["company_information.name"])
    logger.info(
        "Setting up a new rancher cluster on %s (%s) for %s (%s)",
        vco.name,
        vco_id,
        customer.company_information.name,
        customer_id,
    )
    logger.info("Rancher domain: %s", cluster_creation_struct.domain_name)
    logger.info("Rancher cloudspace: %s", cluster_creation_struct.cloudspace_id)
    logger.info(
        "Rancher cloud location: %s (%s)", *decode_validate_cloudspace_id(cluster_creation_struct.cloudspace_id)
    )
    logger.info("Rancher cluster type: %s", cluster_creation_struct.cluster_type)
    logger.info("SSL Certificate source: %s", cluster_creation_struct.ssl_certificate_source)


def _create_management_cluster(
    jwt: str,
    customer_id: str,
    cluster_creation_struct: RancherManagementClusterCreationStruct,
    vco_id: str,
    management_cluster_id: str,
    iam_key: str,
    the_certificate: CustomerSSLCertificate,
    letsencrypt_email: str,
    api_token_id: str,
    api_token: str,
    iam_organization: str,
    iam_organization_api_key: str,
    iam_domain: str,
) -> None:
    location, decoded_cloudspace_id = decode_validate_cloudspace_id(cluster_creation_struct.cloudspace_id)
    g8_client = G8Client(location, jwt=jwt)
    image = g8_client.get_image(g8_client.list_images(tags=os.environ.get("RANCHER_IMAGE_TAG"))[0]["image_id"])
    image_id = image["image_id"]
    boot_type = image["boot_type"]
    account_id = Customer.get_location(customer_id=customer_id, location=location).g8_account_id
    settings = RancherAsAServiceSettings.get_settings()
    template = VirtualMachineTemplateStruct(
        vcpus=settings.mgmt_cluster_vcpus,
        memory=settings.mgmt_cluster_memory,
        disk_size=settings.mgmt_cluster_boot_disk_size,
        data_disks=[settings.mgmt_cluster_data_disk_size],
    )
    init_status_task = Task("Init Management Cluster creation", timeout=21600)
    init_status_task.set_workload(_log_management_cluster_details, vco_id, customer_id, cluster_creation_struct)

    register_domain_name = Task("Register Domain Name")
    register_domain_name.set_workload(
        _register_dns_domain_name,
        cluster_creation_struct.domain_name,
        vco_id,
        customer_id,
        location,
        decoded_cloudspace_id,
        jwt,
        cluster_creation_struct.external_network_ip,
    )
    init_status_task.on_success = register_domain_name

    sp_task = Task("Creating server pool")
    sp_task.set_workload(
        _create_server_pool, location, jwt, decoded_cloudspace_id, cluster_creation_struct, management_cluster_id
    )
    register_domain_name.on_success = sp_task

    lb_task = Task("Creating LoadBalancers")
    lb_task.set_workload(
        _create_load_balancers,
        location,
        jwt,
        customer_id,
        decoded_cloudspace_id,
        cluster_creation_struct,
        init_status_task.id,
        sp_task.id,
        management_cluster_id,
    )
    sp_task.on_success = lb_task

    vms_task = Task("Creating Management Cluster VMs", timeout=1800)
    vms_task.set_workload(
        _create_mgm_cluster_vms,
        customer_id,
        cluster_creation_struct,
        decoded_cloudspace_id,
        location,
        jwt,
        account_id,
        image_id,
        boot_type,
        management_cluster_id,
        template,
        vco_id,
    )
    lb_task.on_success = vms_task
    add_vm_to_sp_task = Task("Adding vms to server pool")
    add_vm_to_sp_task.set_workload(
        _add_vms_to_sp, jwt, customer_id, location, decoded_cloudspace_id, init_status_task.id, sp_task.id, vms_task.id
    )
    vms_task.on_success = add_vm_to_sp_task
    mount_var_disk_task = Task("Mounting Data Disk at /var", timeout=2800)
    mount_var_disk_task.set_workload(
        _mount_var_disk,
        location,
        jwt,
        init_status_task.id,
        vms_task.id,
        vco_id,
        iam_key,
        customer_id,
        cluster_creation_struct.cloudspace_id,
        management_cluster_id,
        cluster_creation_struct.physical_storage,
    )
    add_vm_to_sp_task.on_success = mount_var_disk_task
    transfer_rke_config = Task("Transfer RKE configuration", timeout=1200)
    transfer_rke_config.set_workload(
        _transfer_rke_config,
        MGMT_RKE_PATH,
        location,
        jwt,
        cluster_creation_struct.external_network_ip,
        cluster_creation_struct.name,
        init_status_task.id,
        vms_task.id,
        cluster_creation_struct.domain_name,
        vco_id,
        certificate=the_certificate.crt if the_certificate else None,
        private_key=the_certificate.key if the_certificate else None,
        management_cluster_id=management_cluster_id,
        customer_id=customer_id,
        tag=cluster_creation_struct.tag,
    )
    mount_var_disk_task.on_success = transfer_rke_config

    deploy_rke_cluster_task = Task("Deploying RKE Cluster", timeout=21600)
    deploy_rke_cluster_task.set_workload(
        _deploy_rke_cluster,
        RANCHER_SETUP_PATH,
        location,
        jwt,
        customer_id,
        vco_id,
        init_status_task.id,
        vms_task.id,
        cluster_creation_struct.domain_name,
        letsencrypt_email,
        api_token_id,
        api_token,
        iam_organization,
        iam_organization_api_key,
        iam_domain,
        cluster_creation_struct.name,
    )
    transfer_rke_config.on_success = deploy_rke_cluster_task

    rename_cluster_in_rancher_task = Task("Rename Cluster in Rancher", timeout=600)
    rename_cluster_in_rancher_task.set_workload(_rename_cluster_in_rancher, management_cluster_id)
    deploy_rke_cluster_task.on_success = rename_cluster_in_rancher_task

    cleanup_lbs_task = Task("Cleanup failed creation: Delete LBs")
    cleanup_lbs_task.set_workload(
        _cleanup_loadbalancers, management_cluster_id, customer_id, location, jwt, decoded_cloudspace_id
    )
    init_status_task.on_failure = cleanup_lbs_task
    register_domain_name.on_failure = cleanup_lbs_task
    sp_task.on_failure = cleanup_lbs_task
    lb_task.on_failure = cleanup_lbs_task
    vms_task.on_failure = cleanup_lbs_task
    add_vm_to_sp_task.on_failure = cleanup_lbs_task
    mount_var_disk_task.on_failure = cleanup_lbs_task
    transfer_rke_config.on_failure = cleanup_lbs_task
    deploy_rke_cluster_task.on_failure = cleanup_lbs_task
    rename_cluster_in_rancher_task.on_failure = cleanup_lbs_task

    cleanup_sps_task = Task("Cleanup failed creation: Delete SPs")
    cleanup_sps_task.set_workload(
        _cleanup_serverpools, management_cluster_id, customer_id, location, jwt, decoded_cloudspace_id
    )

    cleanup_lbs_task.on_success = cleanup_sps_task
    cleanup_lbs_task.on_failure = cleanup_sps_task

    cleanup_vms_task = Task("Cleanup failed creation: Delete VMs")
    cleanup_vms_task.set_workload(
        _cleanup_vms,
        cluster_creation_struct.name,
        management_cluster_id,
        customer_id,
        location,
        jwt,
        decoded_cloudspace_id,
    )

    cleanup_sps_task.on_success = cleanup_vms_task
    cleanup_sps_task.on_failure = cleanup_vms_task

    cleanup_dns_record = Task("Cleanup failed creation: Delete DNS Record")
    cleanup_dns_record.set_workload(_cleanup_dns_record, cluster_creation_struct.domain_name, decoded_cloudspace_id)

    cleanup_vms_task.on_success = cleanup_dns_record
    cleanup_vms_task.on_failure = cleanup_dns_record

    cleanup_iam = Task("Cleanup failed creation: iam")
    cleanup_iam.set_workload(_cleanup_iam, management_cluster_id)

    cleanup_dns_record.on_success = cleanup_iam
    cleanup_dns_record.on_failure = cleanup_iam

    finalize_failure = Task("Finalize failure status")
    finalize_failure.set_workload(
        _finalize_status, management_cluster_id, False, init_status_task.id, ClusterStatus.CREATING
    )

    cleanup_iam.on_success = finalize_failure
    cleanup_iam.on_failure = finalize_failure

    finalize_success = Task("Finalize Success status")
    finalize_success.set_workload(
        _finalize_status, management_cluster_id, True, init_status_task.id, ClusterStatus.CREATING
    )

    rename_cluster_in_rancher_task.on_success = finalize_success

    _init_creation(management_cluster_id, init_status_task.id)

    client: Client = DynaqueueConnection.get_client()
    client.submit_task_async(init_status_task)


def _get_workflow_logs(
    workflow_id, work_flow_info: dict, logs: List[TransitionLogsStruct]
) -> List[TransitionLogsStruct]:
    task_logs = get_task_logs(workflow_id, work_flow_info["id"], True)
    logs.append(TransitionLogsStruct(title=work_flow_info["title"], logs=task_logs))
    if work_flow_info["on_success"] and (
        work_flow_info["on_success"]["status"] == "SUCCEEDED" or work_flow_info["on_success"]["status"] == "RUNNING"
    ):
        return _get_workflow_logs(workflow_id, work_flow_info["on_success"], logs)
    if work_flow_info["on_failure"] and (
        work_flow_info["on_failure"]["status"] == "SUCCEEDED" or work_flow_info["on_failure"]["status"] == "RUNNING"
    ):
        return _get_workflow_logs(workflow_id, work_flow_info["on_failure"], logs)
    return logs


def update_rancher_image(
    customer_id: str,
    rancher_id: str,
    tag: str,
    namespace: str,
    deployment_name: str,
    image_name: str,
    container_name: str,
    liveness: str = None,
    validate: bool = False,
) -> None:
    """Update rancher image of selected cluster

    Args:
        customer_id (str): Customer id
        rancher_id (str): Management cluster ID
        tag (str): Target version
    """
    _check_mgmt_cluster(customer_id, rancher_id)
    cluster = RancherManagementCluster.get_by_id(rancher_id)
    customer = Customer.get_by_id(cluster.customer_id, only=["vco"])
    vco = VCO.get_by_id(customer.vco)
    if cluster.status not in ClusterStatus.get_running_statuses():
        raise ValueError("Updating image is available only for running clusters")
    if validate:
        _validate_cluster_nodes_health(cluster)
        _pods_are_running(cluster, "deployment", "cattle", "cattle-system")

    RancherVersions.get_by_tag(tag=tag)  # Check that we supprot this tag
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    jwt = get_g8_jwt_from_db(location)
    G8Client(location, jwt).execute_vm_command(
        cluster.management_cluster_nodes[0].vm_id,
        "kubectl",
        [
            "--kubeconfig",
            KUBE_CONFIG_PATH,
            "-n",
            namespace,
            "patch",
            "deployment",
            deployment_name,
            "-p",
            f'{{"spec":{{"template":{{"spec":{{"containers":[{{"name":"{container_name}", '
            f'"image":"{vco.domain}/common/{image_name}:{tag}"' + (f",{liveness}}}" if liveness else "") + "}]}}}}}}",
        ],
    )
    # If we are updating rancher image we need to patch CATTLE_AGENT_IMAGE env in both containers
    if deployment_name == "cattle":
        is_dev = EnvironmentName.current() == EnvironmentName.DEV
        agent_image = f"{vco.domain if not is_dev else 'console.cloudbuilders.be'}/common/agent:{tag}"
        _update_env_variable(
            customer_id,
            rancher_id,
            namespace,
            deployment_name,
            "cattle-server",
            "CATTLE_AGENT_IMAGE",
            agent_image,
            False,
        )


def get_rancher_image(customer_id: str, rancher_id: str) -> RancherVersions:
    """Get rancher image of cluster

    Args:
        customer_id (str): Customer ID
        rancher_id (str): Management cluster ID
    Returns:
        RancherVersions: Rancher version with supported k8 clusters
    """
    _check_mgmt_cluster(customer_id, rancher_id)

    cluster = RancherManagementCluster.get_by_id(rancher_id)
    if cluster.status not in ClusterStatus.get_running_statuses():
        raise ValueError("Fetching Rancher image is only available for running clusters")
    url = "/v3/settings/server-version"
    resp = _rancher_api_get(cluster, url)
    image_tag = resp.get("value")
    if not image_tag:
        return RancherVersionStruct(tag="NA", supported_kubernetes_versions=[])
    try:
        version = RancherVersions.get_by_tag(tag=image_tag)
    except DoesNotExist:
        return RancherVersionStruct(tag=image_tag, supported_kubernetes_versions=[])
    return version


def _delete_ingress_resources(customer_id: str, jwt: str, lbs: LoadBalancerService):
    location, decoded_cloudspace_id = decode_validate_cloudspace_id(lbs.cloudspace_id)
    for lb_id in lbs.loadbalancer_ids:
        try:
            ingress_business.get_load_balancer(location, jwt, decoded_cloudspace_id, lb_id)
            ingress_business.delete_load_balancer(customer_id, location, jwt, decoded_cloudspace_id, lb_id)
        except KeyError:
            logger.warning("Loadbalancer %s not found, passing", lb_id)
    try:
        remove_cloudspace_external_network(
            customer_id,
            decoded_cloudspace_id,
            jwt,
            location,
            force=True,
            external_network_id=str(lbs.external_network_id),
            external_network_ip=lbs.external_network_ip,
        )
    except HTTPError as err:
        if err.response.status_code != 404:
            raise
    try:
        ingress_business.delete_server_pool(customer_id, location, jwt, decoded_cloudspace_id, lbs.serverpool_id)
    except KeyError:
        pass


def _delete_dangling_ingress_resources(customer_id: str, jwt: str, cloudspace_id: str, service_uid: str):
    location, decoded_cloudspace_id = decode_validate_cloudspace_id(cloudspace_id)
    for load_balancer in ingress_business.list_load_balancers(location, jwt, decoded_cloudspace_id):
        try:
            if load_balancer["name"].endswith(service_uid):
                ingress_business.delete_load_balancer(
                    customer_id, location, jwt, decoded_cloudspace_id, load_balancer["loadbalancer_id"]
                )
        except KeyError:
            pass
    for server_pool in ingress_business.list_server_pools(location, jwt, decoded_cloudspace_id):
        try:
            if server_pool.name.endswith(service_uid):
                ingress_business.delete_server_pool(
                    customer_id, location, jwt, decoded_cloudspace_id, server_pool.serverpool_id
                )
        except KeyError:
            pass


def update_jwt(jwt: str, management_cluster_id: str, vco_id: str, customer_id: str) -> UpdateClusterJwtResult:
    """Update JWt in all vms related to the management cluster

    Args:
        jwt (str): JWT
        management_cluster_id (str): Management cluster ID
        vco_id (str): VCO ID
        customer_id (str): Customer ID

    Returns:
        UpdateClusterJwtResult: List of succeeded and list of failed vms
    """
    result: TaskStatusResultFuture = _update_jwt(jwt, management_cluster_id, vco_id, customer_id)
    return result.get_result().result_value  # pylint: disable=no-member


@job(
    "Update management cluster JWT",
    object_type="jwt-update",
    object_id="jwt-update-{management_cluster_id}",
    timeout=3600,
    cleanup=_report_failure,
)
def _update_jwt(
    jwt: str, management_cluster_id: str, vco_id: str, customer_id: str
) -> Tuple[List[VirtualMachineInfoStruct], List[VirtualMachineInfoStruct]]:
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    with open(UPDATE_JWT_PATH, "r", encoding="utf8") as fh:
        template = Template(fh.read())

    command = template.render(jwt=jwt, vco_id=vco_id)

    g8_clients: Dict[str, G8Client] = {}
    vm: VirtualMachine = None
    vm_list = []
    success_vms = []
    failed_vms = []

    def _exec_vm(node: VirtualMachineInfoStruct):
        location, _ = decode_validate_cloudspace_id(node.cloudspace_id)
        if location in g8_clients:
            g8_client = g8_clients[location]
        else:
            g8_client = G8Client(location, jwt)
            g8_clients[location] = g8_client
        try:
            g8_client.execute_vm_command(node.vm_id, "python3", ["-c", command], capture_output=True, timeout=60)
            success_vms.append(node)
        except Exception:  # pylint: disable=broad-exception-caught
            failed_vms.append(node)

    for vm in cluster.management_cluster_nodes:
        vm_list.append(VirtualMachineInfoStruct(vm_id=vm.vm_id, cloudspace_id=cluster.cloudspace_id))

    vm_list += cluster.cluster_nodes
    node_chunks = [vm_list[i : i + 5] for i in range(0, len(vm_list), 5)]
    pool = Pool(size=5)
    for chunk in node_chunks:
        pool.map(_exec_vm, chunk)
    pool.join(3600)
    return UpdateClusterJwtResult(failed_vms=failed_vms, succeeded_vms=success_vms)


def get_node_driver_latest_info(vco_id: str) -> NodeDriverInfoStruct:
    """Get latest version of node driver

    Args:
        vco_id (str): VCO ID

    Returns:
        NodeDriverInfoStruct: Node driver info
    """
    vco = VCO.get_by_id(vco_id)
    nd_name = _get_node_driver_name(vco)
    version, checksum = get_latest_node_driver_version_sha()
    is_dev = EnvironmentName.current() == EnvironmentName.DEV
    scheme = "http" if is_dev else "https"
    url = f"{scheme}://{vco.domain}/api/1/alpha/rancher/node-driver/{version}/docker-machine-driver-{nd_name}"
    return NodeDriverInfoStruct(url=url, version=version, checksum=checksum, name=nd_name)


@job(
    "Update management cluster node driver",
    object_type="node-driver-update",
    object_id="node-driver-update",
    timeout=3600,
    cleanup=_report_failure,
)
def update_node_driver() -> None:
    """Update node driver for all management clusters"""
    clusters = RancherManagementCluster.list(statuses=[ClusterStatus.RUNNING])
    for cluster in clusters:
        logger.info(
            "Checking and updating node driver for managemnt cluster: %s (%s) at %s",
            cluster.name,
            cluster.management_cluster_id,
            cluster.vco_id,
        )
        location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
        if not cluster.status == ClusterStatus.RUNNING:
            logger.info(
                "Cluster: %s (%s) at %s is not running!",
                cluster.name,
                cluster.management_cluster_id,
                cluster.vco_id,
            )
            continue
        node_driver_info = get_node_driver_latest_info(cluster.vco_id)

        try:
            logger.info(
                "Updating node version for Cluster: %s (%s) at %s",
                cluster.name,
                cluster.management_cluster_id,
                cluster.vco_id,
            )
            vco = VCO.get_by_id(cluster.vco_id)
            nd_name = _get_node_driver_name(vco)
            vm_id = cluster.management_cluster_nodes[0]["vm_id"]
            node_driver_info = get_node_driver_latest_info(cluster.vco_id)
            command = "kubectl"
            args = [
                "--kubeconfig",
                KUBE_CONFIG_PATH,
                "patch",
                "nodedriver",
                nd_name,
                "--type",
                "merge",
                "-p",
                f'{{"spec": {{"url": "{node_driver_info.url}", "checksum": "{node_driver_info.checksum}"}}}}',
            ]
            g8_client = G8Client(location, get_g8_jwt_from_db(location))
            g8_client.execute_vm_command(vm_id, command, args)
            logger.info(
                "Node version for Cluster: %s (%s) at %s is updated successfuly!",
                cluster.name,
                cluster.management_cluster_id,
                cluster.vco_id,
            )
        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception(
                "Failed to update Node version for Cluster: %s (%s) at %s.",
                cluster.name,
                cluster.management_cluster_id,
                cluster.vco_id,
            )


@job(
    "Update cluster Cloud Controller Managers to version {version}",
    object_type="ccms-update",
    object_id="ccms-update",
    timeout=3600,
    cleanup=_report_failure,
)
def update_ccms(version: str = "latest") -> None:
    """Updates the cluster controller manager Daemonset to the specified version

    Args:
        version (str, optional): Container version. Defaults to "latest".
    """
    clusters = RancherManagementCluster.list(statuses=[ClusterStatus.RUNNING])
    pool = Pool(size=10)
    failures = []
    success = []

    def _proccess_update_ccm(cluster):
        logger.info(
            "Checking and updating ccms for managemnt cluster: %s (%s) at %s",
            cluster.name,
            cluster.management_cluster_id,
            cluster.vco_id,
        )
        vco_domain: str
        if EnvironmentName.current() == EnvironmentName.DEV:
            vco_domain = "console.cloudbuilders.be"
        else:
            vco: VCO = VCO.get_by_id(cluster.vco_id)
            vco_domain = vco.domain
        try:
            k8s_clusters = get_kubernetes_clusters(cluster.management_cluster_id, cluster.customer_id)
        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception(
                "Failed to get Kubernetes Clusters for Mgmt cluster %s (%s) for customer %s at %s",
                cluster.name,
                cluster.management_cluster_id,
                cluster.customer_id,
                cluster.vco_id,
            )
            failures.append(
                "Failed to update the clusters controller manager in the k8s clusters for Mgmt cluster "
                f"{cluster.name} ({cluster.management_cluster_id}) for customer {cluster.customer_id} at "
                f"{cluster.vco_id}"
            )
            return
        for k8s_cluster in k8s_clusters:
            logger.info(
                "Updating Cloud Controller Manager for cluster: %s (%s) for customer %s at %s",
                k8s_cluster.name,
                k8s_cluster.id,
                cluster.customer_id,
                cluster.vco_id,
            )
            url = f"/k8s/clusters/{k8s_cluster.id}/v1/apps.daemonsets/kube-system/cloud-controller-manager"
            try:
                ccm_ds = _rancher_api_get(cluster, url)
                annotations = ccm_ds["spec"]["template"]["metadata"].setdefault("annotations", {})
                annotations["cloud-controller-manager/updateTimestamp"] = f"{int(time())}"
                ccm_image = f"{vco_domain}/common/ccm:{version}"
                ccm_ds["spec"]["template"]["spec"]["containers"][0]["image"] = ccm_image
                _rancher_api_put(cluster, url, ccm_ds)
                logger.info(
                    "Successfully updated Cloud Controller Manager for cluster: %s (%s) for customer %s at %s",
                    k8s_cluster.name,
                    k8s_cluster.id,
                    cluster.customer_id,
                    cluster.vco_id,
                )
                success.append(
                    f"Successfully updated Cloud Controller Manager for cluster: {k8s_cluster.name}({k8s_cluster.id})"
                    f"for customer {cluster.customer_id} at {cluster.vco_id}"
                )
            except Exception:  # pylint: disable=broad-exception-caught
                logger.exception(
                    "Failed to Update Cloud Controller Manager for cluster: %s (%s) for customer %s at %s",
                    k8s_cluster.name,
                    k8s_cluster.id,
                    cluster.customer_id,
                    cluster.vco_id,
                )
                failures.append(
                    "Failed to update the clusters controller manager in the k8s cluster "
                    f"{k8s_cluster.name}({k8s_cluster.id}) managed by Mgmt cluster "
                    f"{cluster.name} ({cluster.management_cluster_id}) for customer {cluster.customer_id} at "
                    f"{cluster.vco_id}"
                )

    for cluster in clusters:
        pool.spawn(_proccess_update_ccm, cluster)
    logger.info("==================================================================================")
    logger.info("Successfully updated:")
    for succ in success:
        logger.info(succ)
    logger.info("==================================================================================")
    logger.info("Failed to update:")
    for fail in failures:
        logger.error(fail)


@job(
    "Install or update rancher-agent to version {version}",
    object_type="rancher-agent-update",
    object_id="rancher-agent-update",
    timeout=3600,
    cleanup=_report_failure,
)
def update_rancher_agent(version: str = "latest") -> None:
    """Install or update rancher agents

    Args:
        version (str, optional): Container version. Defaults to "latest".
    """

    clusters = RancherManagementCluster.list(statuses=[ClusterStatus.RUNNING])
    pool = Pool(size=10)  # Adjust the pool size according to your need
    failures = []
    success = []

    def get_jwt(cluster: RancherManagementCluster):
        cluster_node: VirtualMachine = cluster.management_cluster_nodes[0]
        location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
        g8_client: G8Client = G8Client(location, get_g8_jwt_from_db(location))
        config = yaml.load(
            base64.b64decode(
                g8_client.read_vm_file(cluster_node.vm_id, 2 * 1024 * 1024, f"/etc/{cluster.vco_id}/g8s.cfg")["result"]
            ),
            yaml.SafeLoader,
        )
        return config["jwt"]

    def _process_update_cluster_agent(cluster):
        try:
            logger.info(
                "Installing or updating rancher-agent for management cluster: %s (%s) at %s",
                cluster.name,
                cluster.management_cluster_id,
                cluster.vco_id,
            )
            vco: VCO = VCO.get_by_id(cluster.vco_id)
            # Create namespace if needed
            system_namespace = _get_system_namespace(vco)
            response = _rancher_api_get(cluster, "/v1/namespaces")
            for namespace in response["data"]:
                if namespace["id"] == system_namespace:
                    break
            else:
                _rancher_api_post(
                    cluster,
                    "/v1/namespaces",
                    {
                        "apiVersion": "v1",
                        "kind": "Namespace",
                        "metadata": {"name": system_namespace},
                        "spec": {},
                        "status": {},
                    },
                )
            # Create or update deployment
            rancher_agent_deployment_id = f"{system_namespace}/rancher-agent"
            response = _rancher_api_get(cluster, "/v1/apps.deployments")
            for deployment in response["data"]:
                if deployment["id"] == rancher_agent_deployment_id:
                    # Update deployment
                    deployment["spec"]["template"]["spec"]["containers"][0][
                        "image"
                    ] = f"{vco.domain}/common/rancher-agent:{version}"
                    deployment["spec"]["template"]["spec"]["containers"][0]["command"][7] = get_jwt(cluster)
                    liveness_probe = {
                        "exec": {"command": ["/bin/bash", "-c", "/usr/local/bin/liveness.sh"]},
                        "initialDelaySeconds": 60,
                        "timeoutSeconds": 3,
                        "periodSeconds": 10,
                        "successThreshold": 1,
                        "failureThreshold": 12,
                    }
                    # Add the liveness probe to the container specification
                    deployment["spec"]["template"]["spec"]["containers"][0]["livenessProbe"] = liveness_probe

                    _rancher_api_put(cluster, f"/v1/apps.deployments/{system_namespace}/rancher-agent", deployment)
                    logger.info(
                        "Successfully updated the rancher-agent in management cluster: %s (%s) for customer %s at %s",
                        cluster.name,
                        cluster.management_cluster_id,
                        cluster.customer_id,
                        cluster.vco_id,
                    )
                    success.append(
                        "Successfully updated the rancher-agent in management cluster: "
                        f"{cluster.name} ({cluster.management_cluster_id}) for customer"
                        f" {cluster.customer_id} at {cluster.vco_id}"
                    )
                    return
            rancher_agent = _apply_template_to_yaml(
                RANCHER_AGENT_PATH,
                vco_domain=vco.domain,
                iam_domain=vco.iam_domain,
                system_namespace=system_namespace,
                jwt=get_jwt(cluster),
                management_cluster_id=cluster.management_cluster_id,
                customer_id=cluster.customer_id,
            )
            _rancher_api_post(cluster, "/v1/apps.deployments", rancher_agent)
            logger.info(
                "Successfully installed the rancher-agent in management cluster: %s (%s) for customer %s at %s",
                cluster.name,
                cluster.management_cluster_id,
                cluster.customer_id,
                cluster.vco_id,
            )
            success.append(
                "Successfully installed the rancher-agent in management cluster: "
                f"{cluster.name} ({cluster.management_cluster_id})"
                f" for customer {cluster.customer_id} at {cluster.vco_id}"
            )
        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception(
                "Failed to install or update the rancher-agent in Mgmt cluster %s (%s) for customer %s at %s",
                cluster.name,
                cluster.management_cluster_id,
                cluster.customer_id,
                cluster.vco_id,
            )
            logger.info("Retrying with force update (Executing command directly on the VM)")
            try:
                liveness = '"livenessProbe":{"exec":{ "command":["/bin/bash","-c","/usr/local/bin/liveness.sh"]}, "initialDelaySeconds":60, "timeoutSeconds":3, "periodSeconds":10, "successThreshold":1, "failureThreshold":12'  # pylint: disable=line-too-long # noqa: E501

                update_rancher_image(
                    customer_id=cluster.customer_id,
                    rancher_id=cluster.management_cluster_id,
                    tag=version,
                    namespace=system_namespace,
                    deployment_name="rancher-agent",
                    image_name="rancher-agent",
                    container_name="agent",
                    liveness=liveness,
                )
            except Exception:  # pylint: disable=broad-exception-caught
                failures.append(
                    "Failed to install or update the rancher-agent in Mgmt cluster "
                    f"{cluster.name} ({cluster.management_cluster_id}) for customer {cluster.customer_id} at "
                    f"{cluster.vco_id}"
                )
                vco_id = cluster.vco_id
                customer_id = cluster.customer_id
                error_message = """Updating rancher agent failed"""
                metadata = dict(
                    customer_id=customer_id,
                    vco_id=vco_id,
                    management_cluster_id=str(cluster.management_cluster_id),
                    cloudspace_id=cluster.cloudspace_id,
                )
                file_issue(error_message, IssueResourceTypes.RANCHER, metadata, vco_id, customer_id)

    for cluster in clusters:
        pool.spawn(_process_update_cluster_agent, cluster)
    pool.join()

    logger.error("==================================================================================")
    logger.info("Successfully installed or updated:")
    for succ in success:
        logger.error(succ)
    logger.error("==================================================================================")
    logger.error("Failed to install or update:")
    for fail in failures:
        logger.error(fail)


def get_kubernetes_resources(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str, kubernetes_cluster_id: str
) -> ClusterConnectedResources:
    """Get all kubenetes cluster resources (vms, ingress and dns)

    Args:
        jwt (str): JWT
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        management_cluster_id (str): Management cluster ID
        kubernetes_cluster_id (str): Kubernetes cluster ID

    Returns:
        ClusterConnectedResources: Cluster connected resources
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    db_cluster: KubernetesCluster = cluster.kubernetes_clusters.get(kubernetes_cluster_id)
    if not db_cluster:
        return ClusterConnectedResources(ingress_resources=[], vms=[], dns_records=[])
    lbs = db_cluster.load_balancer_services
    vms = []
    dns_records = []
    node_pool: NodePool = None
    node: RancherManagementClusterKubernetesClusterNodePoolNodeStruct = None
    for node_pool in db_cluster.node_pools:
        for node in list_node_pool_nodes(
            jwt, vco_id, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool.node_pool_id
        ):
            g8_name, _ = decode_validate_cloudspace_id(node.cloudspace_id)
            dns_records += DnsRecord.list(g8_name=g8_name, vm_id=node.vm_id)
            vms.append(VirtualMachineSimpleInfoStruct(vm_id=node.vm_id, vm_name=node.vm_name, cs_id=node.cloudspace_id))
    lb: LoadBalancerService = None
    for lb in lbs:
        g8_name, cs_id = decode_validate_cloudspace_id(lb.cloudspace_id)
        dns_records += DnsRecord.list(
            g8_name=g8_name, cloudspace_id=cs_id, cs_only=True, value=lb.external_network_ip.split("/")[0]
        )
    return ClusterConnectedResources(ingress_resources=lbs, vms=vms, dns_records=dns_records)


def get_management_cluster_resources(
    jwt: str, vco_id: str, customer_id: str, management_cluster_id: str
) -> ClusterConnectedResources:
    """Get all cluster resources (vms, ingress and dns)

    Args:
        jwt (str): JWT
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        management_cluster_id (str): Managememt cluster ID

    Returns:
        ClusterConnectedResources: cluster connected Resources
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id, vco_id)
    lbs = []
    vms = []
    dns_records = []
    node: ClusterNode = None
    node: RancherManagementClusterKubernetesClusterNodePoolNodeStruct = None
    g8_name, cloudspace_id = decode_validate_cloudspace_id(cluster.cloudspace_id)
    g8_client = G8Client(g8_name, jwt)
    for mgmt_cluster_vm in cluster.management_cluster_nodes:
        cs_vms = g8_client.list_vms(cloudspace_id)
        for vm in cs_vms:
            if vm["vm_id"] == mgmt_cluster_vm.vm_id:
                vms.append(
                    VirtualMachineSimpleInfoStruct(vm_id=vm["vm_id"], vm_name=vm["name"], cs_id=cluster.cloudspace_id)
                )
                break

    for node in cluster.cluster_nodes:
        dns_records += DnsRecord.list(g8_name=g8_name, vm_id=node.vm_id)
        vms.append(VirtualMachineSimpleInfoStruct(vm_id=node.vm_id, vm_name=node.name, cs_id=node.cloudspace_id))
    lb: LoadBalancerService = None
    kube_clusters = cluster.kubernetes_clusters
    for cluster_id in kube_clusters:
        lbs += kube_clusters[cluster_id].load_balancer_services
        for lb in kube_clusters[cluster_id].load_balancer_services:
            g8_name, cs_id = decode_validate_cloudspace_id(lb.cloudspace_id)
            dns_records += DnsRecord.list(
                g8_name=g8_name, cloudspace_id=cs_id, cs_only=True, value=lb.external_network_ip.split("/")[0]
            )
    return ClusterConnectedResources(ingress_resources=lbs, vms=vms, dns_records=dns_records)


def _remove_empty_lines(text: str):
    return "\n".join([line for line in text.split("\n") if line.strip()])


def update_certificate_secret(
    certificate: CustomerSSLCertificate, management_cluster_id: str, jwt: str, vco_id: str
) -> None:
    """Update management cluster certificate secret

    Args:
        certificate (CustomerSSLCertificate): SSL Certificate
        management_cluster_id (str): management cluster id
        jwt (str): JWT
        vco_id (str): VCO ID
    """
    cluster = _check_mgmt_cluster(certificate.customer_id, management_cluster_id, vco_id)
    with open(UPDATE_CERTIFICATE_PATH, "r", encoding="utf8") as file_handle:
        template = Template(file_handle.read())
    key: str = _remove_empty_lines(certificate.key)
    crt: str = _remove_empty_lines(certificate.crt)
    encoded_key = base64.b64encode(key.encode()).decode()
    encoded_crt = base64.b64encode(crt.encode()).decode()

    command = template.render(crt=encoded_crt, key=encoded_key)
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    g8_client = G8Client(location, jwt)
    g8_client.execute_vm_command(
        cluster.management_cluster_nodes[0].vm_id, "python3", ["-c", command], capture_output=True, timeout=60
    )


def _validate_clousdpaces_connection(jwt: str, customer_id: str, cloudspace_ids: List[str]) -> bool:
    for cs_id in cloudspace_ids:
        loc, decoded_cs_id = decode_validate_cloudspace_id(cs_id)
        connected_cloudspaces = list_remote_cloudspaces(customer_id, loc, decoded_cs_id, jwt)
        connected_cloudspace_ids = [c["cloudspace_id"] for c in connected_cloudspaces]
        return all(other_cs_id == cs_id or other_cs_id in connected_cloudspace_ids for other_cs_id in cloudspace_ids)


def download_agent_package() -> bytes:
    """Fetch and download latest agent package

    Returns:
        bytes: UI Resource
    """
    minio_class = MinioQaDevConnection if EnvironmentName.current() == EnvironmentName.DEV else ExternalMinioConnection
    minio_client = minio_class.get_client()
    latest_package, _ = _get_latest_resource(minio_client, K8S_AGENT_VERSION_PATTERN)
    package = minio_client.get_object(K8S_RESOURCES_BUCKET, latest_package)
    return package.data


def _report_failed_certificate_update(*args, **kwargs) -> None:  # pylint: disable=unused-argument
    cluster: RancherManagementCluster = args[2]
    collection_timestamp = args[3]
    context = get_context()
    url = get_meneja_url()
    update_certificates = UpdateClusterCertificate.get_by_id(collection_timestamp)
    update_certificates.failed_jobs.append(
        FailedUpdateClusterCertificate(
            domain=cluster.domain_name,
            customer_id=cluster.customer_id,
            vco_id=cluster.vco_id,
            workflow_id=context.status.id,
            management_cluster_id=str(cluster.management_cluster_id),
        )
    )
    if not cluster.cluster_health_checks:
        cluster.cluster_health_checks = ClusterHealthChecks()
    cluster.cluster_health_checks.certificate_health_check = ClusterHealthCheck(
        status=ClusterCertificateStatus.EXPIRED.value, last_run=collection_timestamp
    )
    update_certificates.save()
    cluster.save()

    message = f"""Checking/Updating rke certificate failed. for cluster
    {cluster.name} ({cluster.management_cluster_id}) at
    {cluster.vco_id}
        Check workflow logs here: {url}/workflows/{context.status.id}"""
    rocket_chat_message(channel_id=rocket_chat_channel(), message=message)
    vco_id = cluster.vco_id
    customer_id = cluster.customer_id
    error_message = """Checking/Updating rke certificate failed"""
    metadata = dict(
        customer_id=customer_id,
        vco_id=vco_id,
        management_cluster_id=str(cluster.management_cluster_id),
        cloudspace_id=cluster.cloudspace_id,
    )
    file_issue(error_message, IssueResourceTypes.RANCHER, metadata, vco_id, customer_id)


@schedule(cron="0 3 * * *", description="Update nearly expired tls certificates")
def check_and_update_certificate():
    """Check and update rke2 cluster certificate"""

    now = time()
    logger.info("Collection timestamp: %s", now)
    collection_timestamp = int(time())
    update_certificates_collection = UpdateClusterCertificate()
    update_certificates_collection.timestamp = collection_timestamp
    update_certificates_collection.failed_jobs = []
    update_certificates_collection.save()

    clusters = RancherManagementCluster.list(statuses=[ClusterStatus.RUNNING])
    for cluster in clusters:
        if cluster.ssl_certificate_source == SSLCertificateSource.LETS_ENCRYPT.value:
            for priv_net in PRIVATE_NETWORKS:
                if IPAddress(cluster.external_network_ip) in priv_net:
                    _check_and_update_certificate(cluster, collection_timestamp)
        else:
            _check_and_update_certificate(cluster, collection_timestamp)


@job(
    title="Check and update cluster certificate",
    cleanup=_report_failed_certificate_update,
    object_id="check_and_update_rancher_certificate",
    object_type="check_and_update_rancher_certificate",
)
def _check_and_update_certificate(cluster: RancherManagementCluster, collection_timestamp: int) -> None:
    logger.info(
        "Checking and updating certs for management cluster: %s (%s) at %s",
        cluster.name,
        cluster.management_cluster_id,
        cluster.vco_id,
    )
    if not cluster.cluster_health_checks:
        cluster.cluster_health_checks = ClusterHealthChecks()
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    g8_client = G8Client(location, get_g8_jwt_from_db(location))
    result = g8_client.execute_vm_command(
        cluster.management_cluster_nodes[0].vm_id,
        "bash",
        ["-c", GET_CERT_CMD],
        capture_output=True,
        timeout=60,
    )
    result = base64.b64decode(result["result"]).decode()
    secret_data = json.loads(result)
    encoded_crt = base64.b64decode(secret_data["data"]["tls.crt"]).decode()
    if cluster.ssl_certificate_source == SSLCertificateSource.CERTIFICATE_STORE:
        cert = CustomerSSLCertificate.get_by_domain(domain=cluster.domain_name, customer_id=cluster.customer_id)
    else:
        cert = CustomerSSLCertificate.get_by_domain(
            domain=cluster.domain_name,
            customer_id=cluster.customer_id,
            source=SSLCertificateSource.LETS_ENCRYPT.value,
        )
    try:
        validate_not_expired(encoded_crt)
        expired = False
    except SecurityValidationError:
        expired = True
    if not cert:
        if not expired:
            cluster.cluster_health_checks.certificate_health_check = ClusterHealthCheck(
                status=ClusterCertificateStatus.UPDATED.value, last_run=collection_timestamp
            )
        else:
            raise SecurityValidationError("Cluster certificate is expired")
    elif encoded_crt != cert.crt:
        if expired:
            if cert.expires_at - collection_timestamp > 0:
                logger.info(
                    "Certificate for cluster: %s (%s) at %s does not match customer certificate for domain %s",
                    cluster.name,
                    cluster.management_cluster_id,
                    cluster.vco_id,
                    cluster.domain_name,
                )
                update_certificate_secret(
                    cert, cluster.management_cluster_id, get_g8_jwt_from_db(location), cluster.vco_id
                )
                cluster.cluster_health_checks.certificate_health_check = ClusterHealthCheck(
                    status=ClusterCertificateStatus.UPDATED.value, last_run=collection_timestamp
                )
            else:
                raise SecurityValidationError("Customer certificate is expired")
        else:
            cluster.cluster_health_checks.certificate_health_check = ClusterHealthCheck(
                status=ClusterCertificateStatus.NOT_MATCHING.value, last_run=collection_timestamp
            )
            cluster.save()
    else:
        if not expired:
            logger.info(
                "Cluster: %s (%s) at %s certificate is already updated",
                cluster.name,
                cluster.management_cluster_id,
                cluster.vco_id,
            )
            cluster.cluster_health_checks.certificate_health_check = ClusterHealthCheck(
                status=ClusterCertificateStatus.UPDATED.value, last_run=collection_timestamp
            )
        else:
            raise SecurityValidationError("Customer and cluster certificate are expired")
    cluster.save()


def add_rancher_version(rancher_version: RancherVersionStruct) -> None:
    """Add rancher version

    Args:
        rancher_version (RancherVersionsStruct): Rancher version object
    """
    version = RancherVersions()
    version.tag = rancher_version.tag
    version.supported_kubernetes_versions = rancher_version.supported_kubernetes_versions
    return version.save()


def get_rancher_versions() -> List["RancherVersionsGetStruct"]:
    """Get rancher versions

    Returns:
        List[RancherVersions] : List of rancher versions
    """
    versions_list = RancherVersions.list()
    default_settings = RancherAsAServiceSettings.get_settings()
    result = []
    for version in versions_list:
        result.append(
            RancherVersionsGetStruct(
                tag=version.tag,
                supported_kubernetes_versions=version.supported_kubernetes_versions,
                default=version.tag == default_settings.default_version_tag,
            )
        )
    return result


@job(
    "Update rancher image from {old_tag} to {new_tag}",
    object_type="rancher-image-update",
    object_id="rancher-image-update",
    timeout=3600,
    cleanup=_report_failure,
)
def update_all_rancher_images(old_tag: str, new_tag: str) -> None:
    """Update all rancher images

    Args:
        old_tag (str): Current version of cluster
        new_tag (str): targer version of cluster
    """
    pool = Pool(size=10)
    clusters = RancherManagementCluster.list(statuses=[ClusterStatus.RUNNING, ClusterStatus.RESIZING])

    def process_update_rancher_image(cluster: RancherManagementCluster):
        logger.info(
            "Checking and updating rancher image for managemnt cluster: %s (%s) at %s",
            cluster.name,
            cluster.management_cluster_id,
            cluster.vco_id,
        )
        try:
            tag = get_rancher_image(cluster.customer_id, cluster.management_cluster_id).tag
            if tag == old_tag:
                update_rancher_image(
                    cluster.customer_id,
                    cluster.management_cluster_id,
                    new_tag,
                    namespace="cattle-system",
                    deployment_name="cattle",
                    image_name="rancher",
                    container_name="cattle-server",
                )
                logger.info(
                    "Successfully update rancher image for managemnt cluster: %s (%s) at %s for customer %s",
                    cluster.name,
                    cluster.management_cluster_id,
                    cluster.vco_id,
                    cluster.customer_id,
                )
        except Exception:  # pylint: disable=broad-exception-caught
            logger.exception(
                "Failed to get/update rancher image for Mgmt cluster %s (%s) for customer %s at %s",
                cluster.name,
                cluster.management_cluster_id,
                cluster.customer_id,
                cluster.vco_id,
            )

    for cluster in clusters:
        pool.spawn(process_update_rancher_image, cluster)
    pool.join()


def get_management_cluster(customer_id: str, management_cluster_id: str) -> RancherManagementClusterStruct:
    """Get rancher management cluster

    Args:
        customer_id (str): Customer ID
        management_cluster_id (str): Management cluster ID

    Returns:
        RancherManagementClusterStruct: Rancher managemnt cluster
    """
    cluster = _check_mgmt_cluster(customer_id, management_cluster_id)
    version = RancherVersionStruct(tag=None, supported_kubernetes_versions=[])
    if cluster.status == ClusterStatus.RUNNING:
        try:
            version = get_rancher_image(customer_id, management_cluster_id)
        except Exception:  # pylint: disable=broad-exception-caught
            pass
    return RancherManagementClusterStruct(
        name=cluster.name,
        domain_name=cluster.domain_name,
        external_network_ip=cluster.external_network_ip,
        cluster_type=ManagementClusterType.from_string(cluster.cluster_type),
        ssl_certificate_source=SSLCertificateSource.from_string(cluster.ssl_certificate_source),
        management_cluster_nodes=cluster.management_cluster_nodes,
        management_cluster_id=management_cluster_id,
        status=cluster.status,
        rancher_version=version,
        tag=version.tag,
        cloudspace_id=cluster.cloudspace_id,
        letsencrypt_email=cluster.letsencrypt_emailaddress,
        physical_storage=cluster.physical_storage,
    )


def _update_env_variable(
    customer_id: str,
    rancher_id: str,
    namespace: str,
    deployment_name: str,
    container_name: str,
    env_var_name: str,
    env_var_value: str,
    is_init_container: bool = False,  # Set to True if updating an init container
) -> None:
    """Update an environment variable in a Kubernetes Deployment.

    Args:
        customer_id (str): Customer ID
        rancher_id (str): Rancher Management Cluster ID
        namespace (str): Kubernetes namespace
        deployment_name (str): Deployment name
        container_name (str): Container name in the deployment
        env_var_name (str): Name of the environment variable
        env_var_value (str): New value for the environment variable
        is_init_container (bool): Set to True if modifying an init container
    """
    # Ensure cluster is running
    _check_mgmt_cluster(customer_id, rancher_id)
    cluster = RancherManagementCluster.get_by_id(rancher_id)
    if cluster.status not in ClusterStatus.get_running_statuses():
        raise ValueError("Updating environment variable is only allowed for running clusters")

    # Get the Kubernetes API client
    location, _ = decode_validate_cloudspace_id(cluster.cloudspace_id)
    jwt = get_g8_jwt_from_db(location)
    client = G8Client(location, jwt)

    # Choose the correct path based on container type
    container_type = "initContainers" if is_init_container else "containers"

    # Define the patch JSON
    patch_data = {
        "spec": {
            "template": {
                "spec": {
                    container_type: [
                        {
                            "name": container_name,
                            "env": [
                                {
                                    "name": env_var_name,
                                    "value": env_var_value,
                                }
                            ],
                        }
                    ]
                }
            }
        }
    }

    # Convert patch data to JSON string
    patch_json = json.dumps(patch_data)

    # Execute kubectl patch command
    client.execute_vm_command(
        cluster.management_cluster_nodes[0].vm_id,
        "kubectl",
        [
            "--kubeconfig",
            KUBE_CONFIG_PATH,
            "-n",
            namespace,
            "patch",
            "deployment",
            deployment_name,
            "-p",
            patch_json,
        ],
    )


def _validate_cluster_nodes_health(management_cluster: RancherManagementCluster) -> None:
    nodes = _rancher_api_get(management_cluster, "/v1/nodes")["data"]
    for node in nodes:
        if "Ready" not in node["metadata"]["fields"]:
            raise ValueError("One or more nodes in the management cluster are not ready!")


def _pods_are_running(
    management_cluster: RancherManagementCluster, replica_type: str, replica_name: str, namespace: str
) -> None:
    try:
        status = _rancher_api_get(management_cluster, f"/v1/apps.{replica_type}/{namespace}/{replica_name}").get(
            "status", {}
        )
    except Exception as exp:
        raise ValueError("Failed to get pods status") from exp
    if not (
        status.get("availableReplicas", 0) == status.get("replicas", 0)
        and status.get("readyReplicas") == status.get("replicas", 0)
        and status.get("replicas", 0) > 0
    ):
        raise ValueError(f"Pods of {replica_type}: {replica_name} are not ready!")


def _validate_k8_cluster_nodes_health(management_cluster: RancherManagementCluster, kubernetes_cluster_id: str) -> None:
    nodes = _rancher_api_get(management_cluster, f"/v3/nodes?worker=false&clusterId={kubernetes_cluster_id}")["data"]
    for node in nodes:
        if node.get("state") != "active":
            raise ValueError("One or more master nodes in the kubernetes cluster are not ready!")


def uninstall_ingress_controller(
    vco_id: str, management_cluster_id: str, jwt: str, k8_cluster_id: str, node_pool_id: str
) -> None:
    """Uninstall ingress controller

    Args:
        vco_id (str): VCO ID
        management_cluster_id (str): Management cluster ID
        jwt (str): JWT
        k8_cluster_id (str): Kubernetes cluster ID
        node_pool_id (str): Node pool ID
    """
    management_cluster = RancherManagementCluster.get_by_id(management_cluster_id)
    node_pool_name = _get_node_pool_names(
        jwt, vco_id, management_cluster.customer_id, management_cluster_id, k8_cluster_id, [node_pool_id]
    )
    kub: KubernetesCluster = management_cluster.kubernetes_clusters.get(k8_cluster_id)
    if kub is not None:
        db_node_pool: NodePool = None
        for db_node_pool in kub.node_pools:
            if db_node_pool.name == node_pool_name:
                if not db_node_pool.ingress_controller_installed:
                    return
                break
    result: TaskStatusResultFuture = _uninstall_helm_package(
        jwt,
        management_cluster_id,
        k8_cluster_id,
        _get_system_namespace(vco_id),
        _get_node_pool_ingress_app_name(node_pool_name),
        BITNAMI_CHARTS,
    )

    _ = result.get_result(timeout=3600).result_value  # pylint: disable=no-member
    db_node_pool.ingress_controller_installed = False
    management_cluster.save()
