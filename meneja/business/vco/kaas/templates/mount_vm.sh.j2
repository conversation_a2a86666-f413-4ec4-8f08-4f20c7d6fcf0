#!/bin/bash
set -e

cat > /root/init.sh <<EOF_INIT
#!/bin/bash
set -e

echo "[+] Disabling auto os update during provisioning ..."
cat > /etc/apt/apt.conf.d/20auto-upgrades <<EOF_AUTO_UPDATE
APT::Periodic::Update-Package-Lists "0";
APT::Periodic::Download-Upgradeable-Packages "0";
APT::Periodic::AutocleanInterval "0";
APT::Periodic::Unattended-Upgrade "1";
EOF_AUTO_UPDATE

echo "[+] Waiting for cloud-init ..."
cloud-init status --wait

{% if IS_DEV %}
echo "{{DEV_ENV_VPN_IP_ADDRESS}} cairo-cloud.eg.local iam.cairo-cloud.eg.local storage.meneja-dev.gig.tech.local" >> /etc/cloud/templates/hosts.debian.tmpl{% if IS_CLUSTER %}
mkdir -p /etc/docker
cat > /etc/docker/daemon.json <<EOF_DOCKER
{
  "insecure-registries" : ["cairo-cloud.eg.local", "storage.meneja-dev.gig.tech.local"]
}
EOF_DOCKER{% endif %}
cat > /usr/local/share/ca-certificates/iam-cairo-cloud-eg-local.crt <<EOF_CERT
{{ IAM_CERTIFICATE }}
EOF_CERT
update-ca-certificates
{% endif %}

echo "[+] Installing k8s agent ..."
mkdir /var/kubernetes-agent
cd /var/kubernetes-agent
wget -O {{PACKAGE_NAME}} "{{PACKAGE_URL}}"

echo "[+] Extracting kubernetes agent ..."
tar -xf {{PACKAGE_NAME}}

echo "[+] Wait until dpkg is availble ..."
DPKG_LOCKED=`lsof /var/lib/dpkg/lock-frontend | wc -l`
while [ \${DPKG_LOCKED} -ne '0' ]
do 
  sleep 1
  DPKG_LOCKED=$(( `lsof /var/lib/dpkg/lock-frontend | wc -l` + `lsof /var/lib/dpkg/lock | wc -l` + `lsof /var/lib/apt/lists/lock | wc -l` + `lsof /var/cache/apt/archives/lock | wc -l` ))
done

echo "[+] Creating virtual environment for agent and installing required libraries ..."
DEBIAN_FRONTEND=noninteractive apt-get -o DPkg::Lock::Timeout=300 update
DEBIAN_FRONTEND=noninteractive apt-get -o DPkg::Lock::Timeout=300 -y install python3-venv jq nfs-common
mkdir /usr/lib/k8sagent
python3 -m venv /usr/lib/k8sagent

echo "[+] Installing package ..."
cd {{DIR_NAME}}
/usr/lib/k8sagent/bin/pip install --upgrade pip setuptools wheel
/usr/lib/k8sagent/bin/pip install .
rm -rf /var/kubernetes-agent
cd /

echo "[+] Setting Up Config Directory ..."
mkdir /etc/{{VCO_ID}}/
mkdir /etc/docker-machine-driver
echo "jwt: {{JWT}}
iam_pub_key: |
  {{IAM_PUB_KEY | indent(2)}}
iam_url: {{IAM_URL}}
vco_api_url: {{ VCO_API_URL }}
location: {{ LOCATION }}
management_cluster_id: {{ MANAGEMENT_CLUSTER_ID }}
customer_id: {{ CUSTOMER_ID }}
vm_id: {{ VM_ID }}
cloudspace_id: {{ CLOUDSPACE_ID }}
data_disk: {{ DATA_DISK }}
boot_disk: {{ BOOT_DISK }}
is_management: {{ IS_CLUSTER }}
"> /etc/{{VCO_ID}}/g8s.cfg

echo '{"JwtToken": "{{ JWT }}", "CloudAPIUrl": "{{ VCO_API_URL }}", "RKEClusterMgmtID": "{{ MANAGEMENT_CLUSTER_ID }}", "APITimeout": 600, "CustomerID": "{{ CUSTOMER_ID }}"}' > /etc/docker-machine-driver/config.json

echo "[+] Configure agent in crontab ..."
su -c "(crontab -l; echo '*/5 * * * * /usr/lib/k8sagent/bin/g8s-agent {{VCO_ID}} >> /var/log/g8s-agent.log 2>&1') | sort -u | crontab -" root
echo "/var/log/g8s-agent.log {
  rotate 12
  monthly
  compress
  missingok
  notifempty
}
"> /etc/logrotate.d/g8s-agent

cd /
echo "[+] mount /dev/vdb1 at /mnt ..."
mount /dev/vdb1 /mnt

echo "[+] Backing up original /var to /mnt ..."
set +e
rsync --noatime -aqxPW /var/* /mnt
set -e
{%if IS_CLUSTER %}
pushd /mnt/lib/docker
tar xzf /root/docker-lib.tgz
rm /root/docker-lib.tgz
popd{% endif %}

echo "[+] Setup sticky bit for /var/tmp ..."
chmod 1777 /mnt/tmp

echo "[+] Edit /etc/fstab to make mount persistent ..."
echo "/dev/vdb1 /var ext4   defaults,discard 0  2" >> /etc/fstab

echo "[+] Mount /run/k3s to /var/k3s ..."
mkdir /mnt/k3s
chmod 711 /mnt/k3s
mkdir /run/k3s
chmod 711 /run/k3s
echo "/var/k3s /run/k3s  none defaults,bind 0 0" >> /etc/fstab
EOF_INIT
chmod +x /root/init.sh

touch /root/init.result
touch /root/init.logs
cat > /root/run_init.sh <<EOF_RUN_INIT
#!/bin/bash
set +e

/root/init.sh >> /root/init.logs 2>&1
echo \$? > /root/init.result
EOF_RUN_INIT
chmod +x /root/run_init.sh

echo "/root/run_init.sh" | at now
