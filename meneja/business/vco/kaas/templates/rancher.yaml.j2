kind: Namespace
apiVersion: v1
metadata:
  name: cattle-system
---
kind: ServiceAccount
apiVersion: v1
metadata:
  name: cattle-admin
  namespace: cattle-system
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cattle-crb
  namespace: cattle-system
subjects:
- kind: ServiceAccount
  name: cattle-admin
  namespace: cattle-system
roleRef:
  kind: ClusterRole
  name: cluster-admin
  apiGroup: rbac.authorization.k8s.io
{% if certificate %}---
kind: Secret
apiVersion: v1
metadata:
  name: cattle-keys-ingress
  namespace: cattle-system
type: Opaque
data:
  tls.crt: {{ certificate }}  # ssl cert for ingress. If self-signed, must be signed by same CA as cattle server
  tls.key: {{ private_key }}  # ssl key for ingress. If self-signed, must be signed by same CA as cattle server{% endif %}
---
apiVersion: v1
kind: Service
metadata:
  namespace: cattle-system
  name: cattle-service
  labels:
    app: cattle
spec:
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  - port: 443
    targetPort: 443
    protocol: TCP
    name: https
  selector:
    app: cattle
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  namespace: cattle-system
  name: cattle-ingress-http
  annotations:
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "30"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"   # Max time in seconds for ws to remain shell window open
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"   # Max time in seconds for ws to remain shell window open
    nginx.ingress.kubernetes.io/ssl-redirect: "false"        # Disable redirect to ssl
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: cattle-service
              servicePort: 80
      host: {{ domain_name }}
  tls:
    - secretName: {% if certificate %}cattle-keys-ingress{% else %}tls-rancher-ingress{% endif %}
      hosts:
        - {{ domain_name }}
{%- if iam_certificate %}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: certs
  namespace: cattle-system
data:
  iam.cairo-cloud.eg.local.pem: |
    {{ iam_certificate | indent(4) }}
{%- endif %}
---
kind: Deployment
apiVersion: apps/v1
metadata:
  namespace: cattle-system
  name: cattle
  labels:
    app: cattle
spec:
  replicas: {{ replicas }} 
  selector:
    matchLabels:
      app: cattle
  template:
    metadata:
      labels:
        app: cattle
    spec:
      serviceAccountName: cattle-admin{% if iam_certificate %}
      hostAliases:
      - hostnames:
        - cairo-cloud.eg.local
        - iam.cairo-cloud.eg.local
        - storage.meneja-dev.gig.tech.local
        ip: {{ dev_env_vpn_ip_address }}
      initContainers:
      - name: certs
        image: {{ vco_domain }}/common/rancher:{{ tag }}
        command: ['sh', '-c', 'cd /usr/share/pki/trust/anchors && update-ca-certificates']
        env:
          - name: CATTLE_AGENT_IMAGE
            value: {% if is_dev %}console.cloudbuilders.be{% else %}{{ vco_domain }}{% endif %}/common/agent:{{ tag }}
        volumeMounts:
        - name: certs
          mountPath: /usr/share/pki/trust/anchors
{% endif %}
      volumes:{% if iam_certificate %}
      - name: certs
        configMap:
          name: certs
{% endif %}
      - name: node-driver-cfg-volume
        hostPath:
          path: /etc/docker-machine-driver
      containers:
      # Rancher install via RKE addons is only supported up to v2.0.8
      - image: {{ vco_domain }}/common/rancher:{{ tag }}
        args:
          - '--no-cacerts'
        env:
          - name: CATTLE_NAMESPACE
            value: cattle-system
          - name: CATTLE_PEER_SERVICE
            value: cattle-service
          - name: CATTLE_AGENT_IMAGE
            value: {% if is_dev %}console.cloudbuilders.be{% else %}{{ vco_domain }}{% endif %}/common/agent:{{ tag }}
          - name: CATTLE_AGENT_STRICT_VERIFY
            value: "false"
        volumeMounts:{% if iam_certificate %}
        - name: certs
          mountPath: /usr/share/pki/trust/anchors
{% endif %}
        - mountPath: /etc/docker-machine-driver
          name: node-driver-cfg-volume
          readOnly: True
        imagePullPolicy: Always
        name: cattle-server
        livenessProbe:
          httpGet:
            path: /ping
            port: 80
          initialDelaySeconds: 60
          periodSeconds: 60
        readinessProbe:
          httpGet:
            path: /ping
            port: 80
          initialDelaySeconds: 20
          periodSeconds: 10
        ports:
        - containerPort: 80
          protocol: TCP
---
kind: Service
apiVersion: v1
metadata:
  name: redirect-to-ui-resources
  namespace: cattle-system
spec:
{%- if iam_certificate %}
  ports:
    - name: http
      port: 80
---
kind: Endpoints
apiVersion: v1
metadata:
  name: redirect-to-ui-resources
  namespace: cattle-system
subsets:
  - addresses:
      - ip: {{ dev_env_vpn_ip_address }}
    ports:
      - name: http
        port: 80
{% else %}
  type: ExternalName
  externalName: {{ vco_domain }}
  ports:
    - name: https
      port: 443
      targetPort: 443
      protocol: TCP
{%- endif %}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: nginx
{%- if iam_certificate %}
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
{% else %}
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    nginx.ingress.kubernetes.io/secure-backends: "true"
{%- endif %}
    nginx.ingress.kubernetes.io/upstream-vhost: {{ vco_domain }}
    nginx.ingress.kubernetes.io/proxy-read-timeout: "1800"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "1800"
  name: redirect-to-ui-resources-ing
  namespace: cattle-system
spec:
  rules:
    - host: {{ domain_name }}
      http:
        paths:
          - path: /api/1/alpha/rancher/node-driver-ui/
            pathType: ImplementationSpecific
            backend:
              service:
                name: 'redirect-to-ui-resources'
                port: 
{%- if iam_certificate %}
                  number: 80
{% else %}
                  number: 443
{%- endif %}
  tls:
    - secretName: {% if certificate %}cattle-keys-ingress{% else %}tls-rancher-ingress{% endif %}
      hosts:
        - {{ domain_name }}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: nodedrivers.management.cattle.io
spec:
  conversion:
    strategy: None
  group: management.cattle.io
  names:
    kind: NodeDriver
    listKind: NodeDriverList
    plural: nodedrivers
    singular: nodedriver
  scope: Cluster
  versions:
  - name: v3
    schema:
      openAPIV3Schema:
        type: object
        x-kubernetes-preserve-unknown-fields: true
    served: true
    storage: true
status:
  acceptedNames:
    kind: NodeDriver
    listKind: NodeDriverList
    plural: nodedrivers
    singular: nodedriver
  storedVersions:
  - v3
---
apiVersion: v1
kind: Namespace
metadata:
  name: {{ system_namespace }}
spec:
  finalizers: 
  - kubernetes
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rancher-agent
  namespace: {{ system_namespace }}
spec:
  replicas: 2
  selector:
    matchLabels:
      {{vco_domain}}/app: agent
  template:
    metadata:
      creationTimestamp: null
      labels:
        {{vco_domain}}/app: agent
    spec:
      containers:
      - name: agent
        image: {{vco_domain}}/common/rancher-agent:latest
        command:
        - python
        - bridge.py
        - '--portal-domain'
        - {{vco_domain}}
        - '--iam-domain'
        - {{iam_domain}}
        - '--jwt-str'
        - >-
          {{jwt}}
        - '--rancher-management-cluster-id'
        - {{management_cluster_id}}
        - '--customer-id'
        - {{customer_id}}
        livenessProbe:
          exec:
            command:
              - /bin/bash
              - '-c'
              - /usr/local/bin/liveness.sh
          initialDelaySeconds: 60
          timeoutSeconds: 3
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 12
        resources: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
  