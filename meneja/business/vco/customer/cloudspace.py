# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import base64
import binascii
import ipaddress
import json
import logging
import os
import re
import time
import uuid
from dataclasses import dataclass
from operator import attrgetter, itemgetter
from typing import Dict, Iterable, <PERSON>, Tuple
from urllib.parse import parse_qs, urlparse, urlunparse

import gevent
import netaddr
import yaml
from dynaqueue.client.client import Client
from dynaqueue.models.task import Task
from dynaqueue.models.task_status import TaskStatus
from dynaqueue.server import STATUS_FAILED, STATUS_SUCCEEDED
from mongoengine.errors import ValidationError
from requests.models import HTTPError
from werkzeug import exceptions

import meneja.business.g8.cloudspace as G8Cloudspace
from meneja.api.common import ResourceNote
from meneja.business.g8.g8_api import G8Client
from meneja.business.validation import check_password_strength
from meneja.business.vco.customer.common import check_and_modify_resource_limit_error

# TODO: update the code when create vm api call in the g8 side accepts gpus
# from meneja.business.vco.customer.gpu import reserve_and_attach_gpu_to_vm
from meneja.business.vco.customer.images import get_cdrom_info
from meneja.business.vco.dns import generate_and_update_zone_file
from meneja.business.vco.metadata.cloudspace import CloudspaceMetadata
from meneja.jobs import job
from meneja.lib.clients.g8.client import G8ClientApiV1
from meneja.lib.clients.g8.lib.models import (
    CloudspacesWireGuardAllowedIPStruct,
    CloudspacesWireGuardConfigWriteStruct,
    CloudspacesWireGuardPeerStruct,
)
from meneja.lib.connection import DynaqueueConnection
from meneja.lib.enumeration import (
    AttachExternalNic,
    CAAFlag,
    CloudspaceModes,
    DetachExternalNic,
    DnsUpdateAction,
    ExternalNetworkType,
    NicTypes,
    OSType,
    SOAOwner,
    StorageOnlyResources,
    SupportedDNSRecordType,
    UserDataCloudspaceMagicParams,
    UserDataMagicParams,
    WgInterfaceType,
)
from meneja.lib.meneja_g8_mapping import get_meneja_response
from meneja.lib.utils import (
    DOMAIN_REGEX,
    BaseDataClass,
    UrlsafeUnPaddedB64,
    encode_base64_id,
    format_userdata,
    run_for_all_locations,
)
from meneja.model.dns_records import DnsRecord
from meneja.model.g8 import G8HardwareSpec, G8Info
from meneja.model.g8_owners import G8Owner
from meneja.model.vco import VCO
from meneja.model.vco.customer import Customer
from meneja.structs.meneja.dataclasses.dns import DNSRecord
from meneja.structs.vco.dataclasses.cloudspace_external_network import CloudspaceExternalNetworkStruct
from meneja.structs.vco.dataclasses.consumption import CloudspaceConsumptionSeriesStruct
from meneja.structs.vco.dataclasses.exposed_disks import ExposedDiskStruct
from meneja.structs.vco.dataclasses.notes import NoteInputsStruct
from meneja.structs.vco.dataclasses.remote_cloudspace import (
    PeerStruct,
    WireguardInterfaceCreateStruct,
    WireguardInterfaceStruct,
)
from meneja.structs.vco.dataclasses.resource_domain import TopLevelDomainStruct
from meneja.structs.vco.dataclasses.subnet import SubnetStruct
from meneja.structs.vco.dataclasses.vm import ExternalNicStruct, VMModel
from meneja.structs.vco.dataclasses.wireguard_interface_name import WireGuardInterfaceName

acronis_id = os.environ.get("ACRONIS_BACKUP_CDROM_ID", None)
veeam_id = os.environ.get("VEEAM_BACKUP_CDROM_ID", None)

MEMORY_STEP = 128

logger = logging.getLogger()

E_NIC_PATTERN = re.compile(r"(externalnetworkId):([0-9]+)")
CS_ID_PATTERN = re.compile(r"^((?!-)[A-Za-z0-9-]+(?<!-)):([0-9]+)$")
DEFAULT_WG_SUBNET_RANGE_START = "**********/24"
DEFAULT_WG_SUBNET_RANGE_END = "************/24"


def _add_location_kv(location, cloudspace_datum):
    cloudspace_datum["location"] = location
    cloudspace_datum["cloudspace_id"] = encode_base64_id(cloudspace_datum["cloudspace_id"], location)
    return cloudspace_datum


def list_vco_customer_cloudspaces(
    customer_id: str, jwt: str, filter_storage_only: bool = False, **kwargs
) -> Tuple[List[dict], List[dict]]:
    """List cloudspaces accessible to customer

    Args:
        customer_id (str): Customer UID
        jwt (str): the jwt of the user

    Returns:
        Tuple[List[dict], List[dict]]: List of stauses and cloudspaces
    """
    location_filters = set(kwargs.pop("locations", []))
    customer_locations = list(Customer.get_locations(customer_id))

    if not location_filters.issubset(set(map(attrgetter("location"), customer_locations))):
        raise ValidationError("locations filters are not subset of customer locations", field_name="locations")
    if location_filters:
        customer_locations = [loc for loc in customer_locations if loc.location in location_filters]
    if filter_storage_only:
        storage_only_locations = [g8.g8_name for g8 in G8Info.list(storage_only=True, fields=["g8_name"])]
        customer_locations = [loc for loc in customer_locations if loc.location not in storage_only_locations]

    @run_for_all_locations(customer_locations)
    def _location_cloudspaces(location):
        css = G8Cloudspace.list_cloudspaces(location.location, location.g8_account_id, jwt, **kwargs)
        return [_add_location_kv(location.location, cs) for cs in css]

    return _location_cloudspaces(customer_id=customer_id)  # pylint: disable=E1123,E1120


def list_remote_cloudspaces(customer_id: str, location: str, cloudspace_id: int, jwt: str) -> List[Dict[str, str]]:
    """List remote cloudspaces
    Args:
        customer_id (str): Customer ID
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        jwt (str): User JWT token

    Returns:
        List[Dict[str, str]]: List of remote cloudspace IDs (encoded)
    """
    _, customer_cloudspaces = list_vco_customer_cloudspaces(customer_id, jwt)
    cs_ipsecs = G8Client(location, jwt=jwt).list_cloudspace_tunnels(cloudspace_id)
    remote_cloudspaces = []
    for cs_ipsec in cs_ipsecs:
        for cs in customer_cloudspaces:
            for ext_ip in cs["external_networks"][0]:  # CS external networks comes in form: [[{External networks}...]]
                if ext_ip["ip"].split("/")[0] == cs_ipsec["remote_public_address"]:
                    remote_cloudspaces.append(
                        {
                            "cloudspace_id": cs["cloudspace_id"],
                            "local_public_address": cs_ipsec["local_public_address"],
                            "name": cs["name"],
                            "remote_private_network": cs_ipsec["remote_private_network"],
                            "location": cs["location"],
                            "remote_public_address": cs_ipsec["remote_public_address"],
                        }
                    )

    cs_wireguards = G8ClientApiV1(location, jwt=jwt).cloudspaces.get_g8_cloudspace_wireguard_config(cloudspace_id)

    encoded_cs_id = encode_base64_id(cloudspace_id, location)
    for cs in customer_cloudspaces:
        if cs["cloudspace_id"] == encoded_cs_id:
            local_public_address = cs["external_network_ip"]

    for cs_wireguard in cs_wireguards:
        decoded_wg_name = WireGuardInterfaceName.from_encoded(cs_wireguard.name)
        if decoded_wg_name.type == WgInterfaceType.WG:
            continue

        wg_parts = decoded_wg_name.name.split("-")
        remote_cloudspace_id = wg_parts[1] if len(wg_parts) > 1 else None

        if remote_cloudspace_id is not None:
            for cs in customer_cloudspaces:
                if remote_cloudspace_id == cs["cloudspace_id"]:
                    remote_cloudspaces.append(
                        {
                            "cloudspace_id": cs["cloudspace_id"],
                            "local_public_address": local_public_address,
                            "name": cs["name"],
                            "remote_private_network": cs["private_network"],
                            "location": cs["location"],
                            "remote_public_address": cs["external_network_ip"],
                        }
                    )
    return remote_cloudspaces


def _clean_wg_cs_connections(cloudspace_id: int, cloudspace_location: str, jwt: str) -> None:
    cs_metadata = get_cloudspace_metadata(cloudspace_location, jwt, cloudspace_id)
    cs_metadata.connected_cloudspaces.clear()

    g8_client = G8ClientApiV1(cloudspace_location, jwt=jwt)
    wireguard_connections = g8_client.cloudspaces.get_g8_cloudspace_wireguard_config(cloudspace_id)

    for wireguard in wireguard_connections:
        decoded_name = WireGuardInterfaceName.from_encoded(wireguard.name)

        if decoded_name.type == WgInterfaceType.CONNECTED_CLOUDSPCASES.value:
            decoded_name_parts = decoded_name.name.split("-")
            if len(decoded_name_parts) == 2:
                remote_cs_id = decoded_name_parts[1]
                cs_metadata.connected_cloudspaces.append(remote_cs_id)
                remote_location, remote_decoded_id = decode_validate_cloudspace_id(remote_cs_id)
                job_cs_wireguard = gevent.spawn(
                    g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface, cloudspace_id, wireguard.name
                )
                job_remote_cs_wireguard = gevent.spawn(
                    G8ClientApiV1(remote_location, jwt=jwt).cloudspaces.delete_g8_cloudspace_wireguard_interface,
                    remote_decoded_id,
                    wireguard.peers[0].name,
                )
                job_cs_wireguard.get()
                job_remote_cs_wireguard.get()
    cs_metadata.save()


def _delete_wg_cs_connections(
    cloudspace_id: int, remote_cs_id: int, cloudspace_location: str, remote_location: str, jwt: str
) -> bool:
    src_g8_client = G8ClientApiV1(cloudspace_location, jwt=jwt)

    wireguard_connections = src_g8_client.cloudspaces.get_g8_cloudspace_wireguard_config(cloudspace_id)

    for wireguard in wireguard_connections:
        decoded_name = WireGuardInterfaceName.from_encoded(wireguard.name)

        try:
            location, remote_decoded_id = decode_validate_cloudspace_id(  # pylint: disable=unused-variable
                decoded_name.name.split("-")[1]
            )
        except IndexError:
            # skip wg-type interfaces as their naming structure is not the same as connected_cloudspaces-type
            continue
        if remote_decoded_id == remote_cs_id:
            job_cs_wireguard = gevent.spawn(
                src_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface, cloudspace_id, wireguard.name
            )
            job_remote_cs_wireguard = gevent.spawn(
                G8ClientApiV1(remote_location, jwt=jwt).cloudspaces.delete_g8_cloudspace_wireguard_interface,
                remote_cs_id,
                wireguard.peers[0].name,
            )
            job_cs_wireguard.get()
            job_remote_cs_wireguard.get()
            return True
    return False


def _delete_ipsec_cs_connections(
    cloudspace_id: int,
    remote_cs_id: int,
    local_cs_ip: str,
    remote_cs_ip: str,
    cloudspace_location: str,
    remote_location: str,
    jwt: str,
) -> None:
    src_g8_client = G8Client(cloudspace_location, jwt=jwt)
    dest_g8_client = G8Client(remote_location, jwt=jwt)
    current_cloudspace = src_g8_client.get_cloudspace_info(cloudspace_id)
    try:
        remote_cloudspace = dest_g8_client.get_cloudspace_info(remote_cs_id)
    except HTTPError as exc:
        if exc.response.status_code == 403:
            # Attempt to delete ipsec on source cloudspace side
            try:
                current_cs_ipsecs = G8Client(cloudspace_location, jwt=jwt).list_cloudspace_tunnels(cloudspace_id)
                remote_cp_private_network = next(
                    (
                        remote_cs["remote_private_network"]
                        for remote_cs in current_cs_ipsecs
                        if remote_cs["remote_public_address"] == remote_cs_ip
                    ),
                    None,
                )
                src_g8_client.remove_tunnel_cloudspace(
                    cloudspace_id,
                    remote_cs_ip,
                    remote_cp_private_network,
                )
            except Exception:  # pylint: disable=W0718
                pass
            raise exceptions.Forbidden(f"Access denied to cloudspace remote_cloudspace {remote_cs_id}")
    job_cs_ipsec = gevent.spawn(
        src_g8_client.remove_tunnel_cloudspace,
        cloudspace_id,
        remote_cs_ip,
        remote_cloudspace["private_network"],
    )
    job_remote_cs_ipsec = gevent.spawn(
        dest_g8_client.remove_tunnel_cloudspace,
        remote_cs_id,
        local_cs_ip,
        current_cloudspace["private_network"],
    )
    gevent.joinall([job_cs_ipsec, job_remote_cs_ipsec], timeout=120)
    if job_cs_ipsec.exception:
        if (
            job_cs_ipsec.exception.response.text.replace(
                """, "") != "Couldn"t find required ipsec config to remove"):
            raise job_cs_ipsec.exception
    if job_remote_cs_ipsec.exception:
        if (job_remote_cs_ipsec.exception.response.text.replace(""",
                "",
            )
            != "Couldn't find required ipsec config to remove"
        ):
            raise job_remote_cs_ipsec.exception
    return


def delete_remote_cloudspace_connection(
    location: str,
    cloudspace_id: int,
    remote_cs_id: str,
    jwt: str,
    remote_cs_ip: str,
    local_cs_ip: str,
) -> None:
    """Remove a connection between two cloudspaces

    Args:
        location (str): Local cloudspace location
        cloudspace_id (int): Local cloudspace ID
        remote_cs_id (str): Remote Cloudspace ID
        jwt (str): JWT
        remote_cs_ip (str): Remote cloudspace external ip address
        local_cs_ip (str): Local cloudspace external ip address
    """

    try:
        remote_location, remote_cs_id = decode_validate_cloudspace_id(remote_cs_id)
    except ValueError as exp:
        raise ValidationError("Invalid connected_cloudspace_id", field_name="connected_cloudspace_id") from exp

    if _delete_wg_cs_connections(cloudspace_id, remote_cs_id, location, remote_location, jwt):
        return

    else:
        _delete_ipsec_cs_connections(
            cloudspace_id, remote_cs_id, local_cs_ip, remote_cs_ip, location, remote_location, jwt
        )


def delete_cloudspace(
    location: str, jwt: str, cloudspace_id: int, permanently: bool = False, reason: str = "", **kwargs
):
    """Delete a cloudspace

    Args:
        location (str): Location of the cloudspace
        jwt (str): JWT
        cloudspace_id (int): Cloudspace ID
        permanently (bool, optional): Permanently delete the cloudspace. Defaults to False.
        reason (str, optional): Reason for deletion. Defaults to "".
    """
    if not permanently:
        _clean_wg_cs_connections(cloudspace_id, location, jwt)

    G8Client(location, jwt=jwt).delete_cloudspace(
        cloudspace_id=cloudspace_id, permanently=permanently, reason=reason, **kwargs
    )

    if permanently:
        delete_dns_records(DnsRecord.list(g8_name=location, cloudspace_id=cloudspace_id))
    else:
        DnsRecord.list(g8_name=location, cloudspace_id=cloudspace_id).update(set__deleted_timestamp=int(time.time()))
        zones = set()
        dns_record: DnsRecord = None
        for dns_record in DnsRecord.list(g8_name=location, cloudspace_id=cloudspace_id, deleted=True):
            top_level_domain = dns_record.authority_domain
            zones.add(top_level_domain)
        for top_level_domain in zones:
            generate_and_update_zone_file(DnsUpdateAction.RELOAD_ZONE, top_level_domain=top_level_domain)


def _restore_connected_cloudspaces(customer_id: str, cloudspace_id: int, location: str, jwt) -> None:
    cs_metadata = get_cloudspace_metadata(location, jwt, cloudspace_id)

    for remote_cs_id in cs_metadata.connected_cloudspaces:
        try:
            connect_remote_cloudspace(customer_id, location, cloudspace_id, remote_cs_id, jwt, "", "")
        except Exception:  # pylint: disable=broad-exception-caught
            pass


@check_and_modify_resource_limit_error()
def restore_cloudspace(
    customer_id: str, location: str, jwt: str, cloudspace_id: int, **kwargs
):  # pylint: disable=unused-argument
    """restore a cloudspace

    Args:
        customer_id(str): Customer ID
        location (str): Location of the cloudspace
        jwt (str): JWT
        cloudspace_id (int): Cloudspace ID
    """
    G8Client(location, jwt=jwt).restore_cloudspace(cloudspace_id=cloudspace_id, **kwargs)
    DnsRecord.list(g8_name=location, cloudspace_id=cloudspace_id, deleted=True).update(set__deleted_timestamp=0)
    zones = set()
    dns_record: DnsRecord = None
    for dns_record in DnsRecord.list(g8_name=location, cloudspace_id=cloudspace_id):
        top_level_domain = dns_record.authority_domain
        zones.add(top_level_domain)
    for top_level_domain in zones:
        generate_and_update_zone_file(DnsUpdateAction.RELOAD_ZONE, top_level_domain=top_level_domain)

    _restore_connected_cloudspaces(customer_id, cloudspace_id, location, jwt)


def _generate_wg_interface_name(name, interface_type):
    return WireGuardInterfaceName(name, interface_type).encoded_interface_name


@check_and_modify_resource_limit_error()
def connect_remote_cloudspace(
    customer_id: str,
    location: str,
    cloudspace_id: int,
    remote_cs_id: str,
    jwt: str,
    remote_cs_ip: str,
    local_cs_ip: str,
):
    """Connect to a remote cloudspace

    Args:
        customer_id (str): Customer ID
        location (str): Source Cloudspace location
        cloudspace_id (int): Source Cloudspace ID
        remote_cs_id (str): Remote Cloudspace ID (base64 encoded with location)
        jwt (str): User JWT Token
        remote_cs_ip (str): Remote Cloudspace External IP address
        local_cs_ip (str): Local Cloudspace external IP address

    Raises:
        exceptions.Conflict: Remote connection already exists
        exceptions.Forbidden: User has no access to source cloudspace
        ValidationError: Private networks of cloudspaces overlap
    """
    remote_cloudspaces = list_remote_cloudspaces(customer_id, location, cloudspace_id, jwt)
    subnets_in_use = set()
    for remote_cs in remote_cloudspaces:
        subnets_in_use.add(netaddr.IPNetwork(f"{remote_cs['remote_public_address']}/24").network)
        if remote_cs["local_public_address"] == local_cs_ip and remote_cs["remote_public_address"] == remote_cs_ip:
            raise exceptions.Conflict("Remote connection already exists")
    try:
        remote_location, remote_cs_id = decode_validate_cloudspace_id(remote_cs_id)
    except ValueError as exp:
        raise ValidationError("Invalid connected_cloudspace_id", field_name="connected_cloudspace_id") from exp

    src_g8_client = G8Client(location, jwt=jwt)
    dest_g8_client = G8Client(remote_location, jwt=jwt)

    try:
        remote_cloudspace = dest_g8_client.get_cloudspace_info(remote_cs_id)
    except HTTPError as exc:
        if exc.response.status_code == 403:
            raise exceptions.Forbidden(f"Access denied to cloudspace remote_cloudspace {remote_cs_id}")

    current_cloudspace = src_g8_client.get_cloudspace_info(cloudspace_id)

    if ipaddress.ip_network(remote_cloudspace["private_network"]).overlaps(
        ipaddress.ip_network(current_cloudspace["private_network"])
    ):
        raise ValidationError("Private networks of cloudspaces should not overlap")

    src_g8_client = G8ClientApiV1(location, jwt=jwt)
    dest_g8_client = G8ClientApiV1(remote_location, jwt=jwt)
    if (
        current_cloudspace["cloudspace_mode"] == CloudspaceModes.PRIVATE
        or remote_cloudspace["cloudspace_mode"] == CloudspaceModes.PRIVATE
    ):
        raise ValueError(f"Can not connect cloudspaces if one of them is type {CloudspaceModes.PRIVATE}")

    # list all Wireguards/ipsec network addresses for both cloudspaces
    connected_wireguards = src_g8_client.cloudspaces.get_g8_cloudspace_wireguard_config(
        cloudspace_id
    ) + dest_g8_client.cloudspaces.get_g8_cloudspace_wireguard_config(remote_cs_id)

    for wireguard in connected_wireguards:
        subnets_in_use.add(netaddr.IPNetwork(wireguard.address).network)

    remote_connections = list_remote_cloudspaces(customer_id, remote_location, remote_cs_id, jwt)
    for connection in remote_connections:
        subnets_in_use.add(netaddr.IPNetwork(f"{connection['remote_public_address']}/24").network)

    subnets_in_use.add(netaddr.IPNetwork(f"{current_cloudspace['external_network_ip']}/24").network)
    subnets_in_use.add(netaddr.IPNetwork(f"{remote_cloudspace['external_network_ip']}/24").network)

    # getting available subnet network to add wireguard address
    interface_addresses, port = _get_free_ip_range_and_port(subnets_in_use, connected_wireguards)
    interface_addresses = interface_addresses.iter_hosts()
    cloudspace_inteface_address = f"{next(interface_addresses)}/24"
    remote_interface_address = f"{next(interface_addresses)}/24"

    encoded_cloudspace_id = encode_base64_id(cloudspace_id, location)
    encoded_remote_cs_id = encode_base64_id(remote_cs_id, remote_location)

    cloud_interface_name = _generate_wg_interface_name(
        f"{encoded_cloudspace_id}-{encoded_remote_cs_id}", WgInterfaceType.CONNECTED_CLOUDSPCASES.value
    )
    remote_interface_name = _generate_wg_interface_name(
        f"{encoded_remote_cs_id}-{encoded_cloudspace_id}", WgInterfaceType.CONNECTED_CLOUDSPCASES.value
    )

    cs_payload = CloudspacesWireGuardConfigWriteStruct(
        name=cloud_interface_name, address=cloudspace_inteface_address, port=port, mtu=1420, peers=[]
    )

    remote_cs_payload = CloudspacesWireGuardConfigWriteStruct(
        name=remote_interface_name, address=remote_interface_address, port=port, mtu=1420, peers=[]
    )

    job_cs_wireguard = gevent.spawn(
        src_g8_client.cloudspaces.add_g8_cloudspace_wireguard_interface_config, cloudspace_id, cs_payload
    )
    job_remote_cs_wireguard = gevent.spawn(
        dest_g8_client.cloudspaces.add_g8_cloudspace_wireguard_interface_config, remote_cs_id, remote_cs_payload
    )

    gevent.joinall([job_cs_wireguard, job_remote_cs_wireguard], timeout=120)

    if job_cs_wireguard.exception and job_remote_cs_wireguard.exception:
        raise job_cs_wireguard.exception

    elif job_cs_wireguard.exception:
        dest_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface(remote_cs_id, remote_interface_name)

        raise job_cs_wireguard.exception

    elif job_remote_cs_wireguard.exception:
        src_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface(cloudspace_id, cloud_interface_name)
        raise job_remote_cs_wireguard.exception

    # get public key for each peer
    cloud_wireguards = src_g8_client.cloudspaces.get_g8_cloudspace_wireguard_config(cloudspace_id)
    for wireguard in cloud_wireguards:
        if wireguard.name == cloud_interface_name:
            cloud_public_key = wireguard.public_key
            break

    remote_wireguards = dest_g8_client.cloudspaces.get_g8_cloudspace_wireguard_config(remote_cs_id)
    for wireguard in remote_wireguards:
        if wireguard.name == remote_interface_name:
            remote_public_key = wireguard.public_key
            break

    def format_allowed_ips(*networks):
        return [CloudspacesWireGuardAllowedIPStruct(network=network, nat=False) for network in networks]

    allowed_ips = format_allowed_ips(remote_interface_address, remote_cloudspace["private_network"])

    cs_payload = CloudspacesWireGuardPeerStruct(
        name=remote_interface_name,
        public_key=remote_public_key,
        endpoint=f"{remote_cs_ip}:{port}",
        allowed_ips=allowed_ips,
        keep_alive=25,
    )

    job_cs_wireguard = gevent.spawn(
        src_g8_client.cloudspaces.add_g8_cloudspace_wireguard_peer,
        cloudspace_id,
        cloud_interface_name,
        cs_payload,
    )

    allowed_ips = format_allowed_ips(cloudspace_inteface_address, current_cloudspace["private_network"])

    remote_cs_payload = CloudspacesWireGuardPeerStruct(
        name=cloud_interface_name,
        public_key=cloud_public_key,
        endpoint=f"{local_cs_ip}:{port}",
        allowed_ips=allowed_ips,
        keep_alive=25,
    )

    job_remote_cs_wireguard = gevent.spawn(
        dest_g8_client.cloudspaces.add_g8_cloudspace_wireguard_peer,
        remote_cs_id,
        remote_interface_name,
        remote_cs_payload,
    )

    gevent.joinall([job_cs_wireguard, job_remote_cs_wireguard], timeout=120)

    if job_cs_wireguard.exception:
        src_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface(cloudspace_id, cloud_interface_name)
        dest_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface(remote_cs_id, remote_interface_name)

        raise job_cs_wireguard.exception

    elif job_remote_cs_wireguard.exception:
        src_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface(cloudspace_id, cloud_interface_name)
        dest_g8_client.cloudspaces.delete_g8_cloudspace_wireguard_interface(remote_cs_id, remote_interface_name)

        raise job_remote_cs_wireguard.exception

    return


@check_and_modify_resource_limit_error()
def create_cloudspace(customer_id: str, vco_id, payload: dict, jwt: str):
    """Create Cloudspace

    Args:
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        payload (dict): Request payload
        jwt (str): User Access JWT token

    Raises:
        exceptions.Unauthorized: User has no access to customer

    Returns:
        dict: {"cloudspace_id": int}
    """
    location = payload.pop("location")
    customer_locations = Customer.get_locations(customer_id)
    account_id = None  # get account_id for selected location
    for loc in customer_locations:
        if loc.location == location:
            account_id = loc.g8_account_id
            break
    else:
        raise exceptions.Unauthorized(f"Customer has no access to location {location}")
    firewall = payload.pop("firewall", {})
    kwargs = payload
    kwargs["account_id"] = account_id
    kwargs["router_type"] = "vgw"
    if kwargs.get("parent_cloudspace_id") and kwargs.get("external_network_id"):
        raise ValidationError(
            "Either of parent_cloudspace_id or external_network_id should be passed", field_name="parent_cloudspace_id"
        )
    if firewall.get("custom", None):  # Custom firewall
        validate_storage_location(location, StorageOnlyResources.CLOUDSPACE_WITH_VFW)
        kwargs["cloudspace_mode"] = "private"
        required_args = ["vcpus", "memory", "disk_size"]
        for arg in required_args:
            if arg not in firewall["custom"]:
                raise ValidationError(f"Parameter {arg} is required for a custom firewall creation")
        return create_custom_firewall_cs(
            location,
            jwt,
            firewall.get("external_network_id"),
            os_type=firewall["custom"].pop("type", None),
            **firewall["custom"],
            **kwargs,
        )
    else:  # Normal private/public/nested
        if firewall.get("private", False):  # Private cloudspace
            kwargs["cloudspace_mode"] = "private"
        elif firewall.get("parent_cloudspace_id"):  # Nested Cloudspace
            kwargs["cloudspace_mode"] = "nested"
            parent_cloudspace_id = firewall.get("parent_cloudspace_id")
            try:
                parent_location, parent_cloudspace_id = decode_validate_cloudspace_id(parent_cloudspace_id)
            except ValueError as exp:
                raise ValidationError("Invalid parent_cloudspace_id", field_name="parent_cloudspace_id") from exp
            if parent_location != location:
                raise ValidationError(
                    "Parent cloudspace should be in the same location", field_name="parent_cloudspace_id"
                )
            kwargs["external_network_id"] = parent_cloudspace_id
        else:  # public
            kwargs["cloudspace_mode"] = "public"
            kwargs["external_network_id"] = firewall.get("external_network_id")  # may be None
        host = ""
        if kwargs.get("local_domain", None):
            if not DOMAIN_REGEX.fullmatch(kwargs["local_domain"]):
                raise ValidationError(
                    f"""Domain {kwargs['local_domain']} is not a valid domain name, only lower
                    case alphanumeric characters, "-" and "."
                    are allowed And subdomains must start and end with  lower case alphanumeric characters
                    """
                )

        if "host" in kwargs and not kwargs["cloudspace_mode"] == "public":
            raise ValidationError(
                "Adding public domain for non public cloudspace is not allowed", field_name="parent_cloudspace_id"
            )
        if "host" in kwargs and kwargs["host"]:
            host = kwargs["host"]
            del kwargs["host"]
            validate_domain(customer_id, host, vco_id)
        cloudspace = G8Cloudspace.create_cloudspace(location, jwt, **kwargs)
        cs = G8Client(location, jwt=jwt).get_cloudspace_info(cloudspace_id=cloudspace["cloudspace_id"])
        if kwargs["cloudspace_mode"] == "public" and host:
            add_dns_record(
                vco_id=vco_id,
                customer_id=customer_id,
                cloudspace_id=cloudspace["cloudspace_id"],
                location=location,
                jwt=jwt,
                value=cs["external_network_ip"],
                domain_name=host,
            )
        return cloudspace


def _create_cloudspace(location: str, jwt: str, **kwargs):
    kwargs["_async"] = True
    return G8Cloudspace.create_cloudspace(location, jwt, **kwargs)


def _wait_for_cloudspace_creation(location, jwt, root_wf_id: str):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    task_guid = dq_client.get_task_status(root_wf_id).result_value
    data = g8_client.wait_for_task(task_guid)
    return get_meneja_response("cloudapi/cloudspaces/create", data)["cloudspace_id"]


def _create_vm(location, jwt, root_wf_id, disk_size, image_id, vcpus, memory):
    if memory % MEMORY_STEP != 0:
        raise ValueError(f"Invalid memory size. Memory should be multiple of {MEMORY_STEP}")

    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    cs_id = dq_client.get_task_status(root_wf_id).on_success.result_value
    return g8_client.create_vm(cs_id, "firewall_vm", "Firewall VM", disk_size, image_id, vcpus, memory, _async=True)


def _create_empty_vm(location, jwt, root_wf_id, disk_size, os_type, os_name, vcpus, memory):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    cs_id = dq_client.get_task_status(root_wf_id).on_success.result_value
    return g8_client.create_empty_vm(cs_id, "firewall_vm", vcpus, memory, disk_size, os_type, os_name, _async=True)


def _boot_vm_with_cdrom_image(location, jwt, root_wf_id, cdrom_id):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    vm_id = dq_client.get_task_status(root_wf_id).on_success.on_success.on_success.result_value
    return g8_client.start_vm(vm_id, cdrom_id, _async=True)


def _wait_for_machine_to_start(location, jwt, root_wf_id):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    task_guid = dq_client.get_task_status(root_wf_id).on_success.on_success.on_success.on_success.result_value
    g8_client.wait_for_task(task_guid)
    return


def _wait_for_vm_creation(location, jwt, root_wf_id: str):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    task_guid = dq_client.get_task_status(root_wf_id).on_success.on_success.result_value
    data = g8_client.wait_for_task(task_guid)
    return get_meneja_response("cloudapi/machines/create", data)["vm_id"]


def _get_free_ip_range_and_port(subnets_in_use, connected_wireguards):
    start_range = netaddr.IPNetwork(DEFAULT_WG_SUBNET_RANGE_START)
    end_range = netaddr.IPNetwork(DEFAULT_WG_SUBNET_RANGE_END)

    wireguards_ports = [wireguard.port for wireguard in connected_wireguards]
    port = 0
    for i in range(51520, 51820):
        if i not in wireguards_ports:
            port = i
            break

    while start_range <= end_range:
        if start_range.network not in subnets_in_use:
            return start_range, port
        start_range = start_range.next()

    return None, port


def _attach_network_set_gateway(location, jwt, root_wf_id, external_network_id):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    # Step 3 Attach External network to VM
    cs_id = dq_client.get_task_status(root_wf_id).on_success.result_value
    vm_id = dq_client.get_task_status(root_wf_id).on_success.on_success.on_success.result_value
    g8_client.attach_external_network_vm(vm_id, external_network_id)

    # Step 3 Set cloudspace default gateway to VM IP
    vm_data = g8_client.get_vm_info(vm_id)
    gateway = vm_data["network_interfaces"][0]["ip_address"]
    g8_client.set_cloudspace_default_gateway(cs_id, gateway)


def _cleanup_custom_fw_cs(location, jwt, root_wf_id):
    g8_client = G8Client(location, jwt=jwt)
    dq_client = DynaqueueConnection.get_client()
    cloudspace_worfklow = dq_client.get_task_status(root_wf_id).on_success
    if cloudspace_worfklow.status not in [STATUS_SUCCEEDED, STATUS_FAILED]:
        raise RuntimeError("Cleanup started but create cloudspace failed. This should not happen")
    vm_worfklow = cloudspace_worfklow.on_success.on_success
    if vm_worfklow.status == STATUS_SUCCEEDED:
        # VM Created, delete vm
        g8_client.delete_vm(vm_worfklow.result_value, permanently=True)
    g8_client.delete_cloudspace(cloudspace_worfklow.result_value, permanently=True)


def _raise_failed_steps(task_status: TaskStatus):
    if task_status is None:
        return
    if task_status.status == STATUS_SUCCEEDED:
        return _raise_failed_steps(task_status.on_success)
    elif task_status.status == STATUS_FAILED:
        _ = task_status.result_value  # This should raise an error


def create_custom_firewall_cs(
    location: str,
    jwt: str,
    external_network_id: int,
    disk_size: int,
    vcpus: int,
    memory: int,
    os_type: str = None,
    image_id: int = None,
    cdrom_id: int = None,
    **kwargs,
) -> dict:
    """Create a Cloudspace with a custom firewall

    Args:
        location (str): Cloudspace location
        jwt (str): User JWT
        external_network_id (int): External Network ID for firewall VM
        image_id (int): Image ID to use for firewall VM
        cdrom_id (int): CDROM Image ID to use for firewall VM
        disk_size (int): Disk Size to use for firewall VM
        vcpus (int): VCPUs to use for firewall VM
        memory (int): Memory in MB to use for firewall VM

    Returns:
        dict: cloudspace_id
    """
    if not (image_id or cdrom_id) or (image_id and cdrom_id):
        raise ValidationError("For custom firewall, either of image_id or cdrom_id should be passed")

    timeout = 3600

    create_cloudspace_task = Task(retry_count=1, timeout=timeout, title="Init cloudspace creation")
    create_cloudspace_task.set_workload(_create_cloudspace, location, jwt, **kwargs)

    dq_client: Client = DynaqueueConnection.get_client()

    wait_for_cloudspace_creation = Task(retry_count=1, timeout=timeout, title="Wait for cloudspace creation")
    wait_for_cloudspace_creation.set_workload(_wait_for_cloudspace_creation, location, jwt, create_cloudspace_task.id)

    create_vm_task = Task(retry_count=1, timeout=timeout, title="Init vm creation")
    if image_id:
        create_vm_task.set_workload(
            _create_vm, location, jwt, create_cloudspace_task.id, disk_size, image_id, vcpus, memory
        )
    else:
        create_vm_task.set_workload(
            _create_empty_vm,
            location,
            jwt,
            create_cloudspace_task.id,
            disk_size,
            os_type,
            os_type + " - other",
            vcpus,
            memory,
        )

    wait_for_vm_creation = Task(retry_count=1, timeout=timeout, title="Wait for vm creation")
    wait_for_vm_creation.set_workload(_wait_for_vm_creation, location, jwt, create_cloudspace_task.id)

    if cdrom_id:
        boot_vm_with_cdrom_image = Task(retry_count=1, timeout=timeout, title="Boot Machine with CD-ROM Image")
        boot_vm_with_cdrom_image.set_workload(
            _boot_vm_with_cdrom_image, location, jwt, create_cloudspace_task.id, cdrom_id
        )

        wait_for_machine_to_start = Task(retry_count=1, timeout=timeout, title="Wait for vm Boot")
        wait_for_machine_to_start.set_workload(_wait_for_machine_to_start, location, jwt, create_cloudspace_task.id)

    attach_network_set_gateway = Task(
        retry_count=1,
        timeout=timeout,
        title="Attach external network to VM, and set default gateway of CS to the firewall VM",
    )
    attach_network_set_gateway.set_workload(
        _attach_network_set_gateway, location, jwt, create_cloudspace_task.id, external_network_id
    )

    cleanup_custom_fw_cs = Task(title="Cleanup created cloudspace and VM")
    cleanup_custom_fw_cs.set_workload(_cleanup_custom_fw_cs, location, jwt, create_cloudspace_task.id)

    create_cloudspace_task.on_success = wait_for_cloudspace_creation

    wait_for_cloudspace_creation.on_success = create_vm_task

    create_vm_task.on_success = wait_for_vm_creation
    create_vm_task.on_failure = cleanup_custom_fw_cs

    wait_for_vm_creation.on_success = boot_vm_with_cdrom_image if cdrom_id else attach_network_set_gateway
    wait_for_vm_creation.on_failure = cleanup_custom_fw_cs

    if cdrom_id:
        boot_vm_with_cdrom_image.on_success = wait_for_machine_to_start
        boot_vm_with_cdrom_image.on_failure = cleanup_custom_fw_cs

        wait_for_machine_to_start.on_success = attach_network_set_gateway
        wait_for_machine_to_start.on_failure = cleanup_custom_fw_cs

    attach_network_set_gateway.on_failure = cleanup_custom_fw_cs

    task_future = dq_client.submit_task_async(create_cloudspace_task)
    task_status = task_future.get_result()

    # Raise error if any step fails
    _raise_failed_steps(task_status)

    return {"cloudspace_id": task_status.on_success.result_value}


def move_vm(location: str, vm_id: int, jwt: str, **kwargs) -> dict:
    """Move VM

    Args:
        location (str): VM location
        vm_id (int): VM ID
        jwt (str): User JWT Token

    Raises:
        ValidationError: location and target_location do not match

    Returns:
        dict: response
    """
    try:
        target_location, kwargs["target_cloudspace_id"] = decode_validate_cloudspace_id(kwargs["target_cloudspace_id"])
    except ValueError as exp:
        raise ValidationError("Invalid target_cloudspace_id", field_name="target_cloudspace_id") from exp
    if target_location != location:
        raise ValidationError("Target cloudspace should be in the same location", field_name="target_cloudspace_id")
    return G8Client(location, jwt=jwt).move_vm(vm_id, **kwargs)


def clone_vm(location: str, vm_id: int, jwt: str, **kwargs) -> dict:
    """Clone VM

    Args:
        location (str): VM location
        vm_id (int): VM ID
        jwt (str): User JWT Token

    Raises:
        ValidationError: location and target_cloudspace_id do not match

    Returns:
        dict: response
    """
    target_cloudspace_id = kwargs.pop("target_cloudspace_id")
    if target_cloudspace_id:
        try:
            target_location, kwargs["cloudspace_id"] = decode_validate_cloudspace_id(target_cloudspace_id)
        except ValueError as exp:
            raise ValidationError("Invalid target_cloudspace_id", field_name="target_cloudspace_id") from exp
        if target_location != location:
            raise ValidationError("Target cloudspace should be in the same location", field_name="target_cloudspace_id")
    return G8Client(location, jwt=jwt).clone_vm(vm_id, **kwargs)


def calculate_cs_consumption(location: str, jwt: str, cloudspace_id: int, end: int, start: int) -> dict:
    """Calculate Cloudspace Resource consumption

    Args:
        location (str): Cloudspace Location (G8 name)
        jwt (str): User JWT token
        cloudspace_id (int): Cloudspace ID
        end (int): end timestamp
        start (int): start timestamp

    Raises:
        exceptions.NotFound: Cloudspace not found

    Returns:
        dict: response
    """
    return G8Client(location, jwt).get_cloudspace_consumption(cloudspace_id, end, start)


def calculate_cs_consumption_ts(
    location: str,
    jwt: str,
    cloudspace_id: int,
    end: int,
    start: int,
    units: List[int],
    step: str,
) -> dict:
    """Calculate Cloudspace Resource consumption timeseries

    Args:
        location (str): Cloudspace Location (G8 name)
        jwt (str): User JWT token
        cloudspace_id (int): Cloudspace ID
        end (int): end timestamp
        start (int): start timestamp
        units (List[int]): Consumption units to include
        step (str): Time step

    Raises:
        exceptions.NotFound: Cloudspace not found

    Returns:
        dict: response
    """
    cs_consumption = G8Client(location, jwt).get_cloudspace_consumption_ts_v2(cloudspace_id, end, start, step, units)[
        "cloudspace"
    ]
    cs_consumption["cloudspace_id"] = encode_base64_id(cs_consumption["id"], location)
    cs_consumption["vms"] = cs_consumption["machines"]
    for vm in cs_consumption["vms"]:
        vm["vm_id"] = vm["id"]
    return CloudspaceConsumptionSeriesStruct.load(cs_consumption)


def calculate_vm_consumption(location: str, jwt: str, vm_id: int, end: int, start: int):
    """Calculate VM Resource consumption

    Args:
        location (str): Cloudspace Location (G8 name)
        jwt (str): User JWT token
        vm_id (int): VM ID
        end (int): end timestamp
        start (int): start timestamp

    Raises:
        exceptions.NotFound: VM not found

    Returns:
        dict: response
    """
    return G8Client(location, jwt).get_vm_consumption(vm_id, end, start)


def calculate_vm_consumption_ts(
    location: str,
    jwt: str,
    vm_id: int,
    end: int,
    start: int,
    units: List[int],
    step: str,
):
    """Calculate VM Resource consumption timeseries

    Args:
        location (str): Cloudspace Location (G8 name)
        jwt (str): User JWT token
        vm_id (int): VM ID
        end (int): end timestamp
        start (int): start timestamp
        units (List[int]): Consumption units to include
        step (str): Time step

    Raises:
        exceptions.NotFound: VM not found

    Returns:
        dict: response
    """
    return G8Client(location, jwt).get_vm_consumption_ts(vm_id, end, start, step, units)


def decode_validate_cloudspace_id(cloudspace_id) -> Tuple[str, int]:
    """Decoded and validate cloudspace_id that is in base64 format with location

    Raises:
        ValueError: Id does not match location:id format

    Returns:
        Tuple[str, int]: Tuple of the location and the cloudspace ID in that location
    """
    try:
        match = CS_ID_PATTERN.fullmatch(UrlsafeUnPaddedB64.decode(cloudspace_id))
    except (binascii.Error, UnicodeDecodeError) as exp:
        raise ValueError("Invalid cloudspace_id") from exp
    if match is None:
        raise ValueError("Invalid cloudspace_id")
    location, cloudspace_id = match.groups()
    return location, int(cloudspace_id)


def get_vm_external_nics(location: str, jwt: str, vm_id: int) -> List[ExternalNicStruct]:
    """Get VM External NICs

    Args:
        location (str): VM Location
        jwt (str): User JWT Token
        vm_id (int): VM ID

    Returns:
        List[dict]: List of vm nics
    """
    external_nics = []
    vm_info = G8Cloudspace.get_vm_info(location, jwt=jwt, vm_id=vm_id)
    cs_id = vm_info["cloudspace_id"]
    for nic in vm_info["network_interfaces"]:
        if nic["nic_type"] != NicTypes.INTERNAL.name:
            if nic["nic_type"] == NicTypes.CLOUDSPACE.name:
                nic["external_cloudspace_id"] = encode_base64_id(nic["network_id"], location)
                nic["network_id"] = -1
            external_nics.append(nic)
    vm_info["cloudspace_id"] = encode_base64_id(cs_id, location)
    return external_nics


def get_vm_external_nic(location: str, jwt: str, vm_id: int, external_ip_address: str) -> ExternalNicStruct:
    """Get VM external NIC details

    Args:
        location (str): VM Location
        jwt (str): User JWT Token
        vm_id (int): VM ID
        external_ip_address (str): IP Address of external nics

    Raises:
        exceptions.NotFound: Ip Address not found

    Returns:
        dict: NIC details
    """

    vm_external_nics = get_vm_external_nics(location, jwt, vm_id)
    for nic in vm_external_nics:
        if nic["ip_address"].split("/")[0] == external_ip_address:
            return nic
    raise exceptions.NotFound(f"External nic with address {external_ip_address} not found")


def detach_vm_external_nic(
    customer_id: str = None,
    cloudspace_id: int = None,
    location: str = None,
    jwt: str = None,
    vm_id: int = None,
    external_ip_address: str = None,
    external_network_id: int = None,
    external_cloudspace_id: str = None,
    external_network_type: str = None,
) -> None:
    """Detach an external NIC from VM

    Args:
        customer_id (str): Customer ID
        cloudspace_id (int): Cloudspace ID on location
        location (str): VM Location
        jwt (str): User JWT Token
        vm_id (int): VM ID
        external_ip_address (str): External IP Address of NIC to delete

    Raises:
        exceptions.NotFound: NIC not found

    Returns:
        dict: Success
    """
    from meneja.business.vco.customer.ingress import remove_host_from_server_pools  # pylint: disable=C0415, R0401

    if external_cloudspace_id and external_network_id != -1:
        raise ValueError("Cannot provide external cloudspace id and external network id")
    elif (
        external_cloudspace_id and external_network_type == AttachExternalNic.CLOUDSPACE and external_network_id == -1
    ):  # pylint: disable=line-too-long # noqa: E501
        try:
            _, external_network_id = decode_validate_cloudspace_id(external_cloudspace_id)
        except ValueError as exp:
            raise ValueError("Invalid external cloudspace ID") from exp
    elif external_network_type == AttachExternalNic.EXTERNAL and external_network_id == -1:
        raise ValueError("Need to provide external network ID that you want to detach from")
    elif external_cloudspace_id is None and external_network_type == AttachExternalNic.CLOUDSPACE:
        raise ValueError("Need to provide external cloudspace id to detach")

    g8_client = G8Client(location, jwt=jwt)
    vm_info = g8_client.get_vm_info(vm_id)
    for interface in vm_info["network_interfaces"]:
        nic_type = DetachExternalNic.get_by_name(external_network_type or AttachExternalNic.EXTERNAL).value
        ip_address = interface["ip_address"].split("/")[0]
        nic_id = int(interface["network_id"])
        if external_ip_address == ip_address and nic_type == interface["nic_type"] and external_network_id == nic_id:
            break

    else:
        raise exceptions.NotFound(f"External nic with address {external_ip_address} not found")

    result = g8_client.detach_external_network_vm(
        vm_id, external_network_id, external_ip_address, external_network_type
    )
    try:
        remove_host_from_server_pools(customer_id, location, jwt, cloudspace_id, [external_ip_address])
    except Exception as exception:  # pylint: disable=W0718
        logger.exception("Failed to remove host from server pools %s", exception)
    if external_network_type == AttachExternalNic.EXTERNAL:
        delete_dns_records(DnsRecord.list(g8_name=location, vm_id=vm_id, nic_ip=external_ip_address))
    return result


@check_and_modify_resource_limit_error()
def create_vm(
    customer_id: str,
    location: str,
    jwt: str,
    cloudspace_id: int,
    account_id: int,
    vco_id: str,
    domain: str = None,
    **kwargs,  # pylint: disable=unused-argument
) -> dict:
    """Create VM

    Args:
        customer_id (str): Customer ID
        location (str): Virtual Machine location
        jwt (str): User JWT Token
        cloudspace_id (int): Cloudspace ID in which to create VM
        account_id (int): Account ID
        vco_id (str): VCO ID
        domain (str): Userdata domain (Defaults to None)
    Raises:
        ValidationError: Either both or none of image_id and cdrom_id is passed
        ValueError: data disks not integers
        ValidationError: user_data passed with cdrom image
        ValidationError: os_type not passed with cdrom image

    Returns:
        dict: VM ID
    """
    validate_storage_location(location, StorageOnlyResources.VM)
    if kwargs["memory"] % MEMORY_STEP != 0:
        raise ValueError(f"Invalid memory size. Memory should be multiple of {MEMORY_STEP}")
    if sum(map(lambda key: kwargs.get(key) is not None, ["image_id", "cdrom_id", "boot_disk_id"])) > 1:
        raise ValidationError(
            "At most one of image_id, cdrom_id or boot_disk_id should be passed", field_name="cdrom_id"
        )
    if kwargs.get("tpm_secret"):
        check_password_strength(kwargs["tpm_secret"])

    g8_client = G8Client(location, jwt=jwt)
    if kwargs.get("data_disks"):
        data_disks = []
        for data in kwargs["data_disks"]:
            if isinstance(data, int):
                data_disks.append(data)
            elif isinstance(data, list):
                data_disks.extend(data)
        kwargs["data_disks"] = data_disks
    if kwargs.get("snapshot_id"):
        snapshot_id = kwargs.get("snapshot_id")
        invalid_params = [p for p in ["image_id", "cdrom_id", "user_data", "disk_size"] if kwargs.get(p) is not None]
        if invalid_params:
            raise ValidationError(
                f"Invalid parameters passed, parameters: {invalid_params} not required when creating from snapshot"
            )
        kwargs["disk_id"] = kwargs.pop("boot_disk_id", None)
        if not kwargs["disk_id"]:
            raise ValidationError("boot_disk_id is required when creating from snapshot", field_name="boot_disk_id")

        vm = g8_client.create_vm_from_snapshot(cloudspace_id, **kwargs)
        if kwargs.get("all_vm_disks"):
            try:
                vm_id = g8_client.get_disk_info(kwargs["disk_id"])["vm_id"]
                if not vm_id:
                    raise ValidationError(f"Disk {kwargs['disk_id']} is not attached to a machine")
                machine_snapshots = g8_client.list_vm_snapshots_new(vm_id)
                try:
                    snapshot = next(ss for ss in machine_snapshots if ss["snapshot_name"] == snapshot_id)
                except StopIteration as exp:
                    raise ValidationError(f"Snapshot {snapshot_id} is not found") from exp
                for disk_id in map(itemgetter("disk_id"), snapshot["disks"]):
                    if disk_id == kwargs["disk_id"]:
                        continue  # Skip main disk
                    src_disk_info = g8_client.get_disk_info(disk_id)
                    description = (
                        f"Clone for disk {disk_id}, snapshot {snapshot_id}"
                        + "\nOriginal description:\n{src_disk_info['description']}"
                    )
                    new_disk_id = g8_client.clone_disk_snapshot(
                        disk_id, snapshot_id, src_disk_info["disk_name"], description
                    )["disk_id"]
                    try:
                        g8_client.attach_disk_vm(vm["vm_id"], new_disk_id)
                    except Exception:
                        g8_client.delete_disk(new_disk_id, permanently=True)
                        raise
            except Exception:
                g8_client.delete_vm(vm["vm_id"], permanently=True)
                raise
        # TODO: update the code when create vm api call in the g8 side accepts gpus
        # if kwargs.get("gpu_id"):
        #     try:
        #         reserve_and_attach_gpu_to_vm(
        #             vm_id=vm["vm_id"],
        #             account_id=account_id,
        #             jwt=jwt,
        #             g8_name=location,
        #             gpu_id=kwargs["gpu_id"],
        #             vgpu_name=kwargs.get("vgpu_name"),
        #         )
        #     except Exception:
        #         g8_client.delete_vm(vm["vm_id"], permanently=True)
        #         raise
        return vm
    elif kwargs.get("image_id"):
        os_image = g8_client.get_image(image_id=kwargs.get("image_id"))
        if not kwargs.get("boot_type"):
            kwargs["boot_type"] = os_image["boot_type"]
        if os_image["boot_type"] != kwargs.get("boot_type"):
            raise ValueError("Selected boot type can not be different from image boot type")
        if not kwargs.get("disk_size"):
            raise ValidationError("disk_size required when creating machine from image", field_name="disk_size")

        license_key = ""
        if os_image["os_type"] == OSType.WINDOWS:
            g8_owner = G8Owner.get_by_id(VCO.get_by_id(vco_id).g8owner_id)
            for key in g8_owner.license_keys:
                if os_image["os_name"] == key["os_name"]:
                    license_key = key["os_name"]
                    break
            # pylint: disable=W0511
            # TODO Uncomment when cloud enablers are notified with license key management
            # else:
            #     raise ValueError(
            #         f"Windows license key for os name {os_image['os_name']}"
            #         "is required before creating vm with windows")

        payload_userdata = kwargs.pop("userdata", None)
        if kwargs.get("user_data") and payload_userdata:
            raise ValidationError(
                "Cannot pass both userdata via uri and json body. Pass via json body in case of large userdata string"
            )
        if license_key:
            if not payload_userdata:
                if not kwargs.get("user_data"):
                    kwargs["user_data"] = ""
                kwargs[
                    "user_data"
                ] += f"""
write_files:
  - content: {base64.b64encode(license_key.encode("ascii")).decode('ascii')}
    encoding: b64
    path: C:\\gig\\init\\activation.ps1
    permissions: '0644' """
            else:
                license_script = {
                    "content": base64.b64encode(license_key.encode("ascii")).decode("ascii"),
                    "encoding": "b64",
                    "path": "/var/lib/cloud/scripts/per-once/openvpn_setup",
                    "permissions": "0755",
                }
                if payload_userdata.get("write_files"):
                    payload_userdata["write_files"].append(license_script)
                else:
                    payload_userdata["write_files"] = [license_script]

        if kwargs["user_data"]:
            kwargs["user_data"] = format_userdata(kwargs["user_data"])
        if payload_userdata:
            try:
                user_data = yaml.safe_dump(payload_userdata)
            except yaml.YAMLError as exc:
                raise ValueError("Invalid yaml provided.") from exc
            user_data_params = kwargs.pop("UserDataParameters", {})
            if not isinstance(user_data_params, dict):
                raise ValueError("User data parameters must be a dictionary.")

            parameter_pattern = r"{{(\w+)}}"
            yaml_params = set(re.findall(parameter_pattern, user_data))
            cloudspace_magic_params = UserDataCloudspaceMagicParams.values()

            missing_params = (
                yaml_params
                - set(user_data_params.keys())
                - set([*UserDataMagicParams.values(), *cloudspace_magic_params])
            )
            if missing_params:
                raise ValueError(f"Messing parameters for user data template {missing_params}")

            user_data_params["customer_id"] = customer_id
            user_data_params["g8_name"] = location
            user_data_params["user_jwt"] = jwt
            user_data_params["g8_cloudspace_id"] = cloudspace_id
            user_data_params["vco_domain"] = domain
            user_data_params["vm_name"] = kwargs["name"]

            if yaml_params & set(cloudspace_magic_params):
                cloudspace = get_cloudspace(cloudspace_id, location, jwt)
                for val in cloudspace_magic_params:
                    if val in cloudspace:
                        user_data_params[val] = cloudspace[val]

            def replace_params(match):
                key = match.group(1)
                return user_data_params.get(key, match.group(0))

            # Replace parameters in the yaml
            kwargs["user_data"] = re.sub(parameter_pattern, replace_params, user_data)

        vm = g8_client.create_vm(cloudspace_id, **kwargs)
        # TODO: update the code when create vm api call in the g8 side accepts gpus
        # if kwargs.get("gpu_id"):
        #     try:
        #         reserve_and_attach_gpu_to_vm(
        #             vm_id=vm["vm_id"],
        #             account_id=account_id,
        #             jwt=jwt,
        #             g8_name=location,
        #             gpu_id=kwargs["gpu_id"],
        #             vgpu_name=kwargs.get("vgpu_name"),
        #         )
        #     except Exception:
        #         g8_client.delete_vm(vm["vm_id"], permanently=True)
        #         raise
        return vm
    else:
        if kwargs.get("user_data"):
            raise ValidationError(
                "user_data is not allowed when using cdrom image or creating an empty VM", field_name="user_data"
            )
        cdrom_id = kwargs.pop("cdrom_id", None)
        acronis = kwargs.pop("acronis", None)
        veeam = kwargs.pop("veeam", None)
        os_type = kwargs.pop("os_type", None)
        os_name = kwargs.pop("os_name", None)
        boot_disk_id = kwargs.pop("boot_disk_id", None)

        # Validate that os_type/name is set when not using cdrom, o.w. get them from cdrom info
        if not cdrom_id:
            message = "creating a vm with boot_disk_id" if boot_disk_id else "creating an empty VM"
            if not os_type:
                raise ValidationError(f"os_type is required when {message}", field_name="os_type")
            if not os_name:
                raise ValidationError(f"os_name is required when {message}", field_name="os_name")
        else:
            cdrom_info = get_cdrom_info(account_id, cdrom_id, location, g8_client=g8_client)
            os_type = cdrom_info["os_type"]
            os_name = cdrom_info["os_name"]
        vm = g8_client.create_empty_vm(cloudspace_id, os_type=os_type, os_name=os_name, **kwargs)
        disk_attached = False
        try:
            if boot_disk_id:
                vm_info = g8_client.get_vm_info(vm["vm_id"])
                old_disk_id = None
                try:
                    old_disk_id = next(
                        disk["disk_id"]
                        for disk in vm_info["disks"]
                        if disk["disk_name"] == "Boot Disk" and disk["disk_type"] == "B"
                    )
                except StopIteration:
                    pass
                g8_client.attach_disk_vm(vm["vm_id"], boot_disk_id)
                disk_attached = True
                g8_client.set_vm_boot_disk(vm["vm_id"], boot_disk_id)
                if old_disk_id:
                    g8_client.delete_disk(disk_id=old_disk_id, detach=True, permanently=True)
                if kwargs.get("start_vm"):
                    g8_client.start_vm(vm["vm_id"])
            else:
                cdrom_id_to_use = None
                if cdrom_id:
                    cdrom_id_to_use = cdrom_id
                elif acronis or veeam:
                    backup_id = acronis_id if acronis else veeam_id
                    backup_type = "Acronis" if acronis else "Veeam"
                    if not backup_id:
                        raise RuntimeError(f"{backup_type} backup is not available")
                    g8 = G8Info.get_by_name(location)
                    try:
                        cdrom = next(cd for cd in g8.cdroms if cd.cdrom_id == backup_id)
                    except StopIteration as exp:
                        raise RuntimeError(
                            f"{backup_type} backup image is not available for location: {location}"
                        ) from exp
                    if not cdrom.g8_cdrom_id:
                        raise RuntimeError(f"{backup_type} backup image is not available for location: {location}")
                    cdrom_id_to_use = cdrom.g8_cdrom_id

                if cdrom_id_to_use:
                    if kwargs.get("start_vm"):
                        g8_client.start_vm(vm["vm_id"], cdrom_id_to_use)
                    else:
                        g8_client.attach_disk_vm(vm["vm_id"], cdrom_id_to_use, **kwargs)

        except (HTTPError, RuntimeError):
            if boot_disk_id and disk_attached:
                g8_client.detach_disk_vm(vm["vm_id"], boot_disk_id)
            g8_client.delete_vm(vm["vm_id"], permanently=True)
            raise
        # TODO: update the code when create vm api call in the g8 side accepts gpus
        # if kwargs.get("gpu_id"):
        #     try:
        #         reserve_and_attach_gpu_to_vm(
        #             vm_id=vm["vm_id"],
        #             account_id=account_id,
        #             jwt=jwt,
        #             g8_name=location,
        #             gpu_id=kwargs["gpu_id"],
        #             vgpu_name=kwargs.get("vgpu_name"),
        #         )
        #     except Exception:
        #         g8_client.delete_vm(vm["vm_id"], permanently=True)
        #         raise
        return vm


@check_and_modify_resource_limit_error()
def import_vm_from_s3(
    customer_id: str, jwt: str, location: str, cloudspace_id: int, **kwargs  # pylint: disable=unused-argument
) -> None:
    """Import a vm from s3

    Args:
        customer_id (str): Customer ID
        jwt (str): _description_
        location (str): _description_
        cloudspace_id (int): _description_

    Returns:
        dict: vm ID
    """
    if not urlparse(kwargs["link"]).scheme:
        raise ValueError("S3 URL protocol (http or https) is required")
    return G8Client(location, jwt=jwt, request_timeout=600).import_vm_s3(cloudspace_id, **kwargs)


def anti_affinity_gp_exists(location: str, jwt: str, cloudspace_id: int, group_id: str) -> bool:
    """Checks if an AntiAffinity Group exists

    Args:
        location (str): Location (G8)
        jwt (str): User JWT Token
        cloudspace_id (int): Cloudspace ID
        group_id (str): Anti Affinity Group ID

    Returns:
        bool: exists
    """
    af_groups = G8Client(location, jwt=jwt).list_cloudspace_anti_affinity_groups(cloudspace_id)
    return group_id in map(itemgetter("group_id"), af_groups)


def anti_affinity_gp_vms(cloudspace_id: int, group_id: str, location: str, jwt: str) -> dict:
    """Get VMs belonging to AntiAffinity Group

    Args:
        cloudspace_id (int): Cloudspace ID
        group_id (str): Group ID
        location (str): Location
        jwt (str): User JWT

    Returns:
        dict: Group VMs
    """
    # TODO use `get_cloudspace_anti_affinity_group_vms` # pylint: disable=W0511
    g8_client = G8Client(location, jwt=jwt)
    vms_list = g8_client.list_vms(cloudspace_id)
    group_vms = []
    for vm in vms_list:
        vm_info = g8_client.get_vm_info(vm["vm_id"])
        if group_id in vm_info["anti_affinity_group_ids"]:
            group_vms.append({"vm_id": vm["vm_id"], "status": vm["status"]})

    return {"vms": group_vms}


def get_cloudspace(cloudspace_id: int, location: str, jwt: str) -> dict:
    """Get Cloudspace info

    Args:
        cloudspace_id (int): Cloudspace ID
        location (str): location name
        jwt (str): JWT

    Returns:
        dict: cloudspace information dictionary
    """
    cloudspace = G8Client(location, jwt=jwt).get_cloudspace_info(cloudspace_id=cloudspace_id)
    cloudspace["cloudspace_id"] = encode_base64_id(cloudspace["cloudspace_id"], location)
    if cloudspace["cloudspace_mode"] == CloudspaceModes.NESTED:
        cloudspace["external_network_id"] = encode_base64_id(cloudspace["external_network_id"], location)

    return cloudspace


def list_exposed_disks(location: str, jwt: str, cloudspace_id: str) -> List[ExposedDiskStruct]:
    """List exposed disks

    Args:
        location (str): Location name
        jwt (str): JWT
        cloudspace_id (str): Cloudspace ID

    Returns:
        List[ExposedDiskStruct]: list of exposed disks
    """
    return [
        ExposedDiskStruct(disk_id=d_id)
        for d_id in G8Client(location, jwt=jwt).list_exposed_disks(cloudspace_id=cloudspace_id)
    ]


@dataclass
class CloudSpaceChild(BaseDataClass):
    """Cloudspace child info"""

    cloudspace_id: str
    name: str
    ip_in_parent_network: str
    private_network: str


def get_cloudspace_metadata(location: str, jwt: str, cloudspace_id: int) -> CloudspaceMetadata:
    """Get cloudspace metadata

    Args:
        location (str): Location ID
        jwt (str): User JWT
        cloudspace_id (int): Cloudspace ID at location

    Returns:
        CloudspaceMetadata
    """
    g8_client = G8Client(location, jwt=jwt)
    cs_metadata = CloudspaceMetadata.load(cloudspace_id, g8_client)
    return cs_metadata


@job(title="add note", retry_count=1, object_type="cloudspace-metadata", object_id="{cloudspace_id}", block=True)
def create_cloudspace_note(
    cloudspace_id: str, location: str, jwt: str, created_by: str, note_data: NoteInputsStruct
) -> None:
    """Create cloudspace note

    Args:
        cloudspace_id (str): cloudspace id
        location (str): Location ID
        jwt (str): JWT token
        created_by: Note creator
        note_data (NoteInputsStruct): Note

    Returns:
        None
    """
    g8_client = G8Client(location, jwt=jwt)
    cs_metadata = CloudspaceMetadata.load(cloudspace_id, g8_client)
    note = ResourceNote(
        creation_time=time.time(),
        last_update_time=time.time(),
        title=note_data.title,
        content=note_data.content,
        id=uuid.uuid4().hex,
        created_by=created_by,
        modified_by=None,
    )
    cs_metadata.notes.append(note)
    cs_metadata.save()


def update_cloudspace_local_domain(jwt: str, location: str, cloudspace_id: int, local_domain: str) -> bool:
    """Update cloudspace local domain

    Args:
        jwt (str): JWT
        location (str): Location
        cloudspace_id (int): cloudspace ID
        local_domain (str): Local domain

    Returns:
        bool: if updated
    """
    if local_domain and not DOMAIN_REGEX.fullmatch(local_domain):
        raise ValidationError("Local domain must be a valid domain name")
    return G8Client(location, jwt=jwt).update_cloudspace_localdomain(cloudspace_id, local_domain)


@job(title="update note", retry_count=1, object_type="cloudspace-metadata", object_id="{cloudspace_id}", block=True)
def update_cloudspace_note(
    cloudspace_id: str,
    location: str,
    jwt: str,
    note_id: str,
    modified_by: str,
    note_data: NoteInputsStruct,
) -> None:
    """Update cloudspace note

    Args:
        cloudspace_id (str): Cloudspace ID
        location (str): Location ID
        jwt (str): JWT token
        note_id (str): Note ID
        note_data (NoteInputsStruct): Note args
    """
    g8_client = G8Client(location, jwt=jwt)
    cs_metadata = CloudspaceMetadata.load(cloudspace_id, g8_client)
    for note in cs_metadata.notes:
        if note.id == note_id:
            break
    else:
        raise exceptions.NotFound(f"Note with id {note_id} was not found in cloudspace notes")

    note.title = note_data.title
    note.content = note_data.content
    note.last_update_time = time.time()
    note.modified_by = modified_by
    cs_metadata.save()


@job(title="delete note", retry_count=1, object_type="cloudspace-metadata", object_id="{cloudspace_id}", block=True)
def delete_cloudspace_note(cloudspace_id: str, location: str, jwt: str, note_id: str) -> None:
    """Delete cloudspace note

    Args:
        cloudspace_id (str): Cloudspace ID
        location (str): Location ID
        jwt (str): JWT token
        note_id (str): Note ID
    """
    g8_client = G8Client(location, jwt=jwt)
    cs_metadata = CloudspaceMetadata.load(cloudspace_id, g8_client)
    for note in cs_metadata.notes:
        if note.id == note_id:
            break
    else:
        raise exceptions.NotFound(f"Note with id {note_id} was not found in cloudspace notes")

    cs_metadata.notes.remove(note)
    cs_metadata.save()


# pylint: disable=W0511
#  TODO: optimize fetching child cloudspaces after addressed https://git.gig.tech/openvcloud/openvcloud/-/issues/2828
def list_cloudspace_children(cloudspace_id: str, account_id: str, location: str, jwt: str) -> List[CloudSpaceChild]:
    """List child cloudspaces

    Args:
        cloudspace_id (str): Cloudspace ID
        account_id (str): G8 account
        location (str): location ID
        jwt (str): JWT token

    Returns:
        List[CloudSpaceChild]: List of child cloudspaces
    """
    cloudspaces = G8Cloudspace.list_cloudspaces(location, account_id, jwt)
    children = []

    for cs in cloudspaces:
        if cs["cloudspace_mode"] == CloudspaceModes.NESTED and cs["cloudspace_id"] != cloudspace_id:
            cs_details = G8Client(location, jwt=jwt).get_cloudspace_info(cloudspace_id=cs["cloudspace_id"])
            if int(cs_details["external_network_id"]) == int(cloudspace_id):
                children.append(
                    CloudSpaceChild(
                        cloudspace_id=encode_base64_id(cs["cloudspace_id"], location),
                        name=cs_details["name"],
                        ip_in_parent_network=cs_details["external_network_ip"],
                        private_network=cs_details["private_network"],
                    )
                )

    return children


def get_console_url(jwt: str, location: str, vm_id: int, request_url: str) -> str:
    """Get virtual machine console URL

    Args:
        jwt (str): User JWT token
        location (str): Location name (G8)
        vm_id (int): Virtual Machine ID
        request_url (str): request URL incoming to vco portal

    Returns:
        str: Console url to be used
    """

    def _get_ws_scheme(parsed_url):
        return "wss" if parsed_url.scheme == "https" else "ws"

    console_url = G8Client(location, jwt=jwt).get_vm_console_url(vm_id)
    if not console_url:
        raise ValueError("Can only get console URL of a RUNNING machine")
    g8_ips = set(ip for ip in G8HardwareSpec.get_meneja_ips(location) if ip)
    use_cluster_proxy = any(ipaddress.ip_address(g8_ip).is_private for g8_ip in g8_ips)
    parsed_url = urlparse(console_url["url"])
    parsed_request_url = urlparse(request_url)
    if use_cluster_proxy:
        path = f"/console/{parsed_url.netloc}/{parse_qs(parsed_url.query)['token'][0]}"
        return urlunparse((_get_ws_scheme(parsed_request_url), parsed_request_url.netloc, path, "", "", ""))
    return urlunparse((_get_ws_scheme(parsed_url), parsed_url.netloc, "/websockify", "", parsed_url.query, ""))


def add_dns_record(
    vco_id: str,
    customer_id: str,
    location: str,
    cloudspace_id: int,
    jwt: str,
    domain_name: str,
    priority: int = None,
    weight: int = None,
    port: int = None,
    service: str = None,
    protocol: str = None,
    value: str = None,
    type_: SupportedDNSRecordType = SupportedDNSRecordType.A,
    ttl: int = 3600,
    vm_id: int = None,
    caa_domain: str = None,
    tag: str = None,
    flag: int = CAAFlag.ZERO.value,
    external_ip_address: str = None,
) -> None:
    """
    Add a new DNS record that will points to the cloudspace
    Args:
        vco_id (str): Virtual Cloud Operator ID
        customer_id (str): Customer ID
        location (str): The location of cloudspace
        cloudspace_id (str): Cloudspace ID
        jwt (str): JWT of the user
        domain (str): The domain that will points to this cloudspace
        priority (int): Priority 0 or higher (ignored when type not SRV or MX)
        port (int): Port number (ignored when type not SRV)
        weight (int): Weight 0 or higher (ignored when type not SRV)
        service (str): Service (ignored when type not SRV)
        protocol (str): Protocol (ignored when type not SRV)
        value (str): Value of record
        type (SupportedDNSRecordType): DNS record type
        ttl (int): Time to live,
        vm_id (int): Virtual machine id
        external_ip_address (str): External network ip address
    """
    top_level_domain = validate_domain(customer_id, domain_name, vco_id)
    if type_ == SupportedDNSRecordType.CNAME:
        if DnsRecord.list(customer_id=customer_id, domain_name=domain_name, record_type=SupportedDNSRecordType.A.value):
            raise ValueError("Adding CNAME record while having an A record for same domain is not allowed")
        if DnsRecord.list(
            customer_id=customer_id, domain_name=domain_name, record_type=SupportedDNSRecordType.AAAA.value
        ):
            raise ValueError("Adding CNAME record while having an AAAA record for same domain is not allowed")

    if type_ != SupportedDNSRecordType.SRV:
        port = None
        weight = None
        service = None
        protocol = None
    if type_ not in (SupportedDNSRecordType.SRV, SupportedDNSRecordType.MX):
        priority = None
    validate_ip = ("A", "AAAA")
    if type_ != SupportedDNSRecordType.CAA:
        tag = None
        flag = None
        caa_domain = None
    else:
        if not (caa_domain and tag and flag is not None):
            raise ValueError("CA domain tag and flag are required when creating CAA DNS record")
        if not DOMAIN_REGEX.fullmatch(caa_domain):
            raise ValidationError(
                f"""Domain {caa_domain} is not a valid domain name, only lower case alphanumeric characters,
                "-" and "." are allowed And subdomains must start and end with  lower case alphanumeric characters
        """
            )

        value = None

    if external_ip_address and vm_id and type_.value in validate_ip:
        value = external_ip_address
    if not value and type_.value != SupportedDNSRecordType.CAA:
        raise ValidationError(f"Value is required in {type_.value} records")

    if type_ == SupportedDNSRecordType.SRV:
        _validate_srv(port, weight, priority, service, protocol)
    if type_ == SupportedDNSRecordType.MX:
        if not priority:
            priority = 0
        elif priority < 0:
            raise ValidationError("Invalid priority, A priority of 0 or higher is required in MX records")
    if domain_name.endswith(VCO.get_by_id(vco_id).cloud_resource_domain["domain"]):
        soa = SOAOwner.VCO.value
    else:
        soa = SOAOwner.CUSTOMER.value
    if not vm_id and not external_ip_address and type_.value in validate_ip:
        external_networks = G8Client(location, jwt=jwt).get_cloudspace_external_networks(cloudspace_id=cloudspace_id)
        for external_network in external_networks["external_networks"]:
            if value == external_network["external_network_ip"].split("/")[0]:
                break
        else:
            raise ValidationError("IP address does not exist in cloudspace external networks")

    dns_record = DnsRecord(
        vco_id=vco_id,
        domain_name=domain_name,
        cloudspace_id=cloudspace_id,
        nic_ip=external_ip_address,
        vm_id=vm_id,
        g8_name=location,
        customer_id=customer_id,
        value=value,
        type=type_.value,
        soa_owner=soa,
        port=port,
        priority=priority,
        weight=weight,
        service=service,
        protocol=protocol,
        tag=tag,
        caa_domain=caa_domain,
        flag=flag,
        authority_domain=top_level_domain.domain,
        ttl=ttl,
    )
    generate_and_update_zone_file(DnsUpdateAction.RELOAD_ZONE, dns_record)
    dns_record.save()


def _validate_srv(port: int, weight: int, priority: int, service: str, protocol: str) -> bool:
    if not port or port < 1:
        raise ValidationError("Invalid port, A port number of 0 or higher is required in SRV records")
    if not weight:
        weight = 0
    elif weight < 0:
        raise ValidationError("Invalid weight, A weight of 0 or higher is required in SRV records")
    if not priority:
        priority = 0
    elif priority < 0:
        raise ValidationError("Invalid priority, A priority of 0 or higher is required in SRV records")
    if not service:
        raise ValidationError("Invalid service, Service is required in SRV records")
    if not protocol:
        raise ValidationError("Invalid service, Protocol is required in SRV records")


def delete_dns_records(query: Iterable[DNSRecord]) -> None:
    """Deletes a dns record from the DB and update zone files

    Args:
        query (Iterable[DNSRecord]): Query as created by the DNSRecord.list method
    """
    dns_record = None
    if query.count():
        dns_record = query[0]
    query.delete()
    if dns_record:
        generate_and_update_zone_file(DnsUpdateAction.RELOAD_ZONE, dns_record, delete=True)


def validate_domain(
    customer_id: str, domain: str, vco_id: str = None, skip: bool = False, skip_domain_name_validations: bool = False
) -> TopLevelDomainStruct:
    """Validate domain name

    Args:
        customer_id (str): Customer ID
        domain (str): Domain name
        vco_id (str): VCO ID
        skip (bool, optional) defaults to False: If true will skip raising an error.  defaults to False
        skip_domain_name_validations (bool, optional): Skip domain name validations.  defaults to False
    Returns:
        TopLevelDomainStruct: The top level domain Struct of VCO/Customer
    """
    if not skip_domain_name_validations and not DOMAIN_REGEX.fullmatch(domain):
        raise ValueError(
            f"""Domain {domain} is not a valid domain name, only lower case alphanumeric characters, "-" and "."
    are allowed And subdomains must start and end with  lower case alphanumeric characters
    """
        )
    vco_id = vco_id or Customer.get_by_id(customer_id).vco
    vco = VCO.get_by_id(vco_id)
    if vco.cloud_resource_domain and vco.cloud_resource_domain["domain"]:
        if domain != vco.cloud_resource_domain.domain and domain.endswith(vco.cloud_resource_domain.domain):
            return vco.cloud_resource_domain
    from meneja.business.vco.customer import get_customer_top_level_domains  # pylint: disable=C0415, R0401

    for top_level_domain in get_customer_top_level_domains(customer_id):
        if domain.endswith(top_level_domain.domain) and top_level_domain.valid:
            return top_level_domain
    if not skip:
        raise ValueError("Invalid domain, the domain must be sub from your top level domains")


def remove_cloudspace_external_network(
    customer_id: str,
    cloudspace_id: int,
    jwt: str,  # pylint: disable=unused-argument
    location: str,  # pylint: disable=unused-argument
    force: bool = False,
    **kwargs,  # pylint: disable=unused-argument
) -> None:
    """Remove an external network from cloudspace

    Args:
        customer_id (str): Customer ID
        cloudspace_id (int):
        location (str): Location
        jwt (str): JWT
        force (bool): If true will delete all dns records of the network
    """
    used_dns_records = set()
    dns_records = DnsRecord.list(
        customer_id=customer_id, cloudspace_id=cloudspace_id, value=kwargs["external_network_ip"].split("/")[0]
    )
    if force:
        delete_dns_records(dns_records)
    else:
        for dns_record in dns_records:
            used_dns_records.add(dns_record.domain_name)
        if used_dns_records:
            raise ValueError(
                f""""You cannot remove an external network while there are DNS records tied
                to it ({', '.join(used_dns_records)})"""
            )

    if not kwargs["external_network_id"].isdigit():
        _, kwargs["external_network_id"] = decode_validate_cloudspace_id(kwargs["external_network_id"])
    G8Client(location, jwt=jwt).remove_external_network_from_cloudspace(cloudspace_id=cloudspace_id, **kwargs)


@check_and_modify_resource_limit_error()
def add_cloudspace_external_network(
    customer_id: str, cloudspace_id: int, jwt: str, location: str, **kwargs  # pylint: disable=unused-argument
) -> dict:
    """Add External network to cloudspace

    Args:
        customer_id (str)
        cloudspace_id (int): cloudspace_id
        jwt (str): _JWT
        location (str): Location of cloudspace
    """
    if kwargs["external_network_type"] == ExternalNetworkType.CLOUDSPACE:
        _, kwargs["external_network_id"] = decode_validate_cloudspace_id(kwargs["external_network_id"])
    elif kwargs["external_network_type"] != ExternalNetworkType.EXTERNAL_NETWORK:
        raise ValueError(f"External network type: {kwargs['external_network_type']}, is not supported")
    return G8Client(location, jwt=jwt).add_external_network_to_cloudspace_v2(cloudspace_id=cloudspace_id, **kwargs)


def get_cloudspace_external_networks(
    cloudspace_id: int, jwt: str, location: str, **kwargs
) -> List[CloudspaceExternalNetworkStruct]:
    """Get cloudspace external networs

    Args:
        cloudspace_id (int): cloudspace ID
        jwt (str): JWT
        location (str): Location

    Returns:
        List[CloudspaceExternalNetworkStruct]: List of all cloudspace external networks
    """
    external_networks = G8Client(location, jwt=jwt).get_cloudspace_external_networks(
        cloudspace_id=cloudspace_id, **kwargs
    )
    results = []
    for external_network in external_networks["external_networks"]:
        if external_network["type"] == ExternalNetworkType.CLOUDSPACE:
            external_network["external_network_id"] = encode_base64_id(
                external_network["external_network_id"], location
            )
        elif external_network["type"] == ExternalNetworkType.EXTERNAL_NETWORK:
            external_network["external_network_id"] = str(external_network["external_network_id"])
        results.append(CloudspaceExternalNetworkStruct.load(external_network))
    return results


@check_and_modify_resource_limit_error()
def update_cloudspace_external_network(
    customer_id: str, cloudspace_id: int, jwt: str, location: str, **kwargs  # pylint: disable=unused-argument
) -> None:
    """Update cloudspace external network

    Args:
        customer_id (Str): customer id
        cloudspace_id (int): Cloudspace id
        jwt (str): JWT
        location (str): Location
    """
    if not kwargs["external_network_id"].isdigit():
        _, kwargs["external_network_id"] = decode_validate_cloudspace_id(kwargs["external_network_id"])

    return G8Client(location, jwt=jwt).update_external_network_metric(cloudspace_id=cloudspace_id, **kwargs)


@check_and_modify_resource_limit_error()
def enable_cloudspace(
    customer_id: str, cloudspace_id: str, location: str, jwt: str, **kwargs  # pylint: disable=unused-argument
) -> None:
    """Enable cloudspace

    Args:
        customer_id (str): customer ID
        cloudspace_id (str): Cloudspace ID
        location (str): Location name
        jwt (str): JWT

    """
    return G8Client(location, jwt=jwt).enable_cloudspace(cloudspace_id, **kwargs)


def list_available_subnets(customer_id: str, jwt: str, number_of_networks: int = 1) -> List[SubnetStruct]:
    """List available private networks

    Args:
        customer_id (str): customer id
        location (str): location of cloudspace
        jwt (str): JWT
        number_of_networks (int, optional): Number of private networks. Defaults to 1.

    Returns:
        List[SubnetStruct]: List of available private networks
    """
    _, cloudspace_list = list_vco_customer_cloudspaces(customer_id, jwt)
    free_subnets = []
    cloudspace_ips = [netaddr.IPNetwork(cloudspace["private_network"]) for cloudspace in cloudspace_list]

    # Search for available subnets in the range of ************/24 – *************/24
    for i in range(10, 254):
        if netaddr.IPNetwork(f"192.168.{i}.0/24") not in cloudspace_ips:
            free_subnets.append(SubnetStruct(ip_address=f"192.168.{i}.0/24"))
        if len(free_subnets) == number_of_networks:
            break

    # Search for available subnets in the range of **********/24 – ************/24
    if len(free_subnets) != number_of_networks:
        for i in range(16, 31):
            for j in range(254):
                if netaddr.IPNetwork(f"172.{i}.{j}.0/24") not in cloudspace_ips:
                    free_subnets.append(SubnetStruct(ip_address=f"172.{i}.{j}.0/24"))
                if len(free_subnets) == number_of_networks:
                    break
            if len(free_subnets) == number_of_networks:
                break

    # Search for available subnets in the range of 10.0.0.0/24 – ************/24
    if len(free_subnets) != number_of_networks:
        for i in range(255):
            for j in range(254):
                if netaddr.IPNetwork(f"10.{i}.{j}.0/24") not in cloudspace_ips:
                    free_subnets.append(SubnetStruct(ip_address=f"10.{i}.{j}.0/24"))
                if len(free_subnets) == number_of_networks:
                    break
            if len(free_subnets) == number_of_networks:
                break

    return free_subnets


def list_connected_vms(location: str, jwt: str, cloudspace_id: int) -> List[VMModel]:
    """List VMs connected to a cloudspace

    Args:
        location (str, required): CS Location
        jwt (str, required): User's jwt
        cloudspace_id (int, required): CS ID on G8

    Returns:
        List[VMModel]: List of connected VMs
    """
    connected_vms = G8Client(location, jwt=jwt).list_vms(cloudspace_id, include_connected=True, exclude_internal=True)
    for vm in connected_vms:
        vm["ip_addresses"] = {}
        counter = 1
        for nic in vm["network_interfaces"]:
            if nic["network_id"] == cloudspace_id:
                vm["ip_addresses"][str(counter)] = nic["ip_address"]
                counter = counter + 1
        vm["cloudspace_id"] = encode_base64_id(vm["cloudspace_id"], location)
    return connected_vms


def validate_storage_location(location: str, resource_type: StorageOnlyResources) -> None:
    """Validate that location is not storage only

    Args:
        location (str): location name
        resource_type (StorageOnlyResources): Resource type

    Raises:
        ValueError: Cannot deploy {resource_type} on storage-only cloud location
    """
    if G8Info.get_by_name(location, only=["storage_only"]).storage_only:
        raise ValueError(f"Cannot deploy {resource_type.value} on storage-only cloud location")


def _get_wireguard_interface_struct(wireguard):
    wireguard = WireguardInterfaceStruct(
        name=wireguard.name,
        address=wireguard.address,
        port=wireguard.port,
        peers=wireguard.peers,
        mtu=wireguard.mtu,
        public_key=wireguard.public_key,
    )

    wireguard.decode_interface_name()

    return wireguard


def list_remote_wireguard_interfaces(location: str, cloudspace_id: int, jwt: str) -> List[WireguardInterfaceStruct]:
    """List remote cloudspaces connected via wireguard VPN
    Args:
        location (str): Location of source cloudspace
        customer_id (str): Customer ID
        cloudspace_id (int): Cloudspace ID (decoded)
        jwt (str): User JWT token

    Returns:
        List[WireguardInterfaceStruct]: List of remote wireguard connections
    """
    wireguards = G8ClientApiV1(location, jwt=jwt).cloudspaces.get_g8_cloudspace_wireguard_config(cloudspace_id)
    wireguards = [_get_wireguard_interface_struct(wireguard) for wireguard in wireguards]

    return wireguards


def get_remote_wireguard_interface(
    interface_id, location: str, cloudspace_id: int, jwt: str
) -> WireguardInterfaceStruct:
    """Get cloudspaces wireguard configuration
    Args:
        interface_id (str): Wireguard interface ID
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        jwt (str): User JWT token

    Returns:
        WireguardInterfaceStruct: remote wireguard configuration
    """
    wireguards = G8ClientApiV1(location, jwt=jwt).cloudspaces.get_g8_cloudspace_wireguard_config(cloudspace_id)
    for wireguard in wireguards:
        if wireguard.name == interface_id:
            return _get_wireguard_interface_struct(wireguard)

    raise KeyError("Interface not found")


def delete_remote_wireguard_connection(interface_id: str, location, cloudspace_id: int, jwt: str):
    """Delete cloudspaces wireguard configuration
    Args:
        interface_id (str): Wireguard interface ID
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        jwt (str): User JWT token

    """
    decoded_id = WireGuardInterfaceName.from_encoded(interface_id)
    if decoded_id.type != WgInterfaceType.WG.value:
        raise ValueError("Can not delete wireguard interface used for connected cloudspaces")

    G8ClientApiV1(location, jwt=jwt).cloudspaces.delete_g8_cloudspace_wireguard_interface(cloudspace_id, interface_id)

    return


def _validate_wireguard_payload(payload):
    try:
        netaddr.IPNetwork(payload.address)
    except Exception as exc:
        # Add more context to the exception raised from g8 side
        raise ValueError("Invalid Interface address") from exc

    for peer in payload.peers:
        for allowed_ip in peer["allowed_ips"]:
            try:
                netaddr.IPNetwork(allowed_ip["network"])
            except Exception as exc:
                # Add more context to the exception raised from g8 side
                raise ValueError(f"Invalid address {allowed_ip['network']} in allowed IPs") from exc

    return


def add_wireguard_interface(
    location: str,
    cloudspace_id: int,
    jwt: str,
    payload: WireguardInterfaceCreateStruct,
):
    """Add cloudspaces wireguard configuration
    Args:
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        jwt (str): User JWT token
        payload(WireguardInterfaceCreateStruct): payload of wireguard configuration

    """

    _validate_wireguard_payload(payload)

    payload.name = payload.encoded_interface_name
    G8ClientApiV1(location, jwt=jwt).cloudspaces.add_g8_cloudspace_wireguard_interface_config(cloudspace_id, payload)

    return {"interface_id": payload.name}


def update_wireguard_interface(
    location: str, cloudspace_id: int, interface_id: str, jwt: str, payload: WireguardInterfaceCreateStruct
):
    """Update cloudspaces wireguard configuration
    Args:
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        interface_id (str): Wireguard interface ID
        jwt (str): User JWT token
        payload(WireguardInterfaceCreateStruct): payload of wireguard configuration
    """
    decoded_id = WireGuardInterfaceName.from_encoded(interface_id)
    if decoded_id.type != WgInterfaceType.WG:
        raise ValueError("Can not edit wireguard interface used for connected cloudspaces")

    _validate_wireguard_payload(payload)

    payload.name = payload.encoded_interface_name
    G8ClientApiV1(location, jwt=jwt).cloudspaces.update_g8_cloudspace_wireguard_interface(
        cloudspace_id, interface_id, payload
    )

    return


def add_wireguard_peer(
    location: str,
    cloudspace_id: int,
    interface_id,
    jwt: str,
    payload: PeerStruct,
):
    """Add wireguard  peer
    Args:
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        interface_id (str): Wireguard interface ID
        jwt (str): User JWT token
        payload(PeerStruct): payload of wireguard peers

    """

    G8ClientApiV1(location, jwt=jwt).cloudspaces.add_g8_cloudspace_wireguard_peer(cloudspace_id, interface_id, payload)

    return


def _decode_peer_name(peer_name: str) -> Dict[str, str]:
    try:
        return json.loads(base64.b64decode(peer_name).decode("utf-8"))
    except (base64.binascii.Error, UnicodeDecodeError, json.JSONDecodeError):
        return {}


def delete_wireguard_peer(interface_id: str, peer_name: str, location, cloudspace_id: int, jwt: str):
    """Delete wireguard peer
    Args:
        interface_id (str): Wireguard interface ID
        peer name (str): Peer name
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        jwt (str): User JWT token

    """
    decoded_peer_name = _decode_peer_name(peer_name)

    if decoded_peer_name.get("type", "") == WgInterfaceType.CONNECTED_CLOUDSPCASES:
        raise ValueError("Can not delete peer of wireguard interface used for connected cloudspaces")

    G8ClientApiV1(location, jwt=jwt).cloudspaces.delete_g8_cloudspace_wireguard_peer(
        cloudspace_id, interface_id, peer_name
    )
    return


def update_wireguard_peer(
    location: str,
    cloudspace_id: int,
    interface_id,
    peer_name,
    jwt: str,
    payload: PeerStruct,
):
    """Update wireguard  peer
    Args:
        location (str): Location of source cloudspace
        cloudspace_id (int): Source Cloudspace ID (decoded)
        interface_id (str): Wireguard interface ID
        peer_name (str): Peer name
        jwt (str): User JWT token
        payload(PeerStruct): payload of wireguard peers

    """
    decoded_peer_name = _decode_peer_name(peer_name)

    if decoded_peer_name.get("type", "") == WgInterfaceType.CONNECTED_CLOUDSPCASES:
        raise ValueError("Can not edit peer of wireguard interface used for connected cloudspaces")

    G8ClientApiV1(location, jwt=jwt).cloudspaces.update_g8_cloudspace_wireguard_peer(
        cloudspace_id, interface_id, peer_name, payload
    )

    return
