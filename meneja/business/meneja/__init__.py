# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
import dataclasses
import hashlib
import io
import itertools
import json
import logging
import os
import re
import time
import urllib
import uuid
from collections import defaultdict
from copy import deepcopy
from functools import wraps
from random import choice
from string import digits
from time import sleep
from typing import List, <PERSON><PERSON>, Union

import docker
import gevent
from dateutil.parser import parse
from dns import resolver
from dynaqueue.client import Client
from dynaqueue.client.client import TaskStatusResultFuture
from dynaqueue.models import TaskStatus
from dynaqueue.models.task_queue_item import Task, TaskQueueItem
from dynaqueue.scheduler import schedule
from dynaqueue.server import STATUS_FAILED, STATUS_QUEUED, STATUS_RUNNING, STATUS_SUCCEEDED
from dynaqueue.worker.context import get_context
from flask import g as flask_g
from minio import Minio
from mongoengine.errors import DoesNotExist
from pymongo import MongoClient
from pymongo.database import Database
from werkzeug.exceptions import BadRequest

from meneja.business.error import add_stack_frames
from meneja.business.g8 import list_g8s
from meneja.business.security import validate_password
from meneja.business.vco.audit_log_handler import AuditLogHandler
from meneja.business.vco.customer.cloudspace import decode_validate_cloudspace_id
from meneja.jobs import dynaqueue_health_check, job
from meneja.lib import k8
from meneja.lib.connection import DynaqueueConnection, MinioConnection, MongoConnection
from meneja.lib.enumeration import (
    CloudResourceType,
    ComponentStatus,
    EnvironmentName,
    MenejaInstanceLocation,
    MenejaMode,
)
from meneja.lib.enumeration import MenejaStatus as MenejaStatusEnum
from meneja.lib.utils import (
    decode_validate_base64_id,
    mnj_mode,
    redis_cache,
    rocket_chat_channel,
    rocket_chat_message,
    telegram_group,
    telegram_message,
)
from meneja.model.alarm_room import AlarmRoomUsers
from meneja.model.audit import AuditLog, Request, Response
from meneja.model.dns_records import DnsRecord
from meneja.model.g8 import G8FiveMinuteStatus
from meneja.model.healthchecks import (
    BackupS3,
    CustomerBICollection,
    EmailsExport,
    FailedUpdateCertificate,
    FailedUpdateClusterCertificate,
    HealthChecksLog,
    HealthLogErrorInstance,
    UpdateCertificate,
    UpdateClusterCertificate,
)
from meneja.model.notifications import EmailOutbox
from meneja.model.tasks import TaskLogRecord, WorkflowInfo
from meneja.model.vco import VCO, Download
from meneja.structs.meneja.dataclasses.k8s_certs import K8sCertValidity

logger = logging.getLogger()


class MenejaStatusDetail:
    """Stores the status of the components that make up the meneja service"""

    mongodb: ComponentStatus
    collectors: ComponentStatus
    redis: ComponentStatus
    minio: ComponentStatus
    dynaqueue: ComponentStatus
    dynaqueue_worker: "MenejaStatusDetail"
    dns: ComponentStatus
    meneja_backup: ComponentStatus
    iam_backup: ComponentStatus
    vco_operator: ComponentStatus
    customer_bi_collection: ComponentStatus
    k8s_certificates: List[K8sCertValidity]
    notification_emails: ComponentStatus
    update_certificates: ComponentStatus
    update_cluster_certificate: ComponentStatus
    nodes: ComponentStatus
    backup_s3: ComponentStatus
    emails_export: ComponentStatus


@dataclasses.dataclass
class MenejaStatusVCODownload:
    """Stores status of VCO downloads"""

    vco_id: str
    cli: ComponentStatus
    tfp: ComponentStatus
    csi: ComponentStatus


class MenejaStatus:
    """Stores Meneja health status information"""

    detail: MenejaStatusDetail
    vco_downloads: List[MenejaStatusVCODownload]

    def __init__(self):
        self.detail = MenejaStatusDetail()

    @property
    def status(self) -> MenejaStatusEnum:
        """
        Calculates the meneja status from the detail

        :return: The meneja status
        :rtype: MenejaStatusEnum
        """
        critical_checks = [
            self.detail.mongodb,
            self.detail.collectors,
            self.detail.redis,
            self.detail.minio,
            self.detail.dynaqueue,
            self.detail.dns,
            self.detail.vco_operator,
            self.detail.nodes,
            self.detail.backup_s3,
        ]
        non_critical_checks = [
            self.detail.meneja_backup,
            self.detail.customer_bi_collection,
            self.detail.iam_backup,
            self.detail.notification_emails,
            self.detail.update_certificates,
            self.detail.update_cluster_certificate,
            self.detail.emails_export,
        ]
        if ComponentStatus.ERROR in critical_checks:
            return MenejaStatusEnum.CRITICAL
        if ComponentStatus.ERROR in non_critical_checks or self.vco_downloads:
            return MenejaStatusEnum.WARNING
        return MenejaStatusEnum.OK


@redis_cache(30 * 60)
def health_status() -> MenejaStatus:
    """
    Checks the health status of the cluster
    """
    if EnvironmentName.current() == EnvironmentName.DEV:
        return MenejaStatus()
    logger.info("Checking meneja health")
    task: TaskStatusResultFuture = _check_vco_downloads()
    ms = MenejaStatus()
    tasks = [
        gevent.spawn(_get_mongodb_status),
        gevent.spawn(_get_collectors_status),
        gevent.spawn(_get_redis_status),
        gevent.spawn(_get_minio_status),
        gevent.spawn(_get_dynaqueue_status),
        gevent.spawn(_get_dynaqueue_worker_status),
        gevent.spawn(_get_dns_status),
        gevent.spawn(_get_mongo_backup_status, "mongodb-meneja"),
        gevent.spawn(_get_mongo_backup_status, "mongodb-iam"),
        gevent.spawn(_monitor_operator, in_cluster=mnj_mode() != MenejaMode.DEBUG),
        gevent.spawn(_get_customer_bi_collection),
        gevent.spawn(_get_k8s_certificates_status),
        gevent.spawn(_get_email_outbox_status),
        gevent.spawn(_get_update_certificate_status),
        gevent.spawn(_get_update_cluster_certificate_status),
        gevent.spawn(_get_node_status),
        gevent.spawn(_get_backup_s3_status),
        gevent.spawn(_get_emails_export),
    ]
    gevent.joinall(tasks)
    ms.detail.mongodb = tasks[0].get()
    ms.detail.collectors = tasks[1].get()
    ms.detail.redis = tasks[2].get()
    ms.detail.minio = tasks[3].get()
    ms.detail.dynaqueue = tasks[4].get()
    ms.detail.dynaqueue_worker = tasks[5].get()
    ms.detail.dns = tasks[6].get()

    ms.detail.meneja_backup = tasks[7].get()
    ms.detail.iam_backup = tasks[8].get()
    ms.detail.vco_operator = tasks[9].get()
    ms.detail.customer_bi_collection = tasks[10].get()
    ms.detail.k8s_certificates = tasks[11].get()
    ms.detail.notification_emails = tasks[12].get()
    ms.detail.update_certificates = tasks[13].get()
    ms.detail.update_cluster_certificate = tasks[14].get()
    ms.detail.nodes = tasks[15].get()
    ms.detail.backup_s3 = tasks[16].get()
    ms.detail.emails_export = tasks[17].get()

    def stat(vco: VCO, check: str) -> ComponentStatus:
        download: Download
        for download in vco.downloads or []:
            if download.download == check:
                return ComponentStatus.from_string(download.status)
        return ComponentStatus.ERROR

    task.get_result()
    ms.vco_downloads = []
    for vco in VCO.list(active_only=True, only=["vco_id", "downloads"]):
        cli = stat(vco, "cli")
        tfp = stat(vco, "tfp")
        csi = stat(vco, "csi")
        if ComponentStatus.ERROR in (cli, tfp, csi):
            ms.vco_downloads.append(MenejaStatusVCODownload(vco_id=vco.vco_id, cli=cli, tfp=tfp, csi=csi))

    return ms


def _store_error_log(exception: Exception, component: str):
    timestamp = int(time.time())
    traceback_hash = hashlib.md5(str(exception).encode()).hexdigest()
    try:
        healthcheck_log = HealthChecksLog.get_by_trace_hash(traceback_hash=traceback_hash)
        healthcheck_log.last_occurred_at = timestamp
        healthcheck_log.occurrences_count += 1
        healthcheck_log.occurrences.append(HealthLogErrorInstance(timestamp=timestamp))
    except DoesNotExist:
        healthcheck_log = HealthChecksLog(
            last_occurred_at=timestamp,
            error_message=str(exception),
            occurrences_count=1,
            traceback_hash=traceback_hash,
            meneja_component=component,
        )
        if not isinstance(exception, str):
            healthcheck_log.stack = add_stack_frames(exception)
        healthcheck_log.occurrences.append(HealthLogErrorInstance(timestamp=timestamp))
    healthcheck_log.save()


@redis_cache(3600)
def _get_customer_bi_collection() -> ComponentStatus:
    try:
        logger.info("Checking customer BI collection health...")
        MongoConnection.get_client()
        collection = CustomerBICollection.get_last()
        if collection.failed_jobs:
            for failed_job in collection.failed_jobs:
                logger.info("Collection failure: https://meneja.gig.tech/workflows/%s", failed_job)
                _store_error_log(
                    exception=f"Collection failure: https://meneja.gig.tech/workflows/{failed_job}",
                    component="Customer BI collection",
                )
            return ComponentStatus.ERROR
        logger.info("Customer BI collection is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        logger.exception("Could not connect to Mongodb")
        _store_error_log(exception=e, component="Customer BI collection")
        return ComponentStatus.ERROR


def _heath_check(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        start = time.time()
        try:
            return f(*args, **kwargs)
        finally:
            end = time.time()

            def report():
                elapsed = int(end - start)
                if elapsed > 30:
                    message = f"Function {f.__name__} took more than {elapsed} seconds to calculate!"
                    rocket_chat_message(rocket_chat_channel(), message)

            gevent.spawn_later(0, report)

    return wrapper


@_heath_check
def _get_mongodb_status() -> ComponentStatus:
    try:
        logger.info("Checking Mongodb health...")
        client: MongoClient = MongoConnection.get_client()
        admin_db: Database = client.get_database("admin")
        status = admin_db.command("replSetGetStatus")
        states = set({member["stateStr"] for member in status["members"]})
        if {"PRIMARY", "SECONDARY"} != states:
            raise RuntimeError("Mongo replicaset is not healthy")
        list_g8s()
        logger.info("Mongodb is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        _store_error_log(exception=e, component="Mongo")
        logger.exception("Mongo is not healthy")
        return ComponentStatus.ERROR


@job(
    title="Checking Mongodb health...",
    retry_count=1,
    object_id="mongo",
    object_type="health-check",
    is_single=True,
)
@_heath_check
def _get_mongodb_status_job() -> ComponentStatus:
    return _get_mongodb_status()


@_heath_check
def _get_collectors_status() -> ComponentStatus:
    logger.info("Checking collectors health...")
    now = time.time()
    five = G8FiveMinuteStatus.get_last_twelve().first()
    if not five or five.scheduled_at + 9 * 60 < now:
        return ComponentStatus.ERROR
    logger.info("Collectors are healthy")
    return ComponentStatus.OK


@_heath_check
def _get_redis_status() -> ComponentStatus:
    try:
        logger.info("Checking Redis health...")
        dnq_client = DynaqueueConnection.get_client()
        dnq_client._connect()  # pylint: disable=protected-access
        logger.info("Redis info: %s", dnq_client._redis.info())  # pylint: disable=protected-access
        logger.info("Redis is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        _store_error_log(exception=e, component="Redis")
        logger.exception("Failure while checking redis status")
        return ComponentStatus.ERROR


@job(
    title="Checking Redis health...",
    retry_count=1,
    object_id="redis",
    object_type="health-check",
    is_single=True,
)
@_heath_check
def _get_redis_status_job() -> ComponentStatus:
    return _get_redis_status()


@_heath_check
def _get_minio_status() -> ComponentStatus:
    try:
        # Create a random bucket, this to prevent error when the API would be called
        # more than once at the same time.
        logger.info("Checking Minio health...")
        bucket_name = "health-check"
        randomized_part = "".join([choice(digits) for _ in range(7)])
        object_name = f"health-check-object-{randomized_part}"
        test_data = str(uuid.uuid4()).encode()
        miniocl: Minio = MinioConnection.get_client()
        if not miniocl.bucket_exists(bucket_name):
            miniocl.make_bucket(bucket_name)
        miniocl.put_object(bucket_name, object_name, io.BytesIO(test_data), len(test_data))
        response = miniocl.get_object(bucket_name, object_name).data
        miniocl.remove_object(bucket_name, object_name)
        if response == test_data:
            logger.info("Minio is healthy")
            return ComponentStatus.OK
        raise RuntimeError("Minio health checks failed")
    except RuntimeError as exp:  # pylint: disable=broad-except
        logger.exception("Failure while checking minio status")
        _store_error_log(exception=exp, component="Minio")
        return ComponentStatus.ERROR


@job(
    title="Checking Minio health...",
    retry_count=1,
    object_id="minio",
    object_type="health-check",
    is_single=True,
)
@_heath_check
def _get_minio_status_job() -> ComponentStatus:
    return _get_minio_status()


@_heath_check
def _get_dynaqueue_status() -> ComponentStatus:
    try:
        logger.info("Checking Dynaqueue health status...")
        with gevent.Timeout(5):
            dynaqueue_health_check.run().get_result()
            logger.info("Dynaqueue is running fine")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        logger.exception("Failure while checking Dynaqueue status")
        _store_error_log(exception=e, component="Dynaqueue")
        return ComponentStatus.ERROR


@job(
    title="Checking Dynaqueue health status...",
    retry_count=1,
    object_id="dynaqueue",
    object_type="health-check",
    is_single=True,
)
@_heath_check
def _get_dynaqueue_status_job() -> ComponentStatus:
    return _get_dynaqueue_status()


def _check_job_result(health_check_job):
    try:
        return health_check_job.get_result(timeout=5).result_value
    except Exception as e:  # pylint: disable=broad-except
        _store_error_log(exception=e, component="Dynaqueue worker")
        return ComponentStatus.ERROR


@_heath_check
def _get_dynaqueue_worker_status() -> MenejaStatusDetail:
    logger.info("Checking Dynaqueue worker health status...")
    result = MenejaStatusDetail()
    mongodb_job = _get_mongodb_status_job()
    redis_job = _get_redis_status_job()
    minio_job = _get_minio_status_job()
    dynaqueue_job = _get_dynaqueue_status_job()
    result.mongodb = _check_job_result(mongodb_job)
    result.redis = _check_job_result(redis_job)
    result.minio = _check_job_result(minio_job)
    result.dynaqueue = _check_job_result(dynaqueue_job)
    return result


@job(
    title="Checking VCO downloads",
    retry_count=1,
    timeout=60,
    block=False,
    object_id="vco-downloads",
    object_type="health-check",
    is_single=True,
)
def _check_vco_downloads() -> None:
    if EnvironmentName.current() == EnvironmentName.DEV:
        return
    now = time.time()
    check_list = {"cli", "csi", "tfp"}
    vco_checks: List[TaskStatusResultFuture] = list()
    for vco in VCO.list(active_only=True, only=["vco_id", "downloads"]):
        download_statuses = {check: False for check in check_list}  # check -> is_ok
        download_statuses.update(
            {
                download.download: download.status == ComponentStatus.OK.value and now - download.updated_at < 24 * 3600
                for download in vco.downloads or []
            }
        )
        extra_checks = [check for check, ok in download_statuses.items() if not ok]
        if extra_checks:
            vco_checks.append(_check_vco_download(vco.vco_id, vco.vco_id, extra_checks))
    for check in vco_checks:
        check.get_result(55)


@job(
    title="Checking VCO downloads for {vco_name}",
    retry_count=1,
    timeout=30,
    block=False,
    queue="imagebuilder",
    object_type="vco-downloads-check",
    object_id="{vco_id}",
    is_single=True,
)
def _check_vco_download(vco_id, vco_name, checks) -> None:  # pylint: disable=unused-argument
    vco = VCO.get_by_id(vco_id)
    platforms = ("linux", "windows", "darwin")

    def cli(platform):
        with urllib.request.urlopen(f"https://{vco.domain}/api/1/utilities/cli?platform={platform}") as req:
            return req.getcode() == 200 and req.read(1024)

    def tfp(platform):
        with urllib.request.urlopen(
            f"https://{vco.domain}/api/1/utilities/terraform-provider?platform={platform}"
        ) as req:
            return req.getcode() == 200 and req.read(1024)

    def csi():
        image = f"{vco.domain}/csi-driver:latest"
        client = docker.from_env(timeout=25)
        try:
            client.images.get(image)
            client.images.remove(image)
        except docker.errors.ImageNotFound:
            pass
        client.images.pull(image)
        client.images.remove(image)

    jobs = list()
    for platform in platforms:
        if "cli" in checks:
            jobs.append(("cli", gevent.spawn(cli, platform)))
        if "tfp" in checks:
            jobs.append(("tfp", gevent.spawn(tfp, platform)))
    if "csi" in checks:
        jobs.append(("csi", gevent.spawn(csi)))

    failed_checks = set()
    for check, _job in jobs:
        try:
            _job.get()
        except:  # pylint: disable=bare-except  # noqa: E722
            failed_checks.add(check)

    now = int(time.time())
    download: Download
    for check in checks:
        for download in vco.downloads or []:
            if download.download == check:
                break
        else:
            download = Download()
            download.download = check
            if vco.downloads is None:
                vco.downloads = list()
            vco.downloads.append(download)
        download.updated_at = now
        download.status = ComponentStatus.ERROR.value if check in failed_checks else ComponentStatus.OK.value
    if checks:
        vco.save()


def _get_dns_status() -> ComponentStatus:
    logger.info("Checking DNS health...")
    dns_answers = []
    meneja_domain = "meneja.gig.tech"

    client: Client = DynaqueueConnection.get_client()
    queues = MenejaInstanceLocation.values()
    queue: str = None
    for queue in queues:
        task = Task()
        task.set_workload(_check_dns)
        task.queue = queue
        task.title = f"DNS check in {queue}"
        task.timeout = 60
        result: TaskStatus = client.submit_task(task, timeout=60)
        if result.result_value:
            dns_answers.append(result.result_value)
            logger.info("Worker %s resolved to %s", queue.upper(), result.result_value)
        else:
            logger.info("DNS check failed in queue: %s", queue)
    if len(dns_answers) < 2:
        logger.info("Two or more queues failed to resolve DNS, nothing to check")
        return ComponentStatus.ERROR

    if len(dns_answers) < 3:
        logger.info("One or more queues failed to resolve DNS, will check for what already exists: %s", dns_answers)

    for answer_1, answer_2 in itertools.combinations(dns_answers, 2):
        logger.info("comparing %s and %s", answer_1, answer_2)
        if answer_1 != answer_2:
            logger.info("DNS is unhealthy, resolving %s returned inconsistent result: %s", meneja_domain, dns_answers)
            _store_error_log(
                f"DNS is unhealthy, resolving {meneja_domain} returned inconsistent result: {dns_answers}", "DNS"
            )
            return ComponentStatus.ERROR
    if len(dns_answers) == 3:
        logger.info("DNS is healthy")
        return ComponentStatus.OK
    return ComponentStatus.ERROR


def _check_dns():
    task_queue_item: TaskQueueItem = get_context()
    queue = task_queue_item.task.queue
    logger.info("Running in meneja instance %s", queue)
    nameserver = os.environ.get("WORKER_DNS_RESOLVER")
    meneja_domain = "meneja.gig.tech"
    try:
        dns_resolver = resolver.Resolver()
        dns_resolver.nameservers = [nameserver]
        dns_answer = dns_resolver.resolve(meneja_domain)[0]
        logger.info("Answer is %s:", dns_answer)
        return dns_answer
    except Exception as exp:  # pylint: disable=broad-except
        logger.exception("Failed to resolve dns using nameserver %s", nameserver)
        _store_error_log(exp, component=f"DNS-{queue}")
        return None  # So we can know that if dns resolving failed in this queue


def _get_k8s_certificates_status() -> List[K8sCertValidity]:
    certs = k8.get_certificates_validity()
    current_time = time.time() / (60 * 60 * 24)
    chat_id = telegram_group()
    result = []
    for cert in certs:
        time_to_expire = cert.days_left - current_time
        if time_to_expire <= 10:
            telegram_message(chat_id=chat_id, message=f" {time_to_expire} days left for {cert.cert_name}")
            result.append(
                K8sCertValidity(cert_name=cert.cert_name, days_left=0 if time_to_expire < 0 else time_to_expire)
            )
    return result


def _get_email_outbox_status() -> ComponentStatus:
    try:
        logger.info("Checking email outbox health...")
        emails = EmailOutbox.list()
        if len(emails) > 0:
            raise RuntimeError("Email outbox is not empty")
        logger.info("Email outbox is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        _store_error_log(exception=e, component="EmailOutbox")
        logger.exception("Email outbox is not healthy")
        return ComponentStatus.ERROR


def _get_mongo_backup_status(host_name: str) -> ComponentStatus:
    try:
        logger.info("Checking %s backup...", host_name)
        restic_password = os.getenv("RESTIC_PASSWORD")
        minio_sec = os.getenv("MONGO_BACKUP_MINIO_ACCESS_SECRET")
        minio_key = os.getenv("MONGO_BACKUP_MINIO_ACCESS_KEY")
        restic_repo = os.getenv("RESTIC_REPO")
        base_command = [
            "env",
            f"RESTIC_PASSWORD={restic_password}",
            f"AWS_SECRET_ACCESS_KEY={minio_sec}",
            f"AWS_ACCESS_KEY_ID={minio_key}",
            "restic",
            "--json",
            "-r",
            restic_repo,
            "snapshots",
            "--host",
            host_name,
            "--no-lock",
        ]
        query_result = gevent.subprocess.run(base_command, capture_output=True, check=True)
        restic_snapshots = json.loads(query_result.stdout)

        if _last_backup_check(restic_snapshots):
            logger.info("%s was backed up recently", host_name)
            return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        logger.info(e)
        e.args = e.args + (host_name,)
        _store_error_log(exception=e, component="Backup")
        return ComponentStatus.ERROR


def _last_backup_check(snapshots: list) -> bool:
    if not snapshots:
        raise KeyError("Backups don't exist")
    current_date = time.time()
    snapshot = snapshots[-1]
    snapshot_date = int(time.mktime(parse(snapshot["time"]).timetuple()))
    difference = current_date - snapshot_date
    if difference / (60 * 60 * 24) <= 2:
        return True
    raise KeyError("No Backups were made recently")


def _protect_request_date(request):
    fields = {"secret", "password", "client_secret", "tpm_secret", "secret_key", "access_key", "restic_password"}

    def redact(data):
        if isinstance(data, dict):
            return {key: "****************" if key in fields else redact(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [redact(item) for item in data]
        return data

    args = dict(request.args)
    redacted_args = redact(deepcopy(args)) if args else {}
    if request.content_type == "application/octet-stream":
        redacted_data = ""
    elif request.is_json:
        try:
            json_data = request.get_json(force=True, silent=True) or {}
            redacted_data = json.dumps(redact(deepcopy(json_data)))
        except Exception:  # pylint: disable=broad-except
            redacted_data = ""
    else:
        redacted_data = request.data.decode(errors="replace")

    return redacted_args, redacted_data


def _create_audit(
    username: str = None, request: dict = None, user_name: str = None, user_email: str = None, vco: str = None
) -> AuditLog:
    """Create audit before request

    Args:
        username (str, optional): Username. Defaults to None.
        request (dict, optional): Request data. Defaults to None.
        user_name (str, optional): User full name. Defaults to None.
        user_email (str, optional): User email. Defaults to None.
        vco (str, optional): VCO ID. Defaults to None.

    Returns:
        AuditLog: AuditLog
    """
    args, data = _protect_request_date(request)
    rule = request.url_rule.rule if request.url_rule else ""
    request_obj = Request(
        method=request.method,
        path=vco + request.path if vco else request.path,
        data=data,
        args=args,
        rule=rule,
    )
    audit = AuditLog(
        username=username,
        request=request_obj,
        user_name=user_name,
        user_email=user_email,
        vco=vco,
        rule=rule,
    )
    audit.save()
    flask_g.audit = audit


def _update_audit_with_response_and_extra_metadata(
    request: Request = None, response: Response = None, vco_id: str = None
) -> AuditLog:
    """Update audit after response

    Args:
        request (Request, optional): Request data. Defaults to None.
        response (Response, optional): Response data. Defaults to None.
        extra_metadata (Dict, optional): Extra metadata for VCO audits. Defaults to None.
        vco_id (str, optional): Virtual Cloud Operator Id

    Returns:
        AuditLog: AuditLog
    """
    audit = flask_g.audit
    path_args = getattr(flask_g, "path_args", {})
    user_agent = request.user_agent.string
    origin = request.headers.environ.get("SERVER_PROTOCOL")
    x_forward_for = request.access_route[0]
    fallback = request.remote_addr
    response_time = time.time() - audit.timestamp
    if response.content_type in ["application/json", "text/plain"]:
        data = response.get_data().decode()
    else:
        data = response.content_type
    audit.response = Response(
        status_code=response.status_code,
        status_text=response.status,
        content_type=response.content_type,
        data=data,
        response_time=response_time,
        headers=response.headers,
        user_agent=user_agent,
        origin=origin,
        x_forward_for=x_forward_for,
        fallback=fallback,
    )
    try:  # Save audit even if uri params are invalid
        if audit.vco and path_args:
            if "disk_id" in path_args:
                audit.resource_type = CloudResourceType.DISK.value
                audit.resource_id = str(path_args["disk_id"])
                audit.location = (
                    path_args.get("location") or decode_validate_cloudspace_id(path_args["cloudspace_id"])[0]
                )
            elif "vm_id" in path_args:
                audit.resource_type = CloudResourceType.VIRTUAL_MACHINE.value
                audit.resource_id = str(path_args["vm_id"])
                if path_args.get("cloudspace_id"):
                    audit.location = decode_validate_cloudspace_id(path_args["cloudspace_id"])[0]
                else:
                    audit.location = ""
            elif "cloudspace_id" in path_args:
                audit.resource_type = CloudResourceType.CLOUDSPACE.value
                audit.resource_id = path_args["cloudspace_id"]
                audit.location = decode_validate_cloudspace_id(audit.resource_id)[0]
            elif "objectspace_id" in path_args:
                audit.resource_type = CloudResourceType.OBJECTSPACE.value
                audit.resource_id = path_args["objectspace_id"]
                audit.location = decode_validate_base64_id(audit.resource_id)[0]
            else:
                audit.resource_type = CloudResourceType.CUSTOMER.value
    finally:
        if "customer_id" in path_args:
            audit.customer_id = path_args["customer_id"]
        audit.save()

        if vco_id is not None and audit.method != "GET":
            audit_log_forwarder = AuditLogHandler.get_instance()
            audit_log_forwarder.forward(audit)


def purge_audits(
    start_time: float = None, end_time: float = None, get_requests: bool = False, path: str = None
) -> None:
    """Purge audits

    Keyword Arguments:
        start_time {float} -- Date to purge audits created after it
        end_time {float} -- Date to purge audits created before it
        get_requests {bool} -- Only get requests
        path {str} -- Path regex
    """
    AuditLog.purge(start_time, end_time, get_requests, path)


def get_workflow_status(status: TaskStatus) -> Tuple[Union[str, None], Union[float, None]]:
    """Calculates the final status of the workflow

    Keyword Arguments:
        status {TaskStatus} -- Workflow object.

    Returns:
        str, float -- Tuple of status string and executed_on epochtimestamp
    """
    if status is None:
        return None, None

    if status.status in (STATUS_QUEUED, STATUS_RUNNING):
        return status.status, None
    if status.status == STATUS_SUCCEEDED:
        if status.on_success:
            return get_workflow_status(status.on_success)
        return STATUS_SUCCEEDED, status.executed_on

    if status.status == STATUS_FAILED:
        if status.on_failure:
            return get_workflow_status(status.on_failure)
        return STATUS_FAILED, status.executed_on

    raise ValueError("Do not know how to proceed!")


def list_workflows(
    limit: int,
    start_after: str = None,
    reverse: bool = True,
    start_time: str = None,
    end_time: str = None,
    search: str = None,
    status: str = None,
    completed: bool = False,
) -> Tuple[list, int]:
    """List workflows from dynaqueue

    Keyword Arguments:
        limit {str} -- limit of the returned workflows
        start_after {str} -- Start returning records after index.
        reverse {bool} -- Whether to return in reverse order (newest first)
        start_time {float} -- Date to return workflows created after it
        end_time {float} -- Date to return workflows created before it
        search {string} -- Wild card to search by title
        status {string} -- Filter by status
        completed {bool} -- Return only completed tasks
    Returns:
        (list, int) -- tuple contains the requested results and the total number of results
    """
    client: Client = DynaqueueConnection.get_client()
    workflows = client.list_tasks(reverse=reverse, include_running=not completed, include_queued=not completed)
    for workflow in workflows:
        workflow.workflow_status, workflow.executed_on = get_workflow_status(workflow)
    conditions = []
    if start_time:
        conditions.append(lambda workflow: workflow.created_on >= start_time)
    if end_time:
        conditions.append(lambda workflow: workflow.created_on <= end_time)
    if search:
        conditions.append(lambda workflow: re.match(re.compile(search, flags=re.IGNORECASE), workflow.title))
    if status:
        conditions.append(lambda workflow: workflow.workflow_status == status.upper())

    workflows = list(filter(lambda workflow: all(condition(workflow) for condition in conditions), workflows))
    workflows = sorted(workflows, key=lambda workflow: workflow.status == "RUNNING", reverse=True)
    count = len(workflows)
    if start_after:
        start_after_index = 0
        for workflow in workflows:
            if workflow.id == start_after:
                break
            start_after_index += 1
        workflows = workflows[start_after_index + 1 : start_after_index + limit + 1]
    else:
        workflows = workflows[:limit] if limit != 0 else workflows

    return workflows, count


def list_all_workflows(
    limit: int,
    start_after: str = None,
    include_archived: bool = True,
    start_time: str = None,
    end_time: str = None,
    search: str = None,
    status: str = None,
) -> Tuple[List, int]:
    """List all workflows

    Keyword Arguments:
        limit {int} -- Workflows returned limit.
        start_after {str} -- Start returning records after index.
        include_archived {bool} -- Whether to include logs archived in MongoDB.

    Returns:
        (list, int) -- tuple contains the requested results and the total number of results
    """

    # Get redis workflows
    if limit > 100 or limit <= 0:
        raise BadRequest("Limit should be less than or equal 100 and greater than 0")
    workflows, count = list_workflows(
        limit, start_after=start_after, start_time=start_time, end_time=end_time, search=search, status=status
    )
    # Get mongo archived workflows, if limit not exhausted and include_archived
    if include_archived:
        count += WorkflowInfo.objects.count()  # pylint: disable=no-member
        if limit == 0 or limit - len(workflows) > 0:
            new_limit = 0 if limit == 0 else limit - len(workflows)
            if start_after and len(workflows):
                # If start_after is not none, and workflows is not empty
                # then start_after was already in redis
                start_after = None
            archived_workflows = WorkflowInfo.list(
                limit=new_limit,
                start_after=start_after,
                start_time=start_time,
                end_time=end_time,
                search=search,
                status=status,
            )
            # Append workflows
            workflows += archived_workflows
    return workflows, count


def get_workflow_info(workflow_id: str, include_logs: bool = False, include_debug_logs: bool = False) -> dict:
    """Gets workflow info

    Keyword Arguments:
        workflow_id {int} -- Workflow object.
        include_logs {bool} -- Flag to whether to include logs in the result set.
        include_debug_logs {bool} -- Flag to whether to include debug log records

    Returns:
        dict -- workflow info
    """
    client: Client = DynaqueueConnection.get_client()
    registry = dict()

    def _to_dict(task_status: TaskStatus, workflow_status: str = None) -> dict:
        result = dict(
            id=task_status.id,
            title=task_status.title,
            status=task_status.status,
            created_on=task_status.created_on,
            queued_on=task_status.queued_on,
            picked_up_on=task_status.picked_up_on,
            executed_on=task_status.executed_on,
            worker_name=task_status.worker_name,
            workflow_status=workflow_status,
            attempts=task_status.attempts,
        )
        registry[task_status.id] = result
        result["on_success"] = _to_dict(task_status.on_success) if task_status.on_success is not None else None
        result["on_failure"] = _to_dict(task_status.on_failure) if task_status.on_failure is not None else None
        return result

    try:
        workflow = client.get_task_status(workflow_id)
        workflow_status, workflow.executed_on = get_workflow_status(workflow)
        in_redis = True
    except KeyError:
        workflow = WorkflowInfo.get_by_id(workflow_id)
        workflow_status, _ = get_workflow_status(workflow)
        in_redis = False
    result = _to_dict(workflow, workflow_status)
    formatter = logging.Formatter()
    if include_logs:
        logs = defaultdict(list)
        log_records = client.list_task_logs(workflow_id) if in_redis else TaskLogRecord.get_records_by_id(workflow_id)
        for log_record in log_records:
            record = log_record.record
            if not include_debug_logs and record.levelno == logging.DEBUG:
                continue
            logs[log_record.task_id].append(
                dict(level=record.levelname, timestamp=record.created, message=formatter.format(log_record.record))
            )
        for task_id, log_records in logs.items():
            registry[task_id]["logs"] = log_records
    return result


def get_task_logs(workflow_id: str, task_id: str, include_debug_logs: bool, raw_logs: bool = False):
    """Gets workflow task logs

    Keyword Arguments:
        workflow_id {int} -- Workflow id.
        task_id {str} -- Task id
        include_debug_logs {bool} -- Flag to whether to include debug log records
        raw_logs {bool} -- Flag to whether to return logs in their raw form

    Returns:
        list -- task logs
    """
    result = list()
    client: Client = DynaqueueConnection.get_client()

    try:
        logs = TaskLogRecord.get_records_by_id(workflow_id)
    except DoesNotExist:
        logs = client.list_task_logs(workflow_id)

    for log_record in logs:
        if log_record.task_id != task_id:
            continue
        record = log_record.record
        if not include_debug_logs and record.levelno == logging.DEBUG:
            continue
        if raw_logs:
            result.append(record)
        else:
            result.append(
                dict(level=record.levelname, timestamp=record.created, message=log_record.record.getMessage())
            )
    return result


def _monitor_operator(in_cluster=True):
    def _repeat_n(n, predicate):
        for _ in range(n):
            if predicate():
                return True
            sleep(1)
        return False

    k8.update_config_map(
        "vco-op-healthcheck", create=True, data={"ack": "0", "check-timestamp": f"{time.time()}"}, in_cluster=in_cluster
    )

    if _repeat_n(5, lambda: k8.get_config_map("vco-op-healthcheck", in_cluster=in_cluster).data["ack"] == "1"):
        logger.info("Operator responded, exiting")
        return ComponentStatus.OK

    logger.warning("Operator not responsing, restarting")
    k8.restart_deployment("iam-gig-operator", "iam", in_cluster=in_cluster)

    if _repeat_n(
        15, lambda: k8.get_deployment("iam-gig-operator", "iam", in_cluster=in_cluster).status.available_replicas
    ):
        logger.info("Deployment restarted")
        if _repeat_n(5, lambda: k8.get_config_map("vco-op-healthcheck", in_cluster=in_cluster).data["ack"] == "1"):
            logger.info("Operator responded after restart, exiting")
            return ComponentStatus.OK
        else:
            logger.error("Operator failed to respond even after restart")
            _store_error_log(exception="Operator failed to respond even after restart", component="Vco operator")
    else:
        logger.error("Failed to restart iam-gig-operator deployment")
        _store_error_log(exception="Failed to restart iam-gig-operator deployment", component="Vco operator")
    return ComponentStatus.ERROR


def kill_task(task_id: str):
    """kill task

    Args:
        task_id (str): task id
    """
    client: Client = DynaqueueConnection.get_client()
    client.kill_task(task_id)


@schedule(cron="33 0 * * *", description="Clean up obsolete dns records")
def cleanup_dns_records():
    """Cleans up obsolete dns records"""
    DnsRecord.list(obsolete=True).delete()


def set_alarm_room_credentials(user_name: str, password: str):
    """set credentials for alarm room

    Args:
        user_name (str): user name
        password (str): user password
    """
    if not user_name:
        raise BadRequest("User name is required")
    if not password:
        raise BadRequest("Password is required")

    user_name = user_name.strip()
    if len(user_name) < 3:
        raise BadRequest("User name must be 3 or more characters")
    validate_password(password)

    user = AlarmRoomUsers.get_first_user()
    if user is None:
        user = AlarmRoomUsers()

    password_hash = password.encode()
    m = hashlib.md5()
    m.update(password_hash)

    user.user_name = user_name
    user.password = m.hexdigest()
    user.save()


def login_to_alarm_room(user_name: str, password: str):
    """login to alarm room

    Args:
        user_name (str): user name
        password (str): user password
    """
    if not user_name:
        raise BadRequest("User name is required")
    if not password:
        raise BadRequest("Password is required")

    user = AlarmRoomUsers.get_first_user()
    if user is None:
        raise DoesNotExist(f"No account with user name {user_name}")

    password_hash_str = password.encode()
    password_hash = hashlib.md5()
    password_hash.update(password_hash_str)

    if user.password != password_hash.hexdigest():
        raise BadRequest("Wrong password")

    password_hash_string = f"{user_name}{user.password}".encode()
    m = hashlib.md5()
    m.update(password_hash_string)
    return m.hexdigest()


def get_alarm_room_user_name():
    """get current user name"""
    user = AlarmRoomUsers.get_first_user()
    if user is not None:
        return user.user_name


def _get_update_certificate_status() -> ComponentStatus:
    try:
        logger.info("Checking update certificate collection health")
        collection = UpdateCertificate.get_last()
        failed_job: FailedUpdateCertificate
        if collection and collection.failed_jobs:
            for failed_job in collection.failed_jobs:
                logger.info("Collection failure: https://meneja.gig.tech/workflows/%s", failed_job.workflow_id)
                _store_error_log(
                    exception=f"Collection failure: https://meneja.gig.tech/workflows/{failed_job.workflow_id}",
                    component="Update certificate collection",
                )
            return ComponentStatus.WARNING
        logger.info("Update certificate collection is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        logger.exception("Could not connect to Mongodb")
        _store_error_log(exception=e, component="Update certificate collection collection")
        return ComponentStatus.ERROR


def _get_update_cluster_certificate_status() -> ComponentStatus:
    try:
        logger.info("Checking update cluster certificate collection health")
        collection = UpdateClusterCertificate.get_last()
        failed_job: FailedUpdateClusterCertificate
        if collection and collection.failed_jobs:
            for failed_job in collection.failed_jobs:
                logger.info("Collection failure: https://meneja.gig.tech/workflows/%s", failed_job.workflow_id)
                _store_error_log(
                    exception=f"Collection failure: https://meneja.gig.tech/workflows/{failed_job.workflow_id}",
                    component="Update cluster certificate collection",
                )
            return ComponentStatus.WARNING
        logger.info("Update certificate collection is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=W0718
        logger.exception("Could not connect to Mongodb")
        _store_error_log(exception=e, component="Update certificate collection collection")
        return ComponentStatus.ERROR


@_heath_check
def _get_node_status() -> ComponentStatus:
    try:
        logger.info("Checking Meneja nodes health...")
        nodes = k8.list_nodes(True)
        failed = False
        for node in nodes:
            node_name = node.metadata.name
            ready_condition = next(
                (condition for condition in node.status.conditions if condition.type == "Ready"), None
            )
            if ready_condition and ready_condition.status == "True":
                logger.info("Node %s is Ready", node_name)
            else:
                logger.info("Node %s is Not Ready", node_name)
                failed = True
        if failed:
            return ComponentStatus.ERROR
        return ComponentStatus.OK
    except Exception:  # pylint: disable=W0718
        logger.exception("Failed to get node statuses")
        return ComponentStatus.ERROR


@redis_cache(3600)
def _get_backup_s3_status() -> ComponentStatus:
    try:
        logger.info("Checking Backup S3 health...")
        MongoConnection.get_client()
        collection = BackupS3.get_last()
        if collection.failed_jobs:
            for failed_job in collection.failed_jobs:
                logger.info("Backup S3 failure: https://meneja.gig.tech/workflows/%s", failed_job)
                _store_error_log(
                    exception=f"Backup S3 failure: https://meneja.gig.tech/workflows/{failed_job}",
                    component="Backup S3",
                )
            return ComponentStatus.ERROR
        logger.info("Backup S3 is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        logger.exception("Could not connect to Mongodb")
        _store_error_log(exception=e, component="Backup S3")
        return ComponentStatus.ERROR


@redis_cache(3600)
def _get_emails_export() -> ComponentStatus:
    """Get emails export job status"""
    try:
        logger.info("Checking emails export health...")
        MongoConnection.get_client()
        collection = EmailsExport.get_last()
        if collection.failed_jobs:
            for failed_job in collection.failed_jobs:
                logger.info("Emails export failure: https://meneja.gig.tech/workflows/%s", failed_job)
                _store_error_log(
                    exception=f"Emails export failure: https://meneja.gig.tech/workflows/{failed_job}",
                    component="Emails export",
                )
            return ComponentStatus.ERROR
        logger.info("Emails export is healthy")
        return ComponentStatus.OK
    except Exception as e:  # pylint: disable=broad-except
        logger.exception("Could not connect to Mongodb")
        _store_error_log(exception=e, component="Emails export")
        return ComponentStatus.ERROR
