# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
import time
from typing import List

from bson.objectid import ObjectId as BObjectId
from mongoengine import DoesNotExist, Embedded<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EmbeddedDocume<PERSON><PERSON><PERSON><PERSON><PERSON>
from mongoengine.document import Document, EmbeddedDocument
from mongoengine.fields import (
    <PERSON><PERSON><PERSON><PERSON>ield,
    Dynamic<PERSON>ield,
    FloatField,
    IntField,
    ListField,
    MapField,
    ObjectId,
    ObjectIdField,
    StringField,
)

from meneja.lib.enumeration import (
    CheckMessageLevel,
    ClusterCertificateStatus,
    ClusterStatus,
    ManagementClusterType,
    NodeProvisioningStatus,
    SSLCertificateSource,
)
from meneja.model import EnumStringField

# pylint: disable=no-member


class CheckMessage(EmbeddedDocument):
    """Represents a single check message.

    Attributes:
        level (CheckMessageLevel): The severity level of the message.
        message (str): The message content.
        timestamp (int): The timestamp of when the message was created, in seconds since the Unix epoch.
    """

    level = EnumStringField(help_text="Message severity", enum_type=CheckMessageLevel)
    message = StringField(help_text="Check message")
    timestamp = IntField(help_text="Epoch timestamp when the message was logged")


class CheckResult(EmbeddedDocument):
    """Represents the result of a health check.

    Attributes:
        success (bool): Whether the check succeeded or not.
        messages (List[CheckMessage]): A list of messages produced by the check.
    """

    success = BooleanField(help_text="Last run status")
    messages = EmbeddedDocumentListField(CheckMessage, help_text="Log messages produced by the health check")


class HealthCheck(EmbeddedDocument):
    """Represents a health check configuration.

    Attributes:
        name (str): The name of the check.
        schedule (str): The cron schedule for running the check.
        status (CheckResult): The result of the last run of the check.
        next_run (int): The timestamp of when the next run of the check is scheduled, in seconds since the Unix epoch.
    """

    name = StringField(help_text="Check name")
    schedule = StringField(help_text="Cron formatted schedule")
    status = EmbeddedDocumentField(CheckResult, help_text="Last run status")
    next_run = IntField(help_text="Next run epoch timestamp")


class NodeStatus(EmbeddedDocument):
    """Represents the status of a node.

    Attributes:
        timestamp (int): The timestamp of when the status was last updated, in seconds since the Unix epoch.
        health_checks (List[HealthCheck]): A list of health checks that were run on the node.
    """

    timestamp = IntField(help_text="Epoch timestamp when the node status was last updated")
    health_checks = EmbeddedDocumentListField(HealthCheck, help_text="Health checks")


class VirtualMachine(EmbeddedDocument):
    """Represents a virtual machine.

    Attributes:
        vm_id (int): The ID of the virtual machine.
        vm_ip (str): The IP address of the virtual machine.
        status (NodeStatus): The status of the virtual machine.
    """

    vm_id = IntField(help_text="Virtual Machine ID")
    vm_ip = StringField(help_text="Internal IP Address of the Virtual Machine")
    status = EmbeddedDocumentField(NodeStatus, help_text="Last logged node status")


class LoadBalancer(EmbeddedDocument):
    """Represents a load balancer.

    Attributes:
        loadbalancer_id (str): The ID of the load balancer.
    """

    loadbalancer_id = StringField(help_text="id of loadbalancer")


class TaskDetails(EmbeddedDocument):
    """Represents a task transition.

    Attributes:
        transition_id (str): The ID of the transition.
        from_state (str): The starting state of the transition.
        current_state (str): The current state of the transition.
        final_state (str): The final state of the transition.
        start_timestamp (float): The timestamp of when the transition started, in seconds since the Unix epoch.
        end_timestamp (float): The timestamp of when the transition ended, in seconds since the Unix epoch.
    """

    transition_id = StringField(help_text="Task id")
    from_state = StringField(help_text="Starting state of the job")
    current_state = StringField(help_text="Current state of the job")
    final_state = StringField(help_text="Final state of the job")
    start_timestamp = FloatField(help_text="Starting timestamp of transition")
    end_timestamp = FloatField(help_text="Ending timestamp of transition")


class NodeProvisionerStatus(EmbeddedDocument):
    """Represents the status of a node provisioning operation.

    Attributes:
        start (int): The timestamp of when the provisioning operation started, in seconds since the Unix epoch.
        end (int): The timestamp of when the provisioning operation ended, in seconds since the Unix epoch.
        status (NodeProvisioningStatus): The status of the provisioning operation.
    """

    start = IntField(help_text="Start provisioning timestamp")
    end = IntField(default=0, help_text="End provisioning timestamp")
    status = StringField(choices=NodeProvisioningStatus.values())


class ClusterNode(EmbeddedDocument):
    """Represents a node in a Rancher management cluster.

    Attributes:
        cloudspace_id (str): The ID of the cloudspace that contains the node.
        vm_id (int): The ID of the virtual machine that represents the node.
        name (str): The name of the node.
        status (NodeStatus): The status of the node.
        provisioner (str): The ID of the provisioning workflow that created the node, if any.
        provisioner_status (NodeProvisionerStatus): The status of the provisioning operation for the node, if any.
    """

    cloudspace_id = StringField(help_text="Cloudspace id containing this node")
    vm_id = IntField(help_text="Virtual Machine Id")
    name = StringField(help_text="Node/VM name")
    status = EmbeddedDocumentField(NodeStatus, help_text="Last logged node status")
    provisioner = StringField(default=None, help_text="Provisioning workflow id")
    provisioner_status = EmbeddedDocumentField(NodeProvisionerStatus, help_text="Node provision status")


class NodePool(EmbeddedDocument):
    """Represents a node pool in a Rancher management cluster.

    Attributes:
        node_pool_id (str): The ID of the node pool.
        cloudspace_id (str): The ID of the cloudspace that contains the node pool.
        ingress_controller_installed (bool): Whether the ingress controller has been installed on the nodes in the pool.
        name (str): The name of the node pool.
    """

    node_pool_id = StringField(help_text="Node pool id")
    cloudspace_id = StringField(help_text="Cloudspace id where the node pool is located")
    ingress_controller_installed = BooleanField(
        default=False, help_text="Indicates wether the ingress controller was installed already."
    )
    name = StringField(help_text="Node pool name")


class LoadBalancerService(EmbeddedDocument):
    """Represents a Load Balancer Service

    Attributes:
        service_uid (str): The unique ID of the service.
        node_pool_ids (List[str]): A list of node pool IDs associated with the service.
        cloudspace_id (str): The ID of the cloudspace where the ingress resources are located.
        external_network_id (int): The ID of the external network associated with the service.
        external_network_ip (str): The IP address of the external network associated with the service.
        serverpool_id (str): The ID of the server pool associated with the service.
        loadbalancer_ids (List[str]): A list of load balancer IDs associated with the service.
    """

    service_uid = StringField(help_text="The unique ID of the service", unique=True)
    node_pool_ids = ListField(StringField(), help_text="A list of node pool IDs associated with the service")
    cloudspace_id = StringField(help_text="The ID of the cloudspace where the ingress resources are located")
    external_network_id = DynamicField(help_text="The ID of the external network associated with the service")
    external_network_ip = StringField(help_text="The IP address of the external network associated with the service")
    serverpool_id = StringField(help_text="The ID of the server pool associated with the service")
    loadbalancer_ids = ListField(StringField(), help_text="A list of load balancer IDs associated with the service")


class KubernetesCluster(EmbeddedDocument):
    """Represents a Kubernetes Cluster

    Attributes:
        kubernetes_cluster_id (str): The unique ID of the Kubernetes cluster.
        node_pools (List[NodePool]): A list of node pools associated with the Kubernetes cluster.
        load_balancer_services (List[LoadBalancerService]): A list of load balancer services
          associated with the Kubernetes cluster.
    """

    kubernetes_cluster_id = StringField(help_text="The unique ID of the Kubernetes cluster")
    node_pools = EmbeddedDocumentListField(
        NodePool, default=list, help_text="A list of node pools associated with the Kubernetes cluster"
    )
    load_balancer_services = EmbeddedDocumentListField(
        LoadBalancerService, default=list, help_text="List of LoadBalancer Services"
    )


class ClusterHealthCheck(EmbeddedDocument):
    status = StringField(help_text="Last run status")
    last_run = IntField(help_text="Last run time")


class ClusterHealthChecks(EmbeddedDocument):
    certificate_health_check = EmbeddedDocumentField(
        ClusterHealthCheck, help_text="Certificate update health check details"
    )


class RancherManagementCluster(Document):
    """Represents a Rancher management cluster"""

    meta = {"collection": "management_cluster"}

    management_cluster_id = ObjectIdField(primary_key=True, required=True, default=ObjectId, db_field="_id")
    name = StringField(help_text="Management cluster name")
    domain_name = StringField(help_text="Management cluster domain name")
    cloudspace_id = StringField(help_text="Cloudspace ID containing this cluster")
    management_cluster_nodes = EmbeddedDocumentListField(
        VirtualMachine, db_field="vm_ids", help_text="List of management cluster vms"
    )
    loadbalancers = EmbeddedDocumentListField(LoadBalancer, help_text="List of loadbalancer ids")
    serverpool_id = StringField(help_text="server pool id hosting vms")
    customer_id = StringField(help_text="Id of the customer owning this cluster")
    cluster_type = StringField(help_text="Type of the management cluster", choices=ManagementClusterType.values())
    ssl_certificate_source = StringField(help_text="Source of SSL certificates", choices=SSLCertificateSource.values())
    status = StringField(help_text="Current status of the management cluster", choices=ClusterStatus.values())
    transition_tasks_details = EmbeddedDocumentListField(
        TaskDetails, help_text="Transition workflows", hidden=True, default=list  # pylint: disable=E0601
    )
    deleted = BooleanField(help_text="Management Deleted", default=False, readOnly=False, hidden=True)
    cluster_nodes = EmbeddedDocumentListField(
        ClusterNode, help_text="List of nodes created by the management cluster for deploying RKE clusters"
    )
    letsencrypt_emailaddress = StringField(help_text="Email address for letsencrypt")
    api_token_id = StringField(help_text="Api token id")
    api_token = StringField(help_text="Api token")
    technical_authorization_organization = StringField(help_text="Technical authorization organization in iam")
    vco_id = StringField(help_text="Id of VCO where the management cluster exists", hidden=True)
    external_network_ip = StringField(help_text="External network IP to be used by the cluster")
    kubernetes_clusters = MapField(
        EmbeddedDocumentField(KubernetesCluster), default=dict, help_text="Kubernetes clusters"
    )
    failed_connections = ListField(FloatField(), hidden=True, default=list)
    cluster_health_checks = EmbeddedDocumentField(ClusterHealthChecks, help_text="Cluster health checks")
    physical_storage = BooleanField(help_text="Cluster using physical storage", default=False)

    @classmethod
    def list(
        cls,
        customer_id: str = None,
        deleted: bool = False,
        statuses: List[ClusterStatus] = None,
        only: List[str] = None,
        failed_certificate_update: bool = None,
    ) -> List["RancherManagementCluster"]:
        """Returns list of management clusters

        Args:
            customer_id (str): Customer Id
            deleted (bool): Include deleted cluster. Defaults False
            statuses (List[Cluster statuses]): Filter by statuses
            only (List(str)): Return only certain fields
            failed_certificate_update (bool): Return cluster who has issue with certificate update. Defaults None
        """
        q = cls.objects
        if customer_id is not None:
            q = q(customer_id=customer_id)
        if not deleted:
            q = q(deleted=False)
        if statuses is not None:
            q = q(status__in=[x.value for x in statuses])
        if only is not None:
            q = q.only(*only)
        if failed_certificate_update:
            q = q(cluster_health_checks__certificate_health_check__status=ClusterCertificateStatus.EXPIRED.value)
        return list(q)

    @classmethod
    def get_by_id(
        cls, management_cluster_id: str, include_deleted=False, only: List[str] = None
    ) -> "RancherManagementCluster":
        """Gets management cluster by id

        Args:
            management_cluster_id (str): Id of the required management cluster
            include_deleted (bool): if true will return deleted cluster
            only (List[str]): Only return specified fields
        """
        q = cls.objects(management_cluster_id=management_cluster_id, deleted=include_deleted)
        if only:
            q.only(*only)
        return q.get()

    @classmethod
    def delete_by_id(cls, management_cluster_id: str):
        """Delete ManagementCluster by id

        Arguments:
            management_cluster_id (str): Id of the required management cluster
        """
        cls.objects.get(management_cluster_id=management_cluster_id).update(set__deleted=True)

    @classmethod
    def get_transition_by_id(cls, customer_id: str, management_cluster_id: str, transition_id: str) -> TaskDetails:
        """Gets a transition by id

        Args:
            customer_id (str): Customer ID
            management_cluster_id (str): Management Cluster Id
            transition_id (str): Transition Id
        """
        transitions = (
            cls.objects(
                management_cluster_id=management_cluster_id,
                transition_tasks_details__transition_id=transition_id,
                customer_id=customer_id,
                deleted=False,
            )
            .only("transition_tasks_details")
            .get()
            .transition_tasks_details
        )
        for transition in transitions:
            if transition.transition_id == transition_id:
                return transition
        raise DoesNotExist("Transition does not exist")

    @classmethod
    def delete_node(cls, management_cluster_id: str, vm_id: int):
        """Deletes a node by vm_id

        Args:
            management_cluster_id (str): Management Cluster Id
            vm_id (int): VM ID
        """
        document_count = cls.objects(management_cluster_id=management_cluster_id, deleted=False).update(
            pull__cluster_nodes__vm_id=vm_id
        )
        if document_count != 1:
            raise RuntimeError(
                f"Expected a single entry to be deleted. Instead {document_count} documents were deleted"
            )

    @classmethod
    def delete_node_by_name(cls, management_cluster_id: str, node_name: int, cloudspace_id: str):
        """Deletes a node by vm_id

        Args:
            management_cluster_id (str): Management Cluster Id
            vm_id (int): VM ID
            cloudspace_id(str): CS ID
        """
        document_count = cls.objects(management_cluster_id=management_cluster_id, deleted=False).update(
            __raw__={"$pull": {"cluster_nodes": {"node_name": node_name, "cloudspace_id": cloudspace_id}}}
        )
        if document_count != 1:
            raise RuntimeError(
                f"Expected a single entry to be deleted. Instead {document_count} documents were deleted"
            )

    @classmethod
    def list_nodes(cls, management_cluster_id: str, customer_id: str) -> List[ClusterNode]:
        """Lists the nodes in a management cluster

        Args:
            management_cluster_id (str): Management Cluster Id
        """
        return list(
            cls.objects(management_cluster_id=management_cluster_id, customer_id=customer_id, deleted=False)
            .only("cluster_nodes")
            .get()
            .cluster_nodes
        )

    @classmethod
    def check_cluster_hostname_exists(cls, domain: str) -> bool:
        """Check hostname existence

        Args:
            domain (str): Predicated domain

        Returns:
            bool: result
        """
        return bool(cls.objects(domain_name=domain, deleted=False).count())

    @classmethod
    def check_cluster_ip_exists(cls, external_network_ip: str) -> bool:
        """Check External Network IP existence

        Args:
            external_network_ip (str): Predicated external_network_ip

        Returns:
            bool: result
        """
        return bool(cls.objects(external_network_ip=external_network_ip, deleted=False).count())

    @classmethod
    def get_by_domain_name(cls, domain: str) -> "ClusterNode":
        """Fetch cluster by domain name

        Args:
            domain (str): Predicated domain

        Returns:
            "ClusterNode": result
        """
        return cls.objects(domain_name=domain, deleted=False).get()

    @classmethod
    def check_cluster_name_exists(cls, name: str, customer_id: str) -> bool:
        """Check if a cluster with name exists for customer

        Args:
            name (str): Predicated name
            customer_id (str): Customer ID

        Returns:
            bool: result
        """
        return bool(cls.objects(name=name, customer_id=customer_id, deleted=False).count())

    @classmethod
    def set_cluster_node_vm_id(cls, management_cluster_id: str, cloudspace_id: str, node_name: str, vm_id: str):
        """Set vm ID for a cluster node

        Args:
            management_cluster_id (str): Rancher Management Cluster ID
            cloudspace_id (str): CS ID
            NodeProvisioningStatus (str): node name
            vm_id (str): vm ID to be set
        """
        # pylint: disable=no-member
        updated_documents = cls.objects(
            __raw__={
                "_id": BObjectId(management_cluster_id),
                "cluster_nodes.cloudspace_id": cloudspace_id,
                "cluster_nodes.name": node_name,
            }
        ).update(__raw__={"$set": {"cluster_nodes.$.vm_id": vm_id}})
        if updated_documents != 1:
            raise KeyError(f"Cannot find node {node_name} in rke cluster {management_cluster_id}")

    @classmethod
    def set_cluster_node_provisioner_status(
        cls, management_cluster_id: str, node_name: str, cloudspace_id: str, status: NodeProvisioningStatus
    ):
        """Set node provisioning status

        Args:
            management_cluster_id (str): Rancher Management Cluster ID
            node_name (str): node name
            cloudspace_id (str): Cloudspace ID
            status (NodeProvisioningStatus): new node status to be set
        """
        updated_documents = cls.objects(
            __raw__={
                "_id": BObjectId(management_cluster_id),
                "cluster_nodes.cloudspace_id": cloudspace_id,
                "cluster_nodes.name": node_name,
            }
        ).update(__raw__={"$set": {"cluster_nodes.$.provisioner_status.status": status.value}})
        if updated_documents != 1:
            raise KeyError(f"Cannot find node {node_name} in rke cluster {management_cluster_id}")

    @classmethod
    def update_management_cluster_node_health_status(
        cls, management_cluster_id: str, cloudspace_id: str, vm_id: str, status: NodeStatus
    ) -> None:
        """Update management cluster node status

        Args:
            management_cluster_id (str): Rancher Management Cluster ID
            cloudspace_id (str): Cloudspace ID
            vm_id (str): Virtual Machine ID
            status (NodeStatus): Node Status
        """
        updated_documents = cls.objects(
            __raw__={
                "_id": BObjectId(management_cluster_id),
                "management_cluster_nodes.cloudspace_id": cloudspace_id,
                "management_cluster_nodes.vm_id": vm_id,
            }
        ).update(__raw__={"$set": {"management_cluster_nodes.$.status": status.to_mongo().to_dict()}})
        if updated_documents != 1:
            raise KeyError(f"Cannot find node {vm_id} in management cluster {management_cluster_id}")

    @classmethod
    def update_cluster_node_health_status(
        cls, management_cluster_id: str, cloudspace_id: str, vm_id: str, status: NodeStatus
    ) -> None:
        """Update cluster node status

        Args:
            management_cluster_id (str): Rancher Management Cluster ID
            cloudspace_id (str): Cloudspace ID
            vm_id (str): Virtual Machine ID
            status (NodeStatus): Node Status
        """
        updated_documents = cls.objects(
            __raw__={
                "_id": BObjectId(management_cluster_id),
                "cluster_nodes.cloudspace_id": cloudspace_id,
                "cluster_nodes.vm_id": vm_id,
            }
        ).update(__raw__={"$set": {"cluster_nodes.$.status": status.to_mongo().to_dict()}})
        if updated_documents != 1:
            raise KeyError(f"Cannot find node {vm_id} in management cluster {management_cluster_id}")

    def get_cluster_node(self, node_name: str, cloudspace_id: str):
        """Get cluster_node

        Args:
            node_name (str): Node name
            cloudspace_id (str): CS ID

        Raises:
            KeyError: No such node
        """
        for cluster_node in self.cluster_nodes:
            if cluster_node.cloudspace_id == cloudspace_id and cluster_node.name == node_name:
                return cluster_node
        raise KeyError("Node not found")


class RancherAsAServiceSettings(Document):
    """Represents the Rancher as a Service settings.

    Attributes:
        id (str): The unique ID of the settings document.
        default_version_tag (str): The default rancher version tag of rancher.
        mgmt_cluster_vcpus (int): The default number of vCPUs for the management cluster.
        mgmt_cluster_memory (int): The default memory size (in MB) for the management cluster.
        mgmt_cluster_boot_disk_size (int): The default size (in GB) of the boot disk for the management cluster.
        mgmt_cluster_data_disk_size (int): The default size (in GB) of the data disk for the management cluster.
    """

    id = StringField(primary_key=True, required=True, default="the-one", db_field="_id")
    default_version_tag = StringField(help_text="Default rancher version tag", required=True)
    mgmt_cluster_vcpus = IntField(help_text="Management cluster default VM VCPUs", default=6)
    mgmt_cluster_memory = IntField(help_text="Management cluster default VM Memory", default=12288)
    mgmt_cluster_boot_disk_size = IntField(help_text="Management cluster default VM boot disk size", default=25)
    mgmt_cluster_data_disk_size = IntField(help_text="Management cluster default VM data disk size", default=30)
    minimum_kubernetes_version = StringField(help_text="Minimum supported kubernetes version", default="1.22")

    @classmethod
    def get_settings(cls) -> "RancherAsAServiceSettings":
        """Get the RancherSettings document

        Returns:
            RancherSettings
        """
        try:
            return cls.objects(id="the-one").get()
        except DoesNotExist:
            return RancherAsAServiceSettings(
                default_version_tag="not set",
                mgmt_cluster_vcpus=6,
                mgmt_cluster_memory=12288,
                mgmt_cluster_boot_disk_size=25,
                mgmt_cluster_data_disk_size=30,
                minimum_kubernetes_version="1.22",
            )


class RancherFailedJob(Document):
    """Represents a failed job in Rancher.

    Attributes:
        title (str): The name of the failed job.
        workflow_id (str): The ID of the workflow that produced the failure.
        timestamp (float): The timestamp of when the failure occurred, in seconds since the Unix epoch.
    """

    title = StringField(help_text="Name of failed job")
    workflow_id = StringField(help_text="Workflow id")
    timestamp = FloatField(readOnly=True, editable=False, export=True, default=time.time)

    meta = {"indexes": ["-timestamp"]}

    @classmethod
    def list(
        cls,
        start_time: str = None,
        end_time: str = None,
        search: str = None,
    ) -> List["RancherFailedJob"]:
        """List rancher failed jobs

        Keyword Arguments:
            limit {int} -- failed jobs returned limit.
            start_time {int} -- Starting timestamp of filtering records. (default: {None})
            end_time {int} -- Ending timestamp of filtering records. (default: {None})
            search {str} -- Search phrase to filter records with. (default: {None})

        Returns:
             List['RancherFailedJobs'] -- List of rancher failed jobs
        """
        query = cls.objects()

        if start_time:
            query = query(timestamp__gte=start_time)
        if end_time:
            query = query(timestamp__lte=end_time)
        if search:
            query = query(title={"$regex": search, "$options": "i"})
        return query.order_by("-timestamp")

    @classmethod
    def delete_failed_job(cls, _id: str):
        """Delete a rancher failed job by its ID

        Args:
            id (str): Rancher failed job ID
        """
        cls.objects(id=_id).delete()


class RancherVersions(Document):
    """Rancher supported versions"""

    tag = StringField(help_text="Rancher version tag", unique=True)
    supported_kubernetes_versions = ListField(StringField(), help_text="A list of supported kubernetes versions")

    @classmethod
    def list(cls) -> List["RancherVersions"]:
        """List all supported versions"""
        return cls.objects().order_by("-tag")

    @classmethod
    def get_by_tag(cls, tag: str) -> "RancherVersions":
        """Get rancher version by tag

        Args:
            tag (str): tag

        Returns:
            RancherVersions: Rancher version
        """
        return cls.objects(tag=tag).get()

    @classmethod
    def delete_by_tag(cls, tag: str):
        """Delete rancher version by tag

        Arguments:
            tag (str): Tag of the version to be delete
        """
        cls.objects.get(tag=tag).delete()
