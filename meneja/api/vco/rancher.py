# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=C0116, C0115

import mimetypes

from dacite.config import Config
from dacite.core import from_dict
from flask import Response, request
from flask.helpers import make_response
from flask_itsyouonline import authenticated, get_current_user_info, get_key
from flask_restx import Resource, reqparse
from flask_restx.inputs import boolean

from meneja.api import api
from meneja.api.vco import SUCCESS
from meneja.api.vco import alpha_ns as ns
from meneja.api.vco import get_vco_id, success_model
from meneja.business.auth import check_management_cluster_ownership, is_customer_org_admin, requires_custom
from meneja.business.vco.kaas import rancher
from meneja.lib.enumeration import CheckMessageLevel, EnvironmentName
from meneja.lib.fixes import FixedArgument
from meneja.lib.utils import from_dict_with_enum
from meneja.model.vco.rancher import RancherAsAServiceSettings as RancherAsAServiceSettingsModel
from meneja.model.vco.rancher import RancherManagementCluster as ManagementClusterModel
from meneja.structs.meneja.dataclasses.logs import ClusterTransitionLogs, TransitionLogsStruct
from meneja.structs.vco.dataclasses.rke_cluster import (
    CCMError,
    ClusterConnectedResources,
    CreateRancherManagementClusterKubernetesClusterStruct,
    KubeConfigStruct,
    LoadBalancerServiceStruct,
    NodeCreationStruct,
    RancherAsAServiceSettingsStruct,
    RancherBridgeRequest,
    RancherBridgeResponse,
    RancherManagementClusterCreationStruct,
    RancherManagementClusterCreationSuccessStruct,
    RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct,
    RancherManagementClusterKubernetesClusterNodePoolNodeStruct,
    RancherManagementClusterKubernetesClusterNodePoolStruct,
    RancherManagementClusterKubernetesClusterStruct,
    RancherManagementClusterNodeProvisioningStatusStruct,
    RancherManagementClusterNodeStatusStruct,
    RancherManagementClusterNodeStruct,
    RancherManagementClusterStruct,
    RancherSupportedKubernetesClusterVersionStruct,
    RancherVersionsGetStruct,
    SimpleKubernetesClusterNodeListStruct,
    SimpleKubernetesClusterNodeStruct,
    SimpleRancherManagementClusterStruct,
    SimpleVirtualMachineStruct,
    UpdateClusterJwtResult,
)

# Parsers
logs_parser = reqparse.RequestParser(argument_class=FixedArgument)
logs_parser.add_argument(
    "include_debug_logs",
    type=boolean,
    location="args",
    default=True,
    help="If set to True the debug log records will be included. "
    "This parameter is only relevant if include_logs is True",
)
delete_service_parser = reqparse.RequestParser(argument_class=FixedArgument)
delete_service_parser.add_argument(
    "primary_cloudspace_id",
    type=str,
    location="args",
    required=False,
    help="Primary cloudspace ID",
)
version_tag_parser = reqparse.RequestParser(argument_class=FixedArgument)
version_tag_parser.add_argument("tag", type=str, location="args", default="latest", help="Version tag to update to")

REQUEST_ROUTE = "/customers/<string:customer_id>/kubernetes/rancher"

is_dev_env = EnvironmentName.current() == EnvironmentName.DEV


@ns.route(REQUEST_ROUTE + "/rancher-bridge/<string:management_cluster_id>")
class RancherBridge(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(RancherBridgeRequest.list_model(api))
    @ns.doc(
        shortcut="pollForRancherAPIRequests",
        description="Polls for requests that need to be forwarded to the Rancher API",
    )
    def get(self, customer_id, management_cluster_id):
        return {"result": rancher.get_rancher_api_requests(get_vco_id(), customer_id, management_cluster_id)}

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(RancherBridgeResponse.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="queueRancherAPIResponse", description="Delivers the response of a Rancher API Request")
    def put(self, customer_id, management_cluster_id):
        rancher.queue_rancher_api_result(
            get_vco_id(), customer_id, management_cluster_id, RancherBridgeResponse.load(ns.payload)
        )
        return SUCCESS


@ns.route(REQUEST_ROUTE + "/management-clusters")
class RancherManagementClusters(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(RancherManagementClusterCreationStruct.model(api))
    @ns.marshal_with(RancherManagementClusterCreationSuccessStruct.model(api))
    @ns.doc(shortcut="CreateRancherManagementCluster", description="Creates a management cluster")
    def post(self, customer_id):
        management_cluster_id = rancher.create_management_cluster(
            jwt=get_current_user_info().jwt,
            customer_id=customer_id,
            vco_id=get_vco_id(),
            cluster_creation_struct=from_dict_with_enum(
                data_class=RancherManagementClusterCreationStruct, data=ns.payload
            ),
            iam_key=get_key(),
            user_name=get_current_user_info().username,
        )
        return RancherManagementClusterCreationSuccessStruct(management_cluster_id)

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.marshal_with(SimpleRancherManagementClusterStruct.list_model(api))
    @ns.doc(shortcut="GetManagementClusters", description="Lists customer's Management clusters")
    def get(self, customer_id):
        return dict(result=ManagementClusterModel.list(customer_id=customer_id))


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>")
class RancherManagementCluster(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(success_model)
    @ns.doc(shortcut="DeleteRancherManagementCluster", description="Deletes a management cluster")
    def delete(self, customer_id, management_cluster_id):
        jwt = get_current_user_info().jwt
        rancher.delete_management_cluster(
            jwt=jwt, customer_id=customer_id, management_cluster_id=management_cluster_id, vco_id=get_vco_id()
        )
        return SUCCESS

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(shortcut="GetRancherManagementCluster", description="Gets management cluster")
    @ns.marshal_with(RancherManagementClusterStruct.model(api))
    def get(self, customer_id, management_cluster_id):
        return rancher.get_management_cluster(customer_id=customer_id, management_cluster_id=management_cluster_id)

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(success_model)
    @ns.expect(version_tag_parser)
    @ns.doc(shortcut="updateRancherManagementCluster", description="Updates rancher management")
    def put(self, customer_id, management_cluster_id):
        tag = version_tag_parser.parse_args()["tag"]
        rancher.update_rancher_image(
            customer_id=customer_id,
            rancher_id=management_cluster_id,
            tag=tag,
            namespace="cattle-system",
            deployment_name="cattle",
            image_name="rancher",
            container_name="cattle-server",
            validate=True,
        )
        return SUCCESS


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/jwt")
class RancherManagementClusterJwt(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(UpdateClusterJwtResult.model(api))
    @ns.doc(
        shortcut="UpdateRancherManagementClusterJwt", description="Updates jwt on all vms related to management cluster"
    )
    def put(self, customer_id, management_cluster_id):
        jwt = get_current_user_info().jwt
        return rancher.update_jwt(
            jwt=jwt, management_cluster_id=management_cluster_id, vco_id=get_vco_id(), customer_id=customer_id
        )


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/provisioned-nodes")
class RancherManagementClusterProvisionedNodes(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(shortcut="GetRancherManagementClusterNodes", description="Gets management cluster nodes")
    @ns.marshal_with(RancherManagementClusterNodeStruct.list_model(api))
    def get(self, customer_id, management_cluster_id):
        return {
            "result": rancher.list_nodes(get_current_user_info().jwt, get_vco_id(), customer_id, management_cluster_id)
        }


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/kubernetes-versions")
class RancherManagementClusterSupportedKubernetesVersions(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(RancherSupportedKubernetesClusterVersionStruct.list_model(api))
    @ns.doc(
        shortcut="listRancherManagementClusterSupportedKubernetesVersions",
        description="List supported kubernetes versions",
    )
    def get(self, customer_id, management_cluster_id):
        return {"result": rancher.list_supported_kubernetes_versions(get_vco_id(), customer_id, management_cluster_id)}


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/vacuum")
class RancherManagementClusterVacuumer(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(SimpleKubernetesClusterNodeStruct.list_model(api))
    @ns.doc(
        shortcut="listDanglingNodesRancherManagementCluster",
        description="List management cluster nodes that can be removed",
    )
    def get(self, customer_id, management_cluster_id):
        return {
            "result": rancher.get_rancher_management_cluster_dangling_nodes(
                get_current_user_info().jwt, get_vco_id(), customer_id, management_cluster_id
            )
        }

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.expect(SimpleKubernetesClusterNodeListStruct.model(api))
    @ns.marshal_with(success_model)
    @ns.doc(
        shortcut="VacuumRancherManagementCluster",
        description="Vacuums a management cluster for associated nodes that can be removed",
    )
    def put(self, customer_id, management_cluster_id):
        rancher.delete_dangling_nodes(
            get_current_user_info().jwt,
            get_vco_id(),
            customer_id,
            management_cluster_id,
            SimpleKubernetesClusterNodeListStruct.load(ns.payload).nodes,
        )
        return SUCCESS


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/kubernetes-clusters")
class RancherManagementClusterKubernetesClusters(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(shortcut="RancherManagementClusterGetKubernetesClusters", description="Lists kubernetes clusters")
    @ns.marshal_with(RancherManagementClusterKubernetesClusterStruct.list_model(api))
    def get(self, customer_id, management_cluster_id):
        return {"result": rancher.get_kubernetes_clusters(management_cluster_id, customer_id)}

    @authenticated
    @requires_custom(is_customer_org_admin)
    @ns.expect(CreateRancherManagementClusterKubernetesClusterStruct.model(api))
    @ns.marshal_with(RancherManagementClusterKubernetesClusterStruct.model(api))
    @ns.doc(
        shortcut="CreateRancherManagementClusterKubernetesCluster", description="Creates an RKE2 kubernetes cluster"
    )
    def post(self, customer_id, management_cluster_id):
        return rancher.create_kubernetes_cluster(
            get_current_user_info().jwt,
            get_vco_id(),
            customer_id,
            management_cluster_id,
            from_dict(CreateRancherManagementClusterKubernetesClusterStruct, data=ns.payload),
        )


@ns.route(
    REQUEST_ROUTE
    + "/management-clusters/<string:management_cluster_id>/kubernetes-clusters/<string:kubernetes_cluster_id>"
)
class RancherManagementClusterKubernetesCluster(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(shortcut="rancherManagementClusterGetKubernetesCluster", description="Gets a kubernetes cluster")
    @ns.marshal_with(RancherManagementClusterKubernetesClusterStruct.model(api))
    def get(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        return rancher.get_kubernetes_cluster(get_vco_id(), management_cluster_id, customer_id, kubernetes_cluster_id)

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(shortcut="rancherManagementClusterDeleteKubernetesCluster", description="Deletes a kubernetes cluster")
    def delete(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        return rancher.delete_kubernetes_cluster(
            get_current_user_info().jwt, get_vco_id(), customer_id, management_cluster_id, kubernetes_cluster_id
        )


@ns.route(
    REQUEST_ROUTE
    + "/management-clusters/<string:management_cluster_id>/kubernetes-clusters/"
    + "<string:kubernetes_cluster_id>/upgrade"
)
class RancherManagementClusterUpgradeKubernetesCluster(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterUpgradeKubernetesCluster",
        description="Upgrades a kubernetes cluster to a higher version",
    )
    @ns.expect(RancherSupportedKubernetesClusterVersionStruct.model(api), validate=True)
    def put(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        rancher.upgrade_kubernetes_cluster(
            get_vco_id(),
            customer_id,
            management_cluster_id,
            kubernetes_cluster_id,
            from_dict(RancherSupportedKubernetesClusterVersionStruct, data=ns.payload),
        )


@ns.route(
    REQUEST_ROUTE
    + "/management-clusters/<string:management_cluster_id>/kubernetes-clusters/"
    + "<string:kubernetes_cluster_id>/kube-config"
)
class RancherManagementClusterKubernetesClusterKubeConfig(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterGetKubernetesClusterKubeConfig",
        description="Gets a kubernetes cluster kube config",
        model=KubeConfigStruct.model(api),
    )
    @ns.produces(["application/json", "text/plain"])
    def get(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        config = rancher.download_cluster_kubeconfig(
            get_vco_id(), customer_id, management_cluster_id, kubernetes_cluster_id
        )
        if request.headers.get("Accept") == "application/json":
            return dict(result=config)
        return make_response((config, 200, {"Content-Type": "text/plain", "Content-Disposition": "kubeconfig.yml"}))


@ns.route(
    REQUEST_ROUTE
    + "/management-clusters/<string:management_cluster_id>/kubernetes-clusters/"
    + "<string:kubernetes_cluster_id>/integration"
)
class RancherManagementClusterKubernetesClusterCSI(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="RancherManagementClusterKubernetesClusterInstallIntegrationComponents",
        description="Installs the cloud support integration components into kubernetes cluster",
    )
    @ns.marshal_with(success_model)
    def put(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        rancher.install_integration_components(
            get_current_user_info().jwt, get_vco_id(), customer_id, management_cluster_id, kubernetes_cluster_id
        )
        return SUCCESS


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/"
    "kubernetes-clusters/<string:kubernetes_cluster_id>/error-message",
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterKubernetesClusterError(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.expect(CCMError.model(api), validate=True)
    def post(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        rancher.report_ccm_error(
            from_dict(CCMError, data=ns.payload).error_message,
            management_cluster_id,
            kubernetes_cluster_id,
            customer_id,
            get_vco_id(),
        )


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/"
    "kubernetes-clusters/<string:kubernetes_cluster_id>/lb-services",
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterKubernetesClusterLoadBalancerServices(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterRegisterLBService",
        description="Register Service",
    )
    @ns.expect(LoadBalancerServiceStruct.model(api), validate=True)
    def post(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        return rancher.register_load_balancer_service(
            customer_id,
            get_vco_id(),
            get_current_user_info().jwt,
            management_cluster_id,
            kubernetes_cluster_id,
            from_dict(LoadBalancerServiceStruct, data=ns.payload),
        )


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/"
    "kubernetes-clusters/<string:kubernetes_cluster_id>/lb-services/<string:service_uid>",
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterKubernetesClusterLoadBalancerServicesDelete(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.expect(delete_service_parser)
    def delete(self, customer_id, management_cluster_id, kubernetes_cluster_id, service_uid):
        args = delete_service_parser.parse_args()
        return rancher.remove_load_balancer_service(
            customer_id,
            get_vco_id(),
            management_cluster_id,
            kubernetes_cluster_id,
            service_uid,
            get_current_user_info().jwt,
            cloudspace_id=args.get("primary_cloudspace_id"),
        )


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/"
    "kubernetes-clusters/<string:kubernetes_cluster_id>/lb-services/ensure",
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterKubernetesClusterLoadBalancerServicesEnsure(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.expect(LoadBalancerServiceStruct.model(api), validate=True)
    def post(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        rancher.ensure_load_balancer_service(
            customer_id,
            get_vco_id(),
            management_cluster_id,
            kubernetes_cluster_id,
            get_current_user_info().jwt,
            from_dict(LoadBalancerServiceStruct, data=ns.payload),
        )
        return SUCCESS


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>"
    "/kubernetes-clusters/<string:kubernetes_cluster_id>/node-pools"
)
class RancherManagementClusterKubernetesClusterNodePools(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterListKubernetesClusterNodePools",
        description="List kubernetes cluster node pools",
    )
    @ns.marshal_with(RancherManagementClusterKubernetesClusterNodePoolStruct.list_model(api))
    def get(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        return {
            "result": rancher.list_node_pools(get_vco_id(), customer_id, management_cluster_id, kubernetes_cluster_id)
        }

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterListKubernetesClusterAddNodePool",
        description="Add kubernetes cluster node pool",
    )
    @ns.marshal_with(RancherManagementClusterKubernetesClusterNodePoolStruct.model(api))
    @ns.expect(RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct.model(api))
    def post(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        return rancher.add_node_pool(
            get_current_user_info().jwt,
            get_vco_id(),
            customer_id,
            management_cluster_id,
            kubernetes_cluster_id,
            from_dict(RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct, data=ns.payload),
        )


@ns.route(
    (
        f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/kubernetes-clusters"
        "/<string:kubernetes_cluster_id>/node-pools/<string:node_pool_id>"
    )
)
class RancherManagementClusterKubernetesClusterNodePool(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterGetKubernetesClusterNodePool", description="Get kubernetes cluster node pool"
    )
    @ns.marshal_with(RancherManagementClusterKubernetesClusterNodePoolStruct.model(api))
    def get(self, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id):
        return rancher.get_node_pool(
            get_current_user_info().jwt,
            get_vco_id(),
            customer_id,
            management_cluster_id,
            kubernetes_cluster_id,
            node_pool_id,
        )

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterDeleteKubernetesClusterNodePool",
        description="Delete kubernetes cluster node pool",
    )
    @ns.marshal_with(success_model)
    def delete(self, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id):
        rancher.delete_node_pool(
            get_vco_id(),
            customer_id,
            management_cluster_id,
            kubernetes_cluster_id,
            node_pool_id,
        )
        return SUCCESS


scale_nodepool_parser = reqparse.RequestParser(argument_class=FixedArgument)
scale_nodepool_parser.add_argument("new_size", type=int, location="args", help="New size for the node pool")


@ns.route(
    (
        f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/kubernetes-clusters"
        "/<string:kubernetes_cluster_id>/node-pools/<string:node_pool_id>/scale"
    )
)
class RancherManagementClusterKubernetesScaleClusterNodePool(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterScaleKubernetesClusterNodePool",
        description="Scale kubernetes cluster node pool",
    )
    @ns.expect(scale_nodepool_parser)
    @ns.marshal_with(success_model)
    def put(self, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id):
        rancher.scale_node_pool(
            get_vco_id(),
            customer_id,
            management_cluster_id,
            kubernetes_cluster_id,
            node_pool_id,
            scale_nodepool_parser.parse_args()["new_size"],
        )
        return SUCCESS


@ns.route(
    (
        f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/kubernetes-clusters"
        "/<string:kubernetes_cluster_id>/node-pools/<string:node_pool_id>/install-ingress"
    )
)
class RancherManagementClusterKubernetesClusterNodePoolInstallIngress(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterKubernetesClusterNodePoolInstallIngress",
        description="Install ingress for a kubernetes cluster node pool",
    )
    @ns.marshal_with(success_model)
    def put(self, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id):
        rancher.install_ingress(
            get_current_user_info().jwt,
            get_vco_id(),
            customer_id,
            management_cluster_id,
            kubernetes_cluster_id,
            [node_pool_id],
        )
        return SUCCESS

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterKubernetesClusterNodePoolUninstallIngress",
        description="Uninstall ingress for a kubernetes cluster node pool",
    )
    @ns.marshal_with(success_model)
    def delete(
        self, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id
    ):  # pylint: disable=unused-argument
        rancher.uninstall_ingress_controller(
            get_vco_id(),
            management_cluster_id,
            get_current_user_info().jwt,
            kubernetes_cluster_id,
            node_pool_id,
        )
        return SUCCESS


@ns.route(
    (
        f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/kubernetes-clusters"
        "/<string:kubernetes_cluster_id>/node-pools/<string:node_pool_id>/nodes"
    )
)
class RancherManagementClusterKubernetesClusterNodePoolNodes(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterKubernetesClusterGetNodePoolNodes",
        description="Get kubernetes cluster node pool nodes",
    )
    @ns.marshal_with(RancherManagementClusterKubernetesClusterNodePoolNodeStruct.list_model(api))
    def get(self, customer_id, management_cluster_id, kubernetes_cluster_id, node_pool_id):
        return {
            "result": rancher.list_node_pool_nodes(
                get_current_user_info().jwt,
                get_vco_id(),
                customer_id,
                management_cluster_id,
                kubernetes_cluster_id,
                node_pool_id,
            )
        }


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/kube-config")
class RancherManagementClusterKubeConfig(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="GetManagementClusterKubeConfig",
        description="Gets management cluster kubeconfig",
        model=KubeConfigStruct.model(api),
    )
    @ns.produces(["application/json", "text/plain"])
    def get(self, customer_id, management_cluster_id):
        jwt = get_current_user_info().jwt
        config = rancher.get_kube_config(customer_id, management_cluster_id, jwt)
        if request.headers.get("Accept") == "application/json":
            return dict(result=config)
        return make_response((config, 200, {"Content-Type": "text/plain", "Content-Disposition": "kubeconfig.yml"}))


@ns.route(f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/transitions")
class RancherManagementClusterTransitions(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(ClusterTransitionLogs.list_model(api))
    @ns.doc(shortcut="GetManagementTransitions", description="Get Management Cluster transitions")
    def get(self, customer_id, management_cluster_id):
        cluster = ManagementClusterModel.get_by_id(management_cluster_id, only=["transition_tasks_details"])
        if cluster.customer_id != customer_id:
            raise KeyError("Not found!")
        return dict(result=cluster.transition_tasks_details)


@ns.route(f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/transitions/<string:transition_id>")
class RancherManagementClusterTransition(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.marshal_with(ClusterTransitionLogs.model(api))
    @ns.doc(shortcut="GetManagementTransition", description="Get Management Cluster transition by id")
    def get(self, customer_id, management_cluster_id, transition_id):
        return ManagementClusterModel.get_transition_by_id(customer_id, management_cluster_id, transition_id)


@ns.route(f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/transitions/<string:transition_id>/logs")
class RancherManagementClusterTransitionLogs(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc("GetManagementLogs", description="Gets Management Cluster logs")
    @ns.marshal_with(TransitionLogsStruct.list_model(api))
    @ns.expect(logs_parser)
    def get(self, customer_id, management_cluster_id, transition_id):
        args = logs_parser.parse_args()
        include_debug_logs = args.get("include_debug_logs")
        return dict(
            result=rancher.get_pipeline_logs(customer_id, management_cluster_id, transition_id, include_debug_logs)
        )


create_provisioning_parser = reqparse.RequestParser(argument_class=FixedArgument)
create_provisioning_parser.add_argument(
    "async",
    type=boolean,
    default=False,
    location="args",
    help="Flag indicating wether this call should return after creating the vm or after the provisioning is completed.",
)


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/nodes", doc=False if not is_dev_env else None
)
class RancherManagementClusterNodes(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc("CreateClusterNode", description="Creates a Cluster node")
    @ns.expect(NodeCreationStruct.model(api), create_provisioning_parser)
    @ns.marshal_with(SimpleVirtualMachineStruct.model(api))
    def post(self, customer_id, management_cluster_id):
        jwt = get_current_user_info().jwt
        return dict(
            vm_id=rancher.create_node(
                customer_id,
                management_cluster_id,
                from_dict(data_class=NodeCreationStruct, data=ns.payload),
                jwt,
                get_vco_id(),
                get_key(),
            )
        )

    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc("ListClusterNodes", description="List RKE Cluster nodes")
    @ns.marshal_with(SimpleKubernetesClusterNodeStruct.list_model(api))
    def get(self, customer_id, management_cluster_id):
        return dict(result=ManagementClusterModel.list_nodes(management_cluster_id, customer_id))


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/nodes/<string:cloudspace_id>/<int:vm_id>",
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterNode(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc("DeleteRancherManagementClusterNode", description="Deletes RKE cluster node")
    @ns.marshal_with(success_model)
    def delete(self, customer_id, management_cluster_id, cloudspace_id, vm_id):
        jwt = get_current_user_info().jwt
        return rancher.delete_node(management_cluster_id, cloudspace_id, jwt, customer_id, vm_id=vm_id, node_name=None)


@ns.route(
    f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>"
    "/nodes/<string:cloudspace_id>/<string:node_name>",
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterNodeV2(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc("DeleteRancherManagementClusterNodeV2", description="Deletes RKE cluster node")
    @ns.marshal_with(success_model)
    def delete(self, customer_id, management_cluster_id, cloudspace_id, node_name):
        jwt = get_current_user_info().jwt
        return rancher.delete_node(
            management_cluster_id, cloudspace_id, jwt, customer_id, node_name=node_name, vm_id=None
        )


node_provisioning_parser = reqparse.RequestParser(argument_class=FixedArgument)
node_provisioning_parser.add_argument(
    "wait",
    type=int,
    default=60,
    location="args",
    help="amount of time to wait before returning while still in progress",
)


@ns.route(
    (
        f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/nodes/"
        f"<string:cloudspace_id>/<string:node_name>/provision-status"
    ),
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterNodeProvisionStatus(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.expect(node_provisioning_parser)
    @ns.doc("GetRancherManagementClusterNodeProvisionStatus", description="Get node provision status")
    @ns.marshal_with(RancherManagementClusterNodeProvisioningStatusStruct.model(api))
    def get(self, customer_id, management_cluster_id, cloudspace_id, node_name):
        args = node_provisioning_parser.parse_args()
        status, vm_id = rancher.poll_node_provisioning(
            customer_id, management_cluster_id, cloudspace_id, node_name, wait=args["wait"]
        )
        return RancherManagementClusterNodeProvisioningStatusStruct(status, vm_id)


@ns.route(
    (
        f"{REQUEST_ROUTE}/management-clusters/<string:management_cluster_id>/nodes/"
        f"<string:cloudspace_id>/<int:vm_id>/healthchecks"
    ),
    doc=False if not is_dev_env else None,
)
class RancherManagementClusterNodeHealthChecks(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc("UpdateRancherManagementClusterNodeHealth", description="Update RKE cluster node health")
    @ns.expect(RancherManagementClusterNodeStatusStruct.model(api))
    @ns.marshal_with(success_model)
    def put(self, customer_id, management_cluster_id, cloudspace_id, vm_id):
        rancher.update_health_status(
            management_cluster_id,
            customer_id,
            cloudspace_id,
            vm_id,
            from_dict(
                data_class=RancherManagementClusterNodeStatusStruct,
                data=ns.payload,
                config=Config(type_hooks={CheckMessageLevel: CheckMessageLevel}),
            ),
        )
        return SUCCESS


@ns.route("/rancher/node-driver/<version>/<driver_binary>", doc=False if not is_dev_env else None)
class FetchNodeDriver(Resource):
    def get(self, version, driver_binary):
        binary = rancher.get_node_driver_binary(get_vco_id(), version, driver_binary)
        return make_response(
            binary,
            200,
            {"Content-Type": "application/octet-stream", "Content-Disposition": f"filename={driver_binary}"},
        )


@ns.route("/rancher/node-driver-ui/<ui_resource>", doc=False if not is_dev_env else None)
class FetchNodeDriverUI(Resource):
    def get(self, ui_resource):
        domain = None
        vco_id = None
        headers = {}
        try:
            vco_id = get_vco_id()
        except AttributeError:
            domain = request.headers.get("X-Forwarded-Host")
            if not rancher.check_cluster_hostname_exists(domain):
                return Response("Page not found", 404)
            headers["Access-Control-Allow-Origin"] = domain

        resource = rancher.get_node_driver_ui_resource(ui_resource, vco_id, domain)
        mime_type, _ = mimetypes.guess_type(ui_resource)
        headers["Content-Type"] = mime_type
        headers["Content-Disposition"] = f"filename={ui_resource}"

        return make_response(resource, 200, headers)


@ns.route(REQUEST_ROUTE + "/management-clusters/<string:management_cluster_id>/resources")
class RancherManagementClusterResources(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterGetResources",
        description="Gets management cluster resources",
    )
    @ns.marshal_with(ClusterConnectedResources.model(api))
    def get(self, customer_id, management_cluster_id):
        return rancher.get_management_cluster_resources(
            get_current_user_info().jwt, get_vco_id(), customer_id, management_cluster_id
        )


@ns.route(
    REQUEST_ROUTE
    + "/management-clusters/<string:management_cluster_id>/kubernetes-clusters/<string:kubernetes_cluster_id>/resources"
)
class RancherManagementClusterKubernetesClusterResources(Resource):
    @authenticated
    @requires_custom(is_customer_org_admin, check_management_cluster_ownership)
    @ns.doc(
        shortcut="rancherManagementClusterGetKubernetesClusterResources",
        description="Gets a kubernetes cluster resources",
    )
    @ns.marshal_with(ClusterConnectedResources.model(api))
    def get(self, customer_id, management_cluster_id, kubernetes_cluster_id):
        return rancher.get_kubernetes_resources(
            get_current_user_info().jwt, get_vco_id(), customer_id, management_cluster_id, kubernetes_cluster_id
        )


@ns.route("/kubernetes-agent", doc=False)
class KubernetesAgent(Resource):
    def get(self):
        file_bytes = rancher.download_agent_package()
        return make_response(
            file_bytes,
            200,
            {"Content-Type": "application/octet-stream", "Content-Disposition": "filename=kubernetes-agent"},
        )


@ns.route("/kubernetes-prices")
class RancherAsAServiceSettings(Resource):
    @authenticated
    @ns.marshal_with(RancherAsAServiceSettingsStruct.model(api))
    @ns.doc(shortcut="getRancherAsAServiceSettings", description="Gets the rancher as a service settings")
    def get(self):
        return from_dict(RancherAsAServiceSettingsStruct, RancherAsAServiceSettingsModel.get_settings().to_mongo())


@ns.route(
    "/rancher/versions",
)
class RancherVersions(Resource):
    @authenticated
    @ns.marshal_with(RancherVersionsGetStruct.list_model(api))
    @ns.doc(shortcut="getRancherVersions", description="Get rancher supported versions")
    def get(self):
        return {"result": rancher.get_rancher_versions()}
