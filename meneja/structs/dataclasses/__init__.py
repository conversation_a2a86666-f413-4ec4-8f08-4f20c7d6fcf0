# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
"""Base struct for dataclass based api models"""

import logging
from dataclasses import dataclass, fields
from typing import Dict, List, Union

from dacite import from_dict
from dacite.config import Config
from flask_restx import fields as restplus_fields
from flask_restx.api import Api
from typing_utils import get_args, get_origin

from meneja.lib.enumeration import StringEnum

logger = logging.getLogger(__name__)


def _remove_none_values(data: dict) -> dict:
    if isinstance(data, dict):
        return {k: _remove_none_values(v) for k, v in data.items() if v is not None}
    elif isinstance(data, list):
        return [_remove_none_values(x) for x in data if x is not None]
    else:
        return data


class IntOrString(restplus_fields.Raw):
    """This class is used for fields of union of types (str - int)"""

    __schema_type__ = ["integer", "string"]  # For Swagger documentation
    __schema_example__ = "int or str'"  # For Swagger documentation

    def format(self, value):
        if isinstance(value, (int, str)):
            return value
        raise fields.MarshallingError("Field must be an integer or a string.")


# pylint: disable=too-many-branches
@dataclass
class BaseStruct:
    """Base struct for dataclasses based api model"""

    __schema_name__ = "BaseStruct"

    # pylint: disable=too-many-locals
    @classmethod
    def model(cls, api: Api, append_package_to_class_name: bool = True, show_hidden: bool = False):
        """Generate a restx api model from the dataclass"""
        NoneType = type(None)
        schema = {}
        # pylint: disable=no-member
        props = cls.__dataclass_fields__
        for prop_name in props:
            prop = props[prop_name]
            prop_type = prop.type
            model_prop_name = prop.metadata.get("prop_name", prop_name)
            help_text = prop.metadata.get("help_text", "default")
            default = prop.metadata.get("default", None)
            enum = prop.metadata.get("enum")
            read_only = prop.metadata.get("readOnly")
            if not show_hidden and prop.metadata.get("hidden", False):
                continue
            # Analyze type
            origin = get_origin(prop_type)
            args = get_args(prop_type)
            if origin is Union and all(isinstance(arg, type) for arg in args):
                if str in args and int in args:
                    prop_type = IntOrString
                    origin = None
                    args = ()

            if origin is Union and len(args) == 2 and args[1] == NoneType:
                is_required = False
                prop_type = args[0]
                origin = get_origin(prop_type)
                args = get_args(prop_type)
            else:
                is_required = True
            if origin is list:
                is_list = True
                prop_type = args[0]
            else:
                is_list = False
            common_attrs = dict(description=help_text, attribute=prop_name, required=is_required, default=default)
            if is_list:
                if prop_type is int:
                    schema[model_prop_name] = restplus_fields.List(restplus_fields.Integer, **common_attrs)
                elif prop_type is str:
                    schema[model_prop_name] = restplus_fields.List(restplus_fields.String, **common_attrs)
                elif issubclass(prop_type, BaseStruct):
                    schema[model_prop_name] = restplus_fields.List(
                        restplus_fields.Nested(
                            prop_type.model(api, append_package_to_class_name, show_hidden),
                        ),
                        **common_attrs,
                        readOnly=read_only,
                    )
            elif prop_type is str:
                kwargs = dict(**common_attrs)
                if enum:
                    kwargs["enum"] = enum
                schema[model_prop_name] = restplus_fields.String(**kwargs)
            elif prop_type is int:
                schema[model_prop_name] = restplus_fields.Integer(**common_attrs, readOnly=read_only)
            elif prop_type is float:
                schema[model_prop_name] = restplus_fields.Float(**common_attrs, readOnly=read_only)
            elif prop_type is bool:
                schema[model_prop_name] = restplus_fields.Boolean(**common_attrs, readOnly=read_only)
            elif prop_type is dict or prop_type is Dict:
                schema[model_prop_name] = restplus_fields.Raw(**common_attrs, readOnly=read_only)
            elif prop_type is IntOrString:
                schema[model_prop_name] = IntOrString(**common_attrs, readOnly=read_only)
            elif issubclass(prop_type, BaseStruct):
                schema[model_prop_name] = restplus_fields.Nested(
                    prop_type.model(api, append_package_to_class_name, show_hidden), **common_attrs, readOnly=read_only
                )
            elif issubclass(prop_type, StringEnum):
                schema[model_prop_name] = restplus_fields.String(
                    enum=prop_type.values(), **common_attrs, readOnly=read_only
                )
            else:
                raise NotImplementedError(f"'{prop_type}' is not implemented yet")
        class_name = ""
        if append_package_to_class_name:
            for package in cls.__module__.split("."):
                class_name += f"{package[0].upper()}{package[1:]}"
        class_name += cls.__name__
        return api.model(class_name, schema)

    @classmethod
    def list_model(cls, api: Api, append_package_to_class_name: bool = True, field: str = "result"):
        """Generate a list of restx api model from the dataclass"""
        if not hasattr(cls, "_list_model"):
            cls._list_model = api.model(
                f"{cls.__name__}s",
                {field: restplus_fields.List(restplus_fields.Nested(cls.model(api, append_package_to_class_name)))},
            )
        return cls._list_model

    @classmethod
    def load(cls, data: dict, remove_none: bool = False, config: Config = None) -> "BaseStruct":
        """Generate instance of dataclass from dictionary

        Args:
            data (dict): Dict contains the data
            remove_none (bool) defaults False: If true all none values will be removed
            config (Config): defaults None: Dacite config to be used in from_dict
        Returns:
            BaseStruct: The data class instance
        """
        if remove_none:
            data = _remove_none_values(data)
        return from_dict(data_class=cls, data=data, config=config)

    @property
    def attribute_names(self) -> List[str]:
        """get dataclass attribute names

        Returns:
            List[str]: names of struct attributes names
        """
        return [field.name for field in fields(self)]
