# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@
"""Structs for VCOs"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional

from meneja.lib.enumeration import BackupStatuses, PolicyTargetS3LockingMode, SnapshotPolicyFailureBehaviour
from meneja.structs.dataclasses import BaseStruct


@dataclass
class CustomerComplianceStruct(BaseStruct):
    """Class holding information about the number of compliance issues for a certain customer"""

    customer_id: str = field(metadata=dict(help_text="Customer ID"))
    customer_name: str = field(metadata=dict(help_text="Customer name"))
    issues_count: int = field(metadata=dict(help_text="Number of issues count"))


@dataclass
class AuditLogForwardingConfig(BaseStruct):
    """Class holding data of audit log forwarding config"""

    url: str = field(metadata=dict(help_text="Url to upload audits to"))
    headers: Optional[dict] = field(metadata=dict(help_text="Url headers"))


@dataclass
class AuditLogForwardingStatus(BaseStruct):
    """Class holding data of audit log forwarding config"""

    status_code: int = field(metadata=dict(help_text="response status code"))
    status_message: str = field(metadata=dict(help_text="response status message"))
    body: str = field(metadata=dict(help_text="response body"))


@dataclass
class CustomerSupportTextStruct(BaseStruct):
    """Class holding the text for customer support page"""

    customer_support_text: str = field(metadata=dict(help_text="Customer support text"))


@dataclass
class VCOCachedDetails:
    """Class holding some frequently accessed details about VCOs"""

    id: str = field(metadata=dict(help_text="The VCO id"))
    domain: str = field(metadata=dict(help_text="The VCO domain"))
    iam_domain: str = field(metadata=dict(help_text="The VCO iam domain"))
    iam_root_organization: str = field(metadata=dict(help_text="The VCO iam organization"))
    iam_root_organization_api_key: str = field(metadata=dict(help_text="The VCO iam client_secret"))
    support_email_address: str = field(metadata=dict(help_text="The VCO support email address"))


@dataclass
class LicenseIncompliancyForwardingStruct(BaseStruct):
    """Struct to hold license incompliancy forwarding status"""

    enabled: bool = field(metadata=dict(help_text="License incompliancy forwarding status"))


@dataclass
class PartUploadData(BaseStruct):
    part_number: int = field(metadata=dict(help_text="Part number"))
    upload_url: str = field(metadata=dict(help_text="Part upload url"))


@dataclass
class UploadedParts(BaseStruct):
    part_number: int = field(metadata=dict(help_text="Part number"))
    etag: str = field(metadata=dict(help_text="Part etag"))


@dataclass
class UploadImageStruct(BaseStruct):
    upload_id: str = field(metadata=dict(help_text="Image upload id"))
    object_name: str = field(metadata=dict(help_text="Image object name to generate download url"))
    part_size: int = field(metadata=dict(help_text="Image part size in bytes"))


@dataclass
class CreateImageExpectStruct(BaseStruct):
    object_name: str = field(metadata=dict(help_text="Image file object name in minio"))
    uploaded_parts: List[UploadedParts] = field(metadata=dict(help_text="Image file uploaded parts"))


@dataclass
class UploadVersionStruct(UploadImageStruct):
    version_id: str = field(metadata=dict(help_text="Version id"))


@dataclass
class UploadVersionExpectStruct(BaseStruct):
    upload_id: str = field(metadata=dict(help_text="Image file multipart upload id"))
    uploaded_parts: List[UploadedParts] = field(metadata=dict(help_text="Image file uploaded parts"))


@dataclass
class CreateImageUserDataTemplateStruct(BaseStruct):
    template_id: str = field(metadata=dict(help_text="Template id"))


@dataclass
class TemplateParametersStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Parameter name"))
    default_value: str = field(metadata=dict(help_text="Parameter default value"))
    description: str = field(metadata=dict(help_text="Parameter description"))


@dataclass
class ImageUserDataTemplateStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="Template id"))
    label: str = field(metadata=dict(help_text="Template label"))
    description: str = field(metadata=dict(help_text="Template description"))
    content: str = field(metadata=dict(help_text="Template content"))
    added_on: int = field(metadata=dict(help_text="Template creation time"))
    parameters: List[TemplateParametersStruct] = field(metadata=dict(help_text="Template parameters"))


@dataclass
class BackupMetadataOwnerStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Owner name"))
    id: str = field(metadata=dict(help_text="Owner ID"))


@dataclass
class BackupMetadataStruct(BaseStruct):
    by_ce: bool = field(metadata=dict(help_text="Can be accessed by CE"))
    by_vco_admin: bool = field(metadata=dict(help_text="Can be accessed by VCO admin"))
    by_customer: bool = field(metadata=dict(help_text="Can be accessed by customer"))
    customer: BackupMetadataOwnerStruct = field(metadata=dict(help_text="Customer"))
    vco: BackupMetadataOwnerStruct = field(metadata=dict(help_text="VCO"))
    ce: BackupMetadataOwnerStruct = field(metadata=dict(help_text="Cloud Enabler"))


@dataclass
class S3ConfigStruct(BaseStruct):
    url: str = field(metadata=dict(help_text="S3 endpoint URL"))
    region: str = field(metadata=dict(help_text="S3 region"))
    bucket: str = field(metadata=dict(help_text="S3 bucket name"))
    locking_mode: str = field(metadata=dict(help_text="S3 locking mode", enum=PolicyTargetS3LockingMode.values()))


@dataclass
class S3ConfigCreateStruct(S3ConfigStruct):
    access_key: str = field(metadata=dict(help_text="S3 access key"))
    secret_key: str = field(metadata=dict(help_text="S3 secret key"))


@dataclass
class BackupBaseTargetStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Backup target name"))


@dataclass
class BackupCreateTargetStruct(BackupBaseTargetStruct):
    restic_password: str = field(metadata=dict(help_text="Restic password"))
    s3: S3ConfigCreateStruct = field(metadata=dict(help_text="S3 configuration"))


@dataclass
class CeBackupCreateTargetStruct(BackupBaseTargetStruct):
    restic_password: str = field(metadata=dict(help_text="Restic password"))
    s3: S3ConfigCreateStruct = field(metadata=dict(help_text="S3 configuration"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))


@dataclass
class subscribedPolicyG8(BaseStruct):
    id: str = field(metadata=dict(help_text="Target/Policy id"))
    name: str = field(metadata=dict(help_text="Target/Policy name"))
    location: str = field(metadata=dict(help_text="Location"))
    overridable: bool = field(metadata=dict(help_text="Overridable"))


@dataclass
class assignedPolicies(BaseStruct):
    policy_id: str = field(metadata=dict(help_text="Policy id"))
    subscribed_g8s: List[subscribedPolicyG8] = field(metadata=dict(help_text="Policy id"))


@dataclass
class subscribedG8(BaseStruct):
    id: str = field(metadata=dict(help_text="Target/Policy id"))
    location: str = field(metadata=dict(help_text="Location"))
    cloudspace_name: str = field(metadata=dict(help_text="Cloudspace Name"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace ID"))
    overridable: bool = field(metadata=dict(help_text="Overridable"))


@dataclass
class BackupTargetStruct(BackupBaseTargetStruct):
    id: str = field(metadata=dict(help_text="Backup target ID"))
    s3: S3ConfigStruct = field(metadata=dict(help_text="S3 configuration"))
    subscribed_g8s: List[subscribedG8] = field(metadata=dict(help_text="Subscribed G8s"))
    assigned_policies: List[assignedPolicies] = field(metadata=dict(help_text="Subscribed G8s"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))


@dataclass
class BackupTargetsStruct(BackupBaseTargetStruct):
    id: str = field(metadata=dict(help_text="Backup target ID"))
    name: str = field(metadata=dict(help_text="Backup target name"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))
    subscribed_g8s: List[subscribedG8] = field(metadata=dict(help_text="Subscribed G8s"))


@dataclass
class SnapshotPolicy(BaseStruct):
    cooperative: bool = field(metadata=dict(help_text="Cooperative snapshot policy"))
    cooperative_timeout: int = field(metadata=dict(help_text="Cooperative snapshot timeout in seconds"))
    cooperative_failure_behaviour: str = field(
        metadata=dict(help_text="Cooperative snapshot failure behaviour", enum=SnapshotPolicyFailureBehaviour.values())
    )
    timeout: int = field(metadata=dict(help_text="Snapshot timeout in seconds"))
    retry_pause: int = field(metadata=dict(help_text="Retry pauses in seconds"))
    retry_times: int = field(metadata=dict(help_text="Retry times"))


@dataclass
class BackupPolicyBaseStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Policy name"))
    cron: str = field(metadata=dict(help_text="Cron frequency"))
    restic_retention_flags: str = field(metadata=dict(help_text="Restriction retention", default=""))
    failure_report_email: str = field(metadata=dict(help_text="Report email"))


@dataclass
class BackupPolicyListStruct(BackupPolicyBaseStruct):
    id: str = field(metadata=dict(help_text="Backup policy ID"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))
    subscribed_g8s: List[subscribedPolicyG8] = field(metadata=dict(help_text="Subscribed G8s"))


@dataclass
class BackupPolicyStruct(BackupPolicyBaseStruct):
    snapshot_policy: SnapshotPolicy = field(metadata=dict(help_text="Snapshot policy"))


@dataclass
class BackupPoliciesStruct(BackupPolicyStruct):
    id: str = field(metadata=dict(help_text="Backup policy ID"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))
    subscribed_g8s: List[subscribedPolicyG8] = field(metadata=dict(help_text="Subscribed G8s"))


@dataclass
class BackupBaseStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="Backup ID"))
    target: int = field(metadata=dict(help_text="Backup target ID"))
    policy: Optional[str] = field(metadata=dict(help_text="Backup policy ID"))
    policy_name: Optional[str] = field(metadata=dict(help_text="Policy name"))
    location: str = field(metadata=dict(help_text="Backup location"))
    creation_timestamp: int = field(metadata=dict(help_text="Backup creation timestamp"))
    start_time: int = field(metadata=dict(help_text="Backup start time"))
    end_time: int = field(metadata=dict(help_text="Backup end time"))
    snapshot_time: int = field(metadata=dict(help_text="Backup snapshot time"))
    snapshot_end_time: int = field(metadata=dict(help_text="Backup snapshot end time"))
    origin: str = field(metadata=dict(help_text="Backup origin"))
    status: str = field(metadata=dict(help_text="Backup status", enum=BackupStatuses.values()))
    failure_reason: str = field(metadata=dict(help_text="Backup failure reason"))
    failure_description: str = field(metadata=dict(help_text="Backup failure description"))
    previous_backup: str = field(metadata=dict(help_text="Previous backup ID"))
    data_size: int = field(metadata=dict(help_text="Backup data size"))
    delta_size: int = field(metadata=dict(help_text="Backup delta size"))
    workflow: str = field(metadata=dict(help_text="Backup workflow"))
    account_id: int = field(metadata=dict(help_text="Account ID"))
    account_name: str = field(metadata=dict(help_text="Account name"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace ID"))
    cloudspace_name: str = field(metadata=dict(help_text="Cloudspace name"))


@dataclass
class BackupStruct(BackupBaseStruct):
    vm_id: int = field(metadata=dict(help_text="VM ID"))
    vm_name: str = field(metadata=dict(help_text="VM name"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))


@dataclass
class BackupWithVMStruct(BackupBaseStruct):
    vm: dict = field(metadata=dict(help_text="VM info"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))


@dataclass
class IdModelStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="ID"))


@dataclass
class CreateVmFromBackupStruct(BaseStruct):
    target_id: int = field(metadata=dict(help_text="Target ID"))
    backup_id: str = field(metadata=dict(help_text="Backup ID"))
    name: str = field(metadata=dict(help_text="VM name"))
    description: str = field(metadata=dict(help_text="VM description"))
    hostname: str = field(metadata=dict(help_text="VM hostname"))
    private_ip: str = field(metadata=dict(help_text="VM private IP"))
    tpm_secret: str = field(metadata=dict(help_text="VM TPM password"))


@dataclass
class BackupAccessSettings:
    vco_id: str
    cloud_enabler_id: str
    ce_editable_policies: bool
    vco_editable_policies: bool
    ce_editable_targets: bool
    vco_editable_targets: bool


@dataclass
class RestoreDiskStruct(BaseStruct):
    id: int = field(metadata=dict(help_text="Unique identifier for the disk"))
    name: str = field(metadata=dict(help_text="Name of the disk"))
    type: str = field(metadata=dict(help_text="Type of the disk"))
    size: int = field(metadata=dict(help_text="Size of the disk"))
    status: str = field(metadata=dict(help_text="Current status of the disk"))
    progress: int = field(metadata=dict(help_text="restoration percentage"))


@dataclass
class RestoreVMStruct(BaseStruct):
    vm_id: int = field(metadata=dict(help_text="VM ID"))
    disks: List[RestoreDiskStruct] = field(metadata=dict(help_text="vm disks"))
    starttime: int = field(metadata=dict(help_text="Backup start time"))
    status: str = field(metadata=dict(help_text="Backup status"))


@dataclass
class RestoreVmResponse(BaseStruct):
    vm_id: int = field(metadata=dict(help_text="VM ID"))
    backup_id: str = field(metadata=dict(help_text="Backup ID from which vm was restored"))
    task_id: str = field(metadata=dict(help_text="Task ID"))


@dataclass
class PciAddressStruct(BaseStruct):
    domain: int = field(metadata=dict(help_text="PCI domain number"))
    bus: int = field(metadata=dict(help_text="PCI bus number"))
    slot: int = field(metadata=dict(help_text="PCI slot number"))
    function: int = field(metadata=dict(help_text="PCI function number"))
    guid: str = field(metadata=dict(help_text="PCI device GUID"))


@dataclass
class NetworkInterfaceStruct(BaseStruct):
    mac_address: str = field(metadata=dict(help_text="MAC address"))
    ip_address: str = field(metadata=dict(help_text="IP address"))
    model: str = field(metadata=dict(help_text="Network interface model"))
    pci_address: PciAddressStruct = field(metadata=dict(help_text="PCI address info"))
    type: str = field(metadata=dict(help_text="Interface type (e.g., bridge, virtio)"))


@dataclass
class DiskStruct(BaseStruct):
    id: int = field(metadata=dict(help_text="Disk ID"))
    size: int = field(metadata=dict(help_text="Disk size in GB"))
    type: str = field(metadata=dict(help_text="Disk type (e.g., qcow2, raw)"))
    reference_id: str = field(metadata=dict(help_text="Disk reference ID"))
    host: str = field(metadata=dict(help_text="Host where disk is stored"))
    port: int = field(metadata=dict(help_text="Host port"))
    backup_size: int = field(metadata=dict(help_text="Size of full backup in bytes"))
    delta_size: int = field(metadata=dict(help_text="Size of delta backup in bytes"))
    restic_snapshot_id: str = field(metadata=dict(help_text="Restic snapshot ID"))
    restic_snapshot_path: str = field(metadata=dict(help_text="Path to restic snapshot"))
    snapshot_guid: str = field(metadata=dict(help_text="Snapshot GUID"))
    clone_disk_guid: str = field(metadata=dict(help_text="GUID of the cloned disk"))
    backup_blocksize: int = field(metadata=dict(help_text="Backup block size in bytes"))


@dataclass
class ImageStruct(BaseStruct):
    id: int = field(metadata=dict(help_text="Image ID"))
    os: str = field(metadata=dict(help_text="Operating system"))
    os_type: str = field(metadata=dict(help_text="Type of OS (e.g., linux, windows)"))
    boot_type: str = field(metadata=dict(help_text="Boot type (e.g., UEFI, legacy)"))


@dataclass
class VmFromBackupStruct(BaseStruct):
    id: int = field(metadata=dict(help_text="VM ID"))
    reference_id: str = field(metadata=dict(help_text="VM reference ID"))
    name: str = field(metadata=dict(help_text="VM name"))
    description: str = field(metadata=dict(help_text="VM description"))
    hostname: str = field(metadata=dict(help_text="VM hostname"))
    memory: int = field(metadata=dict(help_text="Memory size in MB"))
    vcpus: int = field(metadata=dict(help_text="Number of virtual CPUs"))
    boot_disk_id: int = field(metadata=dict(help_text="Boot disk ID"))
    disks: List[DiskStruct] = field(metadata=dict(help_text="List of VM disks"))
    network_interfaces: List[NetworkInterfaceStruct] = field(metadata=dict(help_text="List of network interfaces"))
    image: ImageStruct = field(metadata=dict(help_text="Image details"))
    metadata: Dict = field(metadata=dict(help_text="Custom VM metadata"))
    userdata: Dict = field(metadata=dict(help_text="Userdata for cloud-init or config injection"))
    vtpm_restic_snapshot_id: str = field(metadata=dict(help_text="Restic snapshot ID for vTPM"))


@dataclass
class BackupMetadataStruct(BaseStruct):
    data: Dict = field(default_factory=dict, metadata=dict(help_text="Backup metadata"))


@dataclass
class BackupFullStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="Backup ID"))
    policy: Optional[str] = field(metadata=dict(help_text="Policy ID"))
    target: int = field(metadata=dict(help_text="Backup target ID"))
    vm_id: int = field(metadata=dict(help_text="Virtual Machine ID"))
    vm_name: str = field(metadata=dict(help_text="Virtual Machine name"))
    policy_name: str = field(metadata=dict(help_text="Policy name"))
    location: str = field(metadata=dict(help_text="Backup location"))
    start_time: int = field(metadata=dict(help_text="Backup start timestamp"))
    end_time: int = field(metadata=dict(help_text="Backup end timestamp"))
    snapshot_time: int = field(metadata=dict(help_text="Snapshot start time"))
    snapshot_end_time: int = field(metadata=dict(help_text="Snapshot end time"))
    creation_timestamp: int = field(metadata=dict(help_text="creation timestamp"))
    origin: str = field(metadata=dict(help_text="Backup origin"))
    status: str = field(metadata=dict(help_text="Backup status"))
    snapshot_status: str = field(metadata=dict(help_text="Snapshot status"))
    failure_reason: str = field(metadata=dict(help_text="Failure reason"))
    failure_description: str = field(metadata=dict(help_text="Failure description"))
    previous_backup: str = field(metadata=dict(help_text="Previous backup ID"))
    data_size: int = field(metadata=dict(help_text="Data size in bytes"))
    delta_size: int = field(metadata=dict(help_text="Delta size in bytes"))
    workflow: str = field(metadata=dict(help_text="Associated workflow ID"))
    account_id: int = field(metadata=dict(help_text="Account ID"))
    account_name: str = field(metadata=dict(help_text="Account name"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace ID"))
    cloudspace_name: str = field(metadata=dict(help_text="Cloudspace name"))
    metadata: BackupMetadataStruct = field(metadata=dict(help_text="Backup metadata"))
    warnings: List[str] = field(default_factory=list, metadata=dict(help_text="Backup warnings"))
