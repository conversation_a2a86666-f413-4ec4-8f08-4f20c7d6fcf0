# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REP<PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import base64
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union

from meneja.lib.enumeration import (
    CheckMessageLevel,
    ManagementClusterType,
    NodePoolRole,
    NodeProvisioningStatus,
    SSLCertificateSource,
)
from meneja.structs.dataclasses import BaseStruct
from meneja.structs.vco.dataclasses.ingress import CloudspaceIngressConfigStruct
from meneja.structs.vco.dataclasses.resources_dns_record import CustomerResourceDnsRecordStruct


@dataclass
class RancherBridgeRequest(BaseStruct):
    id: str = field(metadata=dict(help_text="Request ID"))
    method: str = field(metadata=dict(help_text="Http method"))
    token_id: str = field(metadata=dict(help_text="Rancher authentication token id"))
    token: str = field(metadata=dict(help_text="Rancher authentication token"))
    path: str = field(metadata=dict(help_text="Request path"))
    body: str = field(metadata=dict(help_text="Base64 encoded body"))
    timestamp: int = field(metadata=dict(help_text="Epoch when the request was created"))


@dataclass
class RancherBridgeResponse(BaseStruct):
    id: str = field(metadata=dict(help_text="Request ID"))
    exception: str = field(metadata=dict(help_text="Exception message"))
    status: int = field(metadata=dict(help_text="Http response status"))
    content: str = field(metadata=dict(help_text="Base64 encoded content"))
    timestamp: int = field(metadata=dict(help_text="Epoch when the response was created"))

    @property
    def text(self):
        """Return content of the response, in unicode"""
        return base64.b64decode(self.content).decode()

    @property
    def status_code(self):
        """Return status code"""
        return self.status


@dataclass
class RancherVersionStruct(BaseStruct):
    tag: str = field(metadata=dict(help_text="Rancher version"))
    supported_kubernetes_versions: List[str] = field(metadata=dict(help_text="Supported kubernetes versions"))


@dataclass
class RancherManagementClusterCreationStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Management cluster name"))
    domain_name: str = field(metadata=dict(help_text="Management cluster domain name "))
    cloudspace_id: str = field(metadata=dict(help_text="Cloud space id to be deployed on"))
    external_network_ip: str = field(metadata=dict(help_text="External network IP to be used by the cluster"))
    cluster_type: ManagementClusterType = field(metadata=dict(help_text="Type of management cluster"))
    ssl_certificate_source: SSLCertificateSource = field(
        metadata=dict(help_text="The source where the SSL certificate should be obtained")
    )
    tag: Optional[str] = field(metadata=dict(help_text="Rancher version tag"))
    letsencrypt_email: Optional[str] = field(metadata=dict(help_text="Letsencrypt configuration address"))
    physical_storage: Optional[bool] = field(
        metadata=dict(help_text="Use physical storage disks for the nodes", default=False)
    )


@dataclass
class SimpleVirtualMachineStruct(BaseStruct):
    vm_id: int = field(metadata=dict(help_text="Vm Id"))


@dataclass
class CheckMessageStruct(BaseStruct):
    message: str = field(metadata=dict(help_text="Check message"))
    timestamp: int = field(metadata=dict(help_text="Epoch timestamp when the message was logged"))
    level: CheckMessageLevel = field(metadata=dict(help_text="Message severity"))


@dataclass
class CheckResultStruct(BaseStruct):
    messages: List[CheckMessageStruct] = field(metadata=dict(help_text="Log messages produced by the health check"))
    success: bool = field(metadata=dict(help_text="Last run status"))


@dataclass
class HealthCheckStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Health check name"))
    schedule: str = field(metadata=dict(help_text="Cron formatted schedule"))
    status: CheckResultStruct = field(metadata=dict(help_text="Last run status"))
    next_run: int = field(metadata=dict(help_text="Next run epoch timestamp"))


@dataclass
class RancherManagementClusterNodeStatusStruct(BaseStruct):
    health_checks: List[HealthCheckStruct] = field(metadata=dict(help_text="Health checks"))
    timestamp: Optional[int] = field(metadata=dict(help_text="Epoch timestamp when the node status was last updated"))


@dataclass
class VirtualMachineStruct(BaseStruct):
    vm_id: int = field(metadata=dict(help_text="Vm Id"))
    status: RancherManagementClusterNodeStatusStruct = field(metadata=dict(help_text="Health check statusses"))


@dataclass
class BaseNodeStruct(BaseStruct):
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace id to host node pool"))


@dataclass
class NodeCreationStruct(BaseNodeStruct):
    node_name: str = field(metadata=dict(help_text="Node name"))
    vcpus: int = field(metadata=dict(help_text="Number of cpu to assign to machine"))
    memory: int = field(metadata=dict(help_text="Amount of memory to assign to machine in MiB"))
    user_data: Dict = field(metadata=dict(help_text="Object containing user data"))
    pin_cpus: bool = field(metadata=dict(help_text="Flag to detect whether to pin vcpus or not"))
    physical_storage: Optional[bool] = field(metadata=dict(help_text="Use physical storage", default=False))
    node_pool_identifier: Optional[str] = field(metadata=dict(help_text="Node pool identifier"))
    master_node: Optional[bool] = field(metadata=dict(help_text="Use physical storage", default=False))


@dataclass
class SimpleKubernetesClusterNodeStruct(BaseNodeStruct):
    vm_id: int = field(metadata=dict(help_text="Vm Id"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace id containing this node"))
    name: str = field(metadata=dict(help_text="Node/VM name"))


@dataclass
class RancherManagementClusterStruct(RancherManagementClusterCreationStruct):
    management_cluster_nodes: List[VirtualMachineStruct] = field(metadata=dict(help_text="List of virtual machines"))
    management_cluster_id: str = field(metadata=dict(help_text="Management cluster id "))
    status: str = field(metadata=dict(help_text="Current status of the management cluster"))
    rancher_version: RancherVersionStruct = field(metadata=dict(help_text="Current statue"))


@dataclass
class SimpleRancherManagementClusterStruct(RancherManagementClusterCreationStruct):
    management_cluster_id: str = field(metadata=dict(help_text="Management cluster id "))
    status: str = field(metadata=dict(help_text="Current status of the management cluster"))


@dataclass
class RancherManagementClusterCreationSuccessStruct(BaseStruct):
    management_cluster_id: str = field(metadata=dict(help_text="Cluster Id"))


@dataclass
class VirtualMachineTemplateStruct(BaseStruct):
    vcpus: int = field(metadata=dict(help_text="Number of cpu to assign to machine"))
    memory: int = field(metadata=dict(help_text="Amount of memory to assign to machine in MiB"))
    disk_size: int = field(metadata=dict(help_text="Boot Disk Size in GiB", default=20))
    data_disks: List[int] = field(metadata=dict(help_text="List of data disks", default=[50]))


@dataclass
class KubeConfigStruct(BaseStruct):
    result: str = field(metadata=dict(help_text="Resulting kube config file"))


@dataclass
class RancherAsAServiceSettingsStruct(BaseStruct):
    default_version_tag: str = field(metadata=dict(help_text="Production Rancher image tag"))
    mgmt_cluster_vcpus: int = field(metadata=dict(help_text="Management cluster default VM VCPUs", default=6))
    mgmt_cluster_memory: int = field(metadata=dict(help_text="Management cluster default VM Memory", default=12288))
    mgmt_cluster_boot_disk_size: int = field(
        metadata=dict(help_text="Management cluster default VM boot disk size", default=25)
    )
    mgmt_cluster_data_disk_size: int = field(
        metadata=dict(help_text="Management cluster default VM data disk size", default=30)
    )
    minimum_kubernetes_version: str = field(metadata=dict(help_text="Production Rancher image tag", default="1.22"))


@dataclass
class RancherManagementClusterNodeProvisioningStatusStruct(BaseStruct):
    status: NodeProvisioningStatus = field(metadata=dict(help_text="Node provisioning status"))
    vm_id: int = field(metadata=dict(help_text="Virtual machine id"))


@dataclass
class RancherManagementClusterKubernetesClusterStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="Rancher management kubernetes cluster id"))
    name: str = field(metadata=dict(help_text="Kubernetes cluster name"))
    status: str = field(metadata=dict(help_text="Kubernetes cluster status"))
    node_count: str = field(metadata=dict(help_text="Kubernetes cluster node count"))
    provider: str = field(metadata=dict(help_text="Kubernetes provider"))
    version: str = field(metadata=dict(help_text="Kubernetes cluster version"))


@dataclass
class RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct(BaseStruct):
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace ID for node pool"))
    name: str = field(metadata=dict(help_text="Node pool name"))
    cpus: int = field(metadata=dict(help_text="Number of cpu's in each node in the node pool"))
    pin_cpus: bool = field(metadata=dict(help_text="Exclusively pin's the cpu's on host threads."))
    memory: int = field(metadata=dict(help_text="Amount of memory for each vm in the node pool (MiB)"))
    master: bool = field(metadata=dict(help_text="Flag wether this is a master or worker pool"))
    node_count: int = field(metadata=dict(help_text="Initial amount of nodes"))
    physical_storage: Optional[bool] = field(metadata=dict(help_text="Use physical storage (Only for master nodes)"))


@dataclass
class RancherSupportedKubernetesClusterVersionStruct(BaseStruct):
    version: str = field(metadata=dict(help_text="Kubernetes version"))


@dataclass
class CreateRancherManagementClusterKubernetesClusterStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Kubernetes cluster name"))
    node_pools: List[RancherManagementClusterKubernetesClusterNodePoolDefinitionStruct] = field(
        metadata=dict(help_text="Node pools")
    )
    version: str = field(metadata=dict(help_text="Kubernetes version"))


@dataclass
class RancherManagementClusterKubernetesClusterNodePoolRoleStruct(BaseStruct):
    role: NodePoolRole = field(metadata=dict(help_text="Node pool role"))


@dataclass
class RancherManagementClusterKubernetesClusterNodePoolStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="Node pool id"))
    driver: str = field(metadata=dict(help_text="Node driver name"))
    name: str = field(metadata=dict(help_text="Node pool name"))
    status: str = field(metadata=dict(help_text="Node pool status"))
    node_count: int = field(metadata=dict(help_text="Node pool node count"))
    hostname_prefix: str = field(metadata=dict(help_text="Host name prefix used when creating nodes in the cluster"))
    roles: List[RancherManagementClusterKubernetesClusterNodePoolRoleStruct] = field(
        metadata=dict(help_text="Node pool roles")
    )
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace id where node pool is running"))
    loadbalancer_ip: Optional[str] = field(metadata=dict(help_text="Loadbalancer public IP"), default=None)
    ingress_ctrl_status: Optional[str] = field(metadata=dict(help_text="ingress controller status"), default=None)
    ingress_class: Optional[str] = field(metadata=dict(help_text="Loadbalancer public IP"), default=None)
    ssl_issuer: Optional[str] = field(metadata=dict(help_text="SSL issuer name"), default=None)


@dataclass
class RancherManagementClusterKubernetesClusterNodePoolNodeStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="Node id"))
    name: str = field(metadata=dict(help_text="Node name"))
    phase: str = field(metadata=dict(help_text="Node Phase"))
    vm_id: Optional[int] = field(metadata=dict(help_text="Virtual machine id"))
    vm_name: Optional[str] = field(metadata=dict(help_text="Virtual machine name"))
    cloudspace_id: Optional[str] = field(metadata=dict(help_text="Cloudspace id"))
    cloudspace_name: Optional[str] = field(metadata=dict(help_text="Cloudspace id"))


@dataclass
class RancherManagementClusterNodeStruct(BaseStruct):
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace id"))
    cloudspace_name: str = field(metadata=dict(help_text="Cloudspace name"))
    vm_id: str = field(metadata=dict(help_text="Virtual machine id"))
    vm_name: str = field(metadata=dict(help_text="Virtual machine name"))
    health: Optional[RancherManagementClusterNodeStatusStruct] = field(metadata=dict(help_text="Health check statuses"))
    cluster_id: Optional[str] = field(metadata=dict(help_text="Kubernetes cluster id"))
    cluster_name: Optional[str] = field(metadata=dict(help_text="Kubernetes cluster name"))
    node_name: Optional[str] = field(metadata=dict(help_text="Kubernetes node name"))


@dataclass
class SimpleKubernetesClusterNodeListStruct(BaseStruct):
    nodes: List[SimpleKubernetesClusterNodeStruct] = field(metadata=dict(help_text="Cloudspace id"))


@dataclass
class VirtualMachineInfoStruct(BaseStruct):
    vm_id: str = field(metadata=dict(help_text="Virtual machine id"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace id"))


@dataclass
class UpdateClusterJwtResult(BaseStruct):
    failed_vms: List[VirtualMachineInfoStruct] = field(metadata=dict(help_text="Failed vms list"))
    succeeded_vms: List[VirtualMachineInfoStruct] = field(metadata=dict(help_text="Succeeded vms list"))


@dataclass
class LoadBalancerServiceStruct(BaseStruct):
    service_uid: str = field(metadata=dict(help_text="UID of k8s LB Service"))
    node_pool_ids: List[str] = field(metadata=dict(help_text="NodePool Ids"))
    cloudspace_id: str = field(metadata=dict(help_text="Primary cloudspace that holds the ingress resources"))
    external_network_id: Union[str, int] = field(
        metadata=dict(help_text="ExternalNetworkID Associated with the service")
    )
    external_network_ip: str = field(metadata=dict(help_text="ExternalNetworkIP Associated with the service"))
    serverpool_id: str = field(metadata=dict(help_text="ServerPoolID Associated with the service"))
    loadbalancer_ids: List[str] = field(metadata=dict(help_text="LoadBalancerIDs Associated with the service"))
    ingress_config: Optional[CloudspaceIngressConfigStruct] = field(
        metadata=dict(help_text="Ingress Resources to be Installed")
    )


@dataclass
class NodeDriverInfoStruct(BaseStruct):
    url: str = field(metadata=dict(help_text="Node driver url"))
    version: str = field(metadata=dict(help_text="Node driver latest version"))
    checksum: str = field(metadata=dict(help_text="checksum"))
    name: str = field(metadata=dict(help_text="Node name"))


@dataclass
class VirtualMachineSimpleInfoStruct(BaseStruct):
    vm_name: str = field(metadata=dict(help_text="Virtual machine name"))
    cs_id: str = field(metadata=dict(help_text="Cloudspace ID"))
    vm_id: int = field(metadata=dict(help_text="Virtual machine ID"))


@dataclass
class ClusterConnectedResources(BaseStruct):
    vms: List[VirtualMachineSimpleInfoStruct] = field(metadata=dict(help_text="List of Virtual machine name"))
    ingress_resources: List[LoadBalancerServiceStruct] = field(metadata=dict(help_text="List of ingress resources"))
    dns_records: List[CustomerResourceDnsRecordStruct] = field(metadata=dict(help_text="List of dns records"))


@dataclass
class CCMError(BaseStruct):
    error_message: str = field(metadata=dict(help_text="CCM Error Message"))


@dataclass
class RancherFailedJobStruct(BaseStruct):
    id: str = field(metadata=dict(help_text="ID"))
    title: str = field(metadata=dict(help_text="Title"))
    timestamp: int = field(metadata=dict(help_text="Time when job failed"))
    workflow_id: str = field(metadata=dict(help_text="Workflow id"))


@dataclass
class RancherVersionsGetStruct(BaseStruct):
    tag: str = field(metadata=dict(help_text="Rancher version"))
    supported_kubernetes_versions: List[str] = field(metadata=dict(help_text="Supported kubernetes versions"))
    default: bool = field(metadata=dict(help_text="Version is default", defualt=False))
