# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import math
import os
import socket
from typing import Sequence
from urllib.parse import urlparse

import redis
from dynaqueue.client import Client
from gevent import sleep
from gevent.lock import RLock
from minio import Minio
from minio.datatypes import Part
from mongoengine import connect
from pymongo import MongoClient
from redis.sentinel import Sentinel

from meneja.lib.enumeration import EnvironmentName


class Connection:
    """Connection management"""

    _instance = None
    _lock = RLock()

    def __init__(self):
        cls = type(self)
        if not cls._lock._is_owned():
            raise RuntimeError("Use get_instance to work with this class.")

    @classmethod
    def get_instance(cls) -> "Connection":
        """
        Get the singleton object reference
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @classmethod
    def get_client(cls):
        """
        Gets a client object reference
        """
        return cls.get_instance()._get_client()  # pylint: disable=protected-access


class DynaqueueConnection(Connection):
    """
    Singleton object holding an active dynaqueue client connection
    """

    def __init__(self):
        super().__init__()
        self._dynaqueue_client: Client = None

    @property
    def redis_kombu_connection_string(self):
        """Redis connection string"""
        return RedisConnection.get_instance().redis_kombu_connection_string

    @property
    def redis_kombu_transport_options(self):
        """Redis transport options"""
        return RedisConnection.get_instance().redis_kombu_transport_options

    def _get_client(self) -> Client:
        if self._dynaqueue_client is not None:
            return self._dynaqueue_client
        with self._lock:
            if self._dynaqueue_client is None:

                def redis_connector():
                    return RedisConnection.get_client()

                self._dynaqueue_client = Client(redis_connector)
        return self._dynaqueue_client


# Patch on Redis to solve File descriptor was closed in another greenlet
class MenejaRedis(redis.Redis):
    """Meneja redis client"""

    def execute_command(self, *args, **options):
        "Execute a command and return a parsed response"
        pool = self.connection_pool
        command_name = args[0]
        error = None
        connection = pool.get_connection(command_name, **options)
        try:
            for backoff in range(10):
                try:
                    connection.send_command(*args)
                    return self.parse_response(connection, command_name, **options)
                except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError) as e:
                    error = e
                    connection.disconnect()
                    if (
                        isinstance(e, redis.exceptions.ConnectionError)
                        or connection.retry_on_timeout
                        and isinstance(e, redis.exceptions.TimeoutError)
                    ):
                        sleep(backoff)
                        continue
                    raise
        finally:
            pool.release(connection)
        raise error


class RedisConnection(Connection):
    """
    Singleton object holding an active redis connection
    """

    def __init__(self):
        super().__init__()
        self._redis_client: MenejaRedis = None
        self._redis_kombu_connection_string = None
        self._redis_kombu_transport_options = None

    @property
    def redis_kombu_connection_string(self):
        """Redis connection string"""
        if self._redis_kombu_connection_string is None:
            self.get_client()
        return self._redis_kombu_connection_string

    @property
    def redis_kombu_transport_options(self):
        """Redis transport options"""
        self.redis_kombu_connection_string  # pylint: disable=pointless-statement
        return self._redis_kombu_transport_options

    def _get_client(self) -> MenejaRedis:
        if self._redis_client is not None:
            return self._redis_client
        with self._lock:
            if self._redis_client is None:
                addr, port = os.environ.get("MNJ_DYNAQUEUE_REDIS", "localhost:6379").split(":")
                redis_sentinel_cluster = os.environ.get("MNJ_DYNAQUEUE_REDIS_SENTINEL_CLUSTER_NAME")
                password = os.environ.get("MNJ_DYNAQUEUE_REDIS_PASSWORD")
                try:
                    ips: Sequence[str] = socket.gethostbyname_ex(addr)[-1]
                except socket.gaierror as exc:
                    raise RuntimeError("Dynaqueue redis service discovery failed") from exc
                if redis_sentinel_cluster is not None:
                    sentinel = Sentinel(list((val, port) for val in ips), password=password)
                    self._redis_client = sentinel.master_for(redis_sentinel_cluster, redis_class=MenejaRedis)
                    self._redis_kombu_transport_options = dict(master_name=redis_sentinel_cluster)
                    if password is None:
                        kombu = "sentinel://{}:{}"
                        self._redis_kombu_connection_string = ";".join(
                            kombu.format(ip, port, redis_sentinel_cluster) for ip in ips
                        )
                    else:
                        kombu = "sentinel://:{}@{}:{}"
                        self._redis_kombu_connection_string = ";".join(
                            kombu.format(password, ip, port, redis_sentinel_cluster) for ip in ips
                        )
                else:
                    if password is None:
                        self._redis_kombu_connection_string = f"redis://{ips[0]}:{port}"
                    else:
                        self._redis_kombu_connection_string = f"redis://:{password}@{ips[0]}:{port}"
                    self._redis_client = redis.Redis(host=ips[0], port=port, db=0, password=password)
            return self._redis_client


class MongoConnection(Connection):
    """
    Singleton object holding an active mongoclient connection
    """

    def __init__(self):
        super().__init__()
        self.dbname = os.environ.get("MNJ_DATABASE")
        self._mongo_client: MongoClient = None
        self._iam_mongo_client: MongoClient = None

    def _get_client(self) -> MongoClient:
        if self._mongo_client is not None:
            return self._mongo_client
        with self._lock:
            if self._mongo_client is None:
                connection_string = self.get_connection_string(self.dbname)
                connect(host=connection_string)
                self._mongo_client = MongoClient(connection_string)
        return self._mongo_client

    def _get_iam_client(self):
        if self._iam_mongo_client is not None:
            return self._iam_mongo_client
        with self._lock:
            if self._iam_mongo_client is None:
                connection_string = self._get_iam_connection_string()
                connect(host=connection_string)
                self._iam_mongo_client = MongoClient(connection_string)
        return self._iam_mongo_client

    @classmethod
    def get_iam_client(cls):
        """
        Gets a client object reference
        """
        return cls.get_instance()._get_iam_client()  # pylint: disable=protected-access

    @classmethod
    def get_connection_string(cls, dbname: str = "") -> str:
        """
        Gets the connection string for the mongo database

        :param dbname: optional databasename
        :type dbname: str

        :return: The connection string for the mongo database
        :rtype: str
        """
        return cls.get_instance()._get_connection_string(dbname)  # pylint: disable=protected-access

    @classmethod
    def _get_connection_string(cls, dbname: str = "") -> str:
        """Get connection string for the mongo database

        Args:
            dbname (str, optional): Databse name. Defaults to "".

        Returns:
            str: Connection string for the mongo database
        """
        addr, port = os.environ.get("MNJ_MONGODB", "localhost:27017").split(":")
        replica_set = os.environ.get("MNJ_MONGORS")
        if replica_set:
            try:
                ips = socket.gethostbyname_ex(addr)[-1]
            except socket.gaierror as exc:
                raise RuntimeError("Mongo service discovery failed") from exc
            rs_addrs = ",".join([f"{ip}:{port}" for ip in ips])
            addr = f"mongodb://{rs_addrs}/{dbname}?replicaSet={replica_set}"
        else:
            addr = f"mongodb://{addr}/{dbname}"
        return addr

    @classmethod
    def _get_iam_connection_string(cls, dbname: str = "") -> str:
        if EnvironmentName.current() in (EnvironmentName.DEV, EnvironmentName.TEST, EnvironmentName.LAB):
            addr, port = os.environ.get("MNJ_MONGODB", "localhost:27017").split(":")
        else:
            addr, port = os.environ.get("IAM_MONGODB", "mongo.iam:27017").split(":")
        replica_set = os.environ.get("MNJ_MONGORS")
        if replica_set:
            try:
                ips = socket.gethostbyname_ex(addr)[-1]
            except socket.gaierror as exc:
                raise RuntimeError("Mongo service discovery failed") from exc
            rs_addrs = ",".join([f"{ip}:{port}" for ip in ips])
            addr = f"mongodb://{rs_addrs}/{dbname}?replicaSet={replica_set}"
        else:
            addr = f"mongodb://{addr}/{dbname}"
        return addr


class MenejaMinio(Minio):
    """Meneja MinIO client"""

    MAX_PART_SIZE = 1024 * 1024 * 125

    def _generate_presigned_urls_for_multipart_upload(
        self, bucket_name: str, object_name: str, upload_id: str, parts_count: int
    ) -> list:
        """generates presigned upload urls for each part of the file

        Args:
            bucket_name (str)
            object_name (str)
            upload_id (str)
            parts_count (int)

        Returns:
            list: list of parts urls
        """
        parts_data = []
        for part_number in range(1, parts_count + 1):
            # Generate a presigned URL for this part.
            part_url = self.get_presigned_url(
                "PUT",
                bucket_name,
                object_name,
                response_headers={"Content-Length": str(self.MAX_PART_SIZE)},
                extra_query_params={"partNumber": str(part_number), "uploadId": upload_id},
            )
            parts_data.append({"part_number": part_number, "upload_url": part_url})
        return parts_data

    def start_multipart_upload(self, bucket_name: str, object_name: str, file_size: int) -> tuple:
        """starts multipart upload

        Args:
            bucket_name (str)
            object_name (str)
            file_size (int)

        Returns:
            tuple: upload_id and parts upload urls
        """
        parts_count = int(math.ceil(file_size / self.MAX_PART_SIZE))
        upload_id = self._create_multipart_upload(bucket_name, object_name, {})
        parts_upload_data = self._generate_presigned_urls_for_multipart_upload(
            bucket_name, object_name, upload_id, parts_count
        )
        return upload_id, parts_upload_data

    def complete_multipart_upload(self, bucket_name: str, object_name: str, upload_id: str, parts: list):
        """completes the multipart upload after all parts has been uploaded

        Args:
            bucket_name (str)
            object_name (str)
            upload_id (str)
            parts (list): list of parts with part_number and etag
        """
        parts = [Part(part["part_number"], part["etag"]) for part in parts]
        return self._complete_multipart_upload(bucket_name, object_name, upload_id, parts)

    def create_multipart_upload(self, bucket_name, object_name):
        """Initiate multipart upload

        Args:
            bucket_name (str)
            object_name (str)

        Returns:
            str: upload_id
        """
        return self._create_multipart_upload(bucket_name, object_name, {})

    def upload_part(self, bucket_name, object_name, data, headers, upload_id, part_number):
        """upload part

        Args:
            bucket_name (str)
            object_name (str)
            data (bytes)
            headers (dict)
            upload_id (str)
            part_number (number)

        Returns:
            str: etag
        """
        return self._upload_part(bucket_name, object_name, data, headers, upload_id, part_number)

    def list_multipart_uploads(self, bucket_name: str):
        """List multipart uploads

        Args:
            bucket_name (str)
        """
        return self._list_multipart_uploads(bucket_name)

    def abort_multipart_upload(self, bucket_name: str, object_name: str, upload_id: str):
        """Abort multipart upload

        Args:
            bucket_name (str)
            object_name (str)
            upload_id (str)
        """
        return self._abort_multipart_upload(bucket_name, object_name, upload_id)


class MinioConnectionBase(Connection):
    """
    Singleton object holding an active minio client connection
    """

    URL: str = ""

    def __init__(self, **kwargs):
        super().__init__()
        self._minio_client: Minio = None
        self._minio_client: Minio = None
        self.key = kwargs.get("key", None)
        self.secret = kwargs.get("secret", None)

    def _get_client(self) -> Minio:
        if self._minio_client is not None:
            return self._minio_client
        with self._lock:
            if self._minio_client is None:
                endpoint = urlparse(self.URL)
                secure = endpoint.scheme == "https"
                key = self.key or os.environ.get("MINIO_ACCESS_KEY")
                secret = self.secret or os.environ.get("MINIO_ACCESS_SECRET")
                self._minio_client = MenejaMinio(endpoint.netloc, key, secret, secure=secure)
        return self._minio_client


class MinioConnection(MinioConnectionBase):
    """Internal MinIO connection class"""

    URL = os.environ.get("MINIO_URL")


class ExternalMinioConnection(MinioConnectionBase):
    """External MinIO connection class"""

    URL = os.environ.get("EXTERN_MINIO_URL")


class MinioQaDevConnection(MinioConnectionBase):
    """Dev/QAS MinIO connection class"""

    URL = os.environ.get("MINIO_READ_ONLY_URL")

    def __init__(self):
        super().__init__(
            key=os.environ.get("MINIO_READ_ONLY_KEY"), url=self.URL, secret=os.environ.get("MINIO_READ_ONLY_SECRET")
        )


class BackupMinioConnection(MinioConnectionBase):
    """Backup MinIO connection class"""

    URL = os.environ.get("BACKUP_MINIO_URL")

    def __init__(self):
        super().__init__(
            key=os.environ.get("BACKUP_MINIO_ACCESS_KEY"),
            url=self.URL,
            secret=os.environ.get("BACKUP_MINIO_ACCESS_SECRET"),
        )
