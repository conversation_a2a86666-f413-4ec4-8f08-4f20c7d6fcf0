# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, D<PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import time
from datetime import datetime, timezone
from typing import List, Optional

from dateutil.parser import parse

from kubernetes import client as k8c  # pylint: disable=no-name-in-module
from kubernetes import config  # pylint: disable=no-name-in-module
from meneja.structs.meneja.dataclasses.k8s_certs import K8sCertValidity


def _config(in_cluster):
    if in_cluster:
        config.load_incluster_config()
    else:
        config.load_kube_config()  # should be used when testing locally with KUBECONFIG set


def deploy_vco(
    iam_name: str,
    iam_replicas: int,
    iam_args: List[str],
    iam_ingress_host: str,
    iam_ingress_name: str,
    iam_root_organization: str,
    iam_client_secret: str,
    meneja_support_email: str,
    meneja_ingress_name: str,
    meneja_ingress_host: str,
    meneja_replicas: int,
    vco_id: str,
    vco_utility_name: str,
    meneja_image_tag: str = None,
    meneja_ingress_path: str = "/",
    iam_ingress_path: str = "/",
    in_cluster: bool = True,
):
    """Create a custom iam object for VCO

    Args:
        iam_name (str): IAM name, used for naming iam & meneja deployments
        iam_replicas (int): Number of IAM deployment replicas
        iam_args (List[str]): Arguments for IAM deployments
        iam_ingress_host (str): Host (domain name) for ingress record for the IAM deployment
        iam_ingress_name (str): Name for ingress record for VCO IAM
        iam_root_organization (str): Name for IAM root org to be used by Meneja
        iam_client_secret (str): iam api secret key
        meneja_support_email (str): E-Mail for sending meneja updates
        meneja_ingress_name (str): Name for ingress record for VCO meneja
        vco_id (str): VCO ID
        vco_utility_name (str): VCO utility name
        meneja_ingress_host (str): Host (domain name) for ingress record
        meneja_ingress_path (str): Path to route traffic on meneja (default '/')
        iam_ingress_path (str): Path to route traffic on IAM (default '/')
        meneja_replicas (int): Number of Meneja deployment replicas
        meneja_image_tag (str): Meneja image tag from registry, optional
        in_cluster (bool): whether the code is running in the kubernetes cluster
    """
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    wild_card_cert = _get_wild_card_secret(domain=meneja_ingress_host, namespace="default")
    iam_wild_card_cert = _get_wild_card_secret(domain=iam_ingress_host, namespace="iam")
    body = dict(
        apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
        kind="IAM",
        metadata=dict(name=iam_name),
        spec=dict(
            iam_name=iam_name,
            iam_replicas=iam_replicas,
            iam_args=iam_args,
            iam_ingress_host=iam_ingress_host,
            iam_ingress_name=iam_ingress_name,
            meneja_support_email=meneja_support_email,
            meneja_ingress_name=meneja_ingress_name,
            meneja_ingress_host=meneja_ingress_host,
            meneja_replicas=meneja_replicas,
            meneja_image_tag=meneja_image_tag,
            meneja_ingress_path=meneja_ingress_path,
            vco_id=vco_id,
            vco_utility_name=vco_utility_name,
            iam_ingress_path=iam_ingress_path,
            iam_root_organization=iam_root_organization,
            iam_client_secret=iam_client_secret,
            wild_card_cert=wild_card_cert,
            iam_wild_card_cert=iam_wild_card_cert,
        ),
    )
    custom_objs_api.create_namespaced_custom_object(body=body, **custom_obj_args)


def patch_vco(iam_name: str, support_email: str = None, iam_support_email: str = None, in_cluster: bool = True):
    """Patch VCO (IAM) Customer K8s Resource

    Args:
        iam_name (str): Name of VCO (IAM) object
        support_email (str, optional): (vco) support email. Defaults to None.
        iam_support_email (str, optional): IAM support email. Defaults to None.
        in_cluster (bool): whether the code is running in the kubernetes cluster
    """
    if not (support_email or iam_support_email):
        return
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    body = dict(
        apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
        kind="IAM",
        metadata=dict(name=iam_name),
        spec={},
    )
    if support_email:
        body["spec"]["meneja_support_email"] = support_email
    if iam_support_email:
        previous = custom_objs_api.get_namespaced_custom_object(name=iam_name, **custom_obj_args)
        iam_args = previous["spec"]["iam_args"]
        for i, iam_arg in enumerate(iam_args):
            if iam_arg.startswith("--support-email="):
                iam_args[i] = f"--support-email={iam_support_email}"
                break
        body["spec"]["iam_args"] = iam_args

    custom_objs_api.patch_namespaced_custom_object(name=iam_name, body=body, **custom_obj_args)


def delete_deployed_vco(custom_object_name: str, in_cluster: bool = True):
    """Delete the deployed IAM custom resource

    Args:
        custom_object_name (str): object name to be deleted
        in_cluster (bool): whether the code is running in the kubernetes cluster
    """
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    try:
        custom_objs_api.delete_namespaced_custom_object(name=custom_object_name, **custom_obj_args)
    except k8c.rest.ApiException as exc:
        if exc.status == 404:  # VCO custom object does not exit, do nothing
            pass
        else:
            raise


def get_gva(g8_name: str, in_cluster: bool = True) -> Optional[dict]:
    """
    Get G8VCOAccess custom object

    Args:
        g8_name (str): G8 Name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.

    Returns:
        Optional[dict]: G8VCOAccess custom object or None if not found
    """
    _config(in_cluster)
    gva_name = f"{g8_name}-g8-vco-access"
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="g8-vco-accesses")
    custom_objs_api = k8c.CustomObjectsApi()
    try:
        return custom_objs_api.get_namespaced_custom_object(name=gva_name, **custom_obj_args)
    except k8c.rest.ApiException as exc:
        if exc.status == 404:
            return None
        raise


def gva_exists(g8_name: str, in_cluster: bool = True) -> bool:
    """
    Check if a G8VCOAccess custom object exists for the given G8 name.

    Args:
        g8_name (str): G8 Name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.

    Returns:
        bool: True if G8VCOAccess exists, False otherwise
    """
    _config(in_cluster)
    gva_name = f"{g8_name}-g8-vco-access"
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="g8-vco-accesses")
    custom_objs_api = k8c.CustomObjectsApi()
    try:
        custom_objs_api.get_namespaced_custom_object(name=gva_name, **custom_obj_args)
        return True
    except k8c.rest.ApiException as exc:
        if exc.status == 404:
            return False
        raise


def _get_or_create_gva(
    custom_objs_api: k8c.CustomObjectsApi, custom_obj_args: dict, g8_name: str, g8_domain: str, gva_name: str
):
    try:
        gva = custom_objs_api.get_namespaced_custom_object(name=gva_name, **custom_obj_args)
    except k8c.rest.ApiException as exc:
        if exc.status == 404:  # GVA does not exist create it
            gva = custom_objs_api.create_namespaced_custom_object(
                body=dict(
                    apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
                    kind="G8VcoAccess",
                    metadata=dict(name=gva_name, namespace=custom_obj_args["namespace"]),
                    spec=dict(g8_name=g8_name, g8_domain=g8_domain, vcos=[]),
                ),
                **custom_obj_args,
            )
        else:
            raise
    return gva


def _update_gva_vcos(g8_name: str, g8_domain: str, vco_id: str, add: bool, in_cluster: bool = True):
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="g8-vco-accesses")

    gva_name = f"{g8_name}-g8-vco-access"
    gva = _get_or_create_gva(custom_objs_api, custom_obj_args, g8_name, g8_domain, gva_name)

    vcos: set = set(gva["spec"]["vcos"])

    if (vco_id not in vcos and add) or (vco_id in vcos and not add):
        if add:
            vcos.add(vco_id)
        else:
            vcos.remove(vco_id)
        custom_objs_api.patch_namespaced_custom_object(
            name=gva_name,
            body=dict(
                metadata=dict(name=gva_name, namespace=custom_obj_args["namespace"]),
                spec=dict(g8_name=g8_name, vcos=list(vcos)),
            ),
            **custom_obj_args,
        )


def update_gva_ips(g8_name: str, ips: List[str], in_cluster: bool = True):
    """Set GVA IP Addresses used for reverse-proxying

    Args:
        g8_name (str): G8 Name
        ips (List[str]): IP Addresses list
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    k8c.CustomObjectsApi().patch_namespaced_custom_object(
        name=f"{g8_name}-g8-vco-access",
        body=dict(spec=dict(ips=ips)),
        group="gig.tech",
        version="v1",
        namespace="iam",
        plural="g8-vco-accesses",
    )


def add_vco_to_gva(g8_name: str, g8_domain: str, vco_id: str, in_cluster: bool = True):
    """Add VCO to GVA VCO list for G8

    Args:
        g8_name (str): G8 Name
        g8_domain (str): G8 Domain name
        vco_id (str): VCO ID
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _update_gva_vcos(g8_name, g8_domain, vco_id, True, in_cluster)


def remove_vco_from_gva(g8_name: str, g8_domain: str, vco_id: str, in_cluster: bool = True):
    """Remove VCO from GVA VCO list for G8

    Args:
        g8_name (str): G8 Name
        g8_domain (str): G8 Domain name
        vco_id (str): VCO ID
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _update_gva_vcos(g8_name, g8_domain, vco_id, False, in_cluster)


def delete_gva(g8_name: str, in_cluster: bool = True):
    """Delete G8VCOAccess object

    Args:
        g8_name (str): G8Name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="g8-vco-accesses")

    gva_name = f"{g8_name}-g8-vco-access"

    try:
        custom_objs_api.delete_namespaced_custom_object(name=gva_name, **custom_obj_args)
    except k8c.rest.ApiException as exp:
        if int(exp.status) == 404:
            pass
        else:
            raise


def patch_vco_naming(iam_name: str, vco_name: str, vco_utility_name: str, in_cluster: bool = True):
    """patch naming info for a vco

    Args:
        iam_name (str): name of the iam custom object to patch
        vco_name (str): new vco name
        vco_utility_name (str): new vco utility name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    body = dict(
        apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
        kind="IAM",
        metadata=dict(name=iam_name),
        spec=dict(
            vco_name=vco_name,
            vco_utility_name=vco_utility_name,
        ),
    )
    k8c.CustomObjectsApi().patch_namespaced_custom_object(name=iam_name, body=body, **custom_obj_args)


def patch_vco_utility_build_timestamp(iam_name: str, utility_type: str, in_cluster: bool = True):
    """patch naming info for a vco

    Args:
        iam_name (str): name of the iam custom object to patch
        vco_name (str): new vco name
        utility_type (str): utility type
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    timestamp_field = f"{utility_type.replace('-', '_')}_build_timestamp"
    body = dict(
        apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
        kind="IAM",
        metadata=dict(name=iam_name),
        spec={timestamp_field: int(time.time())},
    )
    k8c.CustomObjectsApi().patch_namespaced_custom_object(name=iam_name, body=body, **custom_obj_args)


def restart_deployment(deployment: str, namespace: str = "default", in_cluster: bool = True):
    """Restart deployment, equivalent to `kubectl rollout restart deployment <deployment> -n <namespace>

    see
    https://github.com/kubernetes/kubectl/blob/c1df07341a15fb36264a018e130ee397332bdfc0/pkg/polymorphichelpers/objectrestarter.go#L51

    Args:
        deployment (str): deployment name
        namespace (str): namespace name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    now = datetime.now(timezone.utc).isoformat()  # RFC3339 ts UTC timezone
    body = {"spec": {"template": {"metadata": {"annotations": {"kubectl.kubernetes.io/restartedAt": now}}}}}
    try:
        k8c.AppsV1Api().patch_namespaced_deployment(deployment, namespace, body, pretty="true")
    except k8c.rest.ApiException as exc:
        print(f"Exception when calling AppsV1Api->patch_namespaced_deployment: {exc}\n")


def get_deployment(name: str, namespace: str = "default", in_cluster: bool = True):
    """
    Gets deployment

    Args:
        name(str): config map name
        namespace (str): namespace name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    return k8c.AppsV1Api().read_namespaced_deployment(name, namespace)


def get_config_map(name: str, namespace: str = "default", in_cluster: bool = True):
    """
    Gets config map

    Args:
        name(str): config map name
        namespace (str): namespace name
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    return k8c.CoreV1Api().read_namespaced_config_map(name, namespace)


def update_config_map(
    name: str, namespace: str = "default", create: bool = False, in_cluster: bool = True, *, data: dict
):
    """
    Updates config map

    Args:
        name(str): config map name
        namespace (str): namespace name
        create (bool, optional): Create the configmap if it does not exist
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    """
    _config(in_cluster)
    core_api = k8c.CoreV1Api()
    body = k8c.V1ConfigMap(api_version="v1", metadata=k8c.V1ObjectMeta(name=name, namespace=namespace), data=data)
    try:
        config_map = core_api.patch_namespaced_config_map(name, namespace, body)
    except k8c.rest.ApiException as e:
        if int(e.status) == 404 and create:
            config_map = core_api.create_namespaced_config_map(namespace, body)
        else:
            raise
    return config_map


def scale_replicas(iam_name: str, iam_replicas: int = None, in_cluster: bool = True):
    """Scale VCO replicas

    Args:
        iam_name (str):  Name of VCO (IAM) object
        iam_replicas (int, optional): Number of replicas to be set for IAM deployment.
            If None, deployment will not be changed.
        meneja_replicas (int, optional): [description]. Number of replicas to be set for VCO deployment.
            If None, deployment will not be changed.
        in_cluster (bool, optional): [description]. Defaults to True.
    """
    if iam_replicas is None:
        return
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    body = dict(
        apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
        kind="IAM",
        metadata=dict(name=iam_name),
        spec={},
    )
    if iam_replicas is not None:
        body["spec"]["iam_replicas"] = int(iam_replicas)
    custom_objs_api.patch_namespaced_custom_object(name=iam_name, body=body, **custom_obj_args)


def get_certificates_validity(namespace: str = "default", in_cluster: bool = True) -> List[K8sCertValidity]:
    """Lists k8s certificates

    Args:
        namespace (str, optional): namespace name . Defaults to "default".
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.
    Returns: List of k8s certs struct
    """
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    result = []
    certificates = custom_objs_api.list_namespaced_custom_object(
        group="cert-manager.io", version="v1", namespace=namespace, plural="certificates"
    )
    for cert in certificates["items"]:
        result.append(
            K8sCertValidity(
                cert_name=cert["metadata"]["name"],
                days_left=(
                    parse(cert["status"]["notAfter"]).timestamp() / (60 * 60 * 24)
                    if cert.get("status", {}).get("notAfter", False)
                    else 0
                ),
            )
        )
    return result


def _get_wild_card_secret(domain: str, namespace: str, in_cluster: bool = True) -> str:
    """Get wildcard cert secret name

    Args:
        domain (str): Domain
        namespace (str): Namespace
        in_cluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.

    Returns:
        str: Name of wildcard secret
    """
    _config(in_cluster)
    custom_objs_api = k8c.CustomObjectsApi()
    certificates = custom_objs_api.list_namespaced_custom_object(
        group="cert-manager.io", version="v1", namespace=namespace, plural="certificates"
    )
    domain_parts = domain.split(".")
    for certificate in certificates["items"]:
        if certificate["status"]["conditions"][0]["status"] != "True":
            continue
        for dns in certificate["spec"]["dnsNames"]:
            if dns.startswith("*"):
                dns_parts = dns.split(".")
                if len(domain_parts) >= len(dns_parts) and domain_parts[1:] == dns_parts[1:]:
                    return certificate["spec"]["secretName"]
    return ""


def list_nodes(incluster: bool = True) -> List[dict]:
    """List kubernetes cluster nodes

    Args:
        incluster (bool, optional): Whether call is made inside k8s cluster. Defaults to True.

    Returns:
       List[dict]: List of nodes
    """
    _config(incluster)
    return k8c.CoreV1Api().list_node().items


def clean_oidc_args(iam_args: list) -> list:
    """Remove all OIDC-related arguments from the IAM args list to start with a clean slate.

    Args:
        iam_args (list): List of IAM arguments

    Returns:
        list: Cleaned list with OIDC-related arguments removed
    """
    cleaned_args = []
    for arg in iam_args:
        # Remove any argument that starts with --oidc-provider or --password-login-action
        if not (arg.startswith("--oidc-provider") or arg.startswith("--password-login-action")):
            cleaned_args.append(arg)
    return cleaned_args


def patch_vco_login(
    iam_name: str,
    oidc_provider: dict = None,
    oidc_provider_id: str = None,
    oidc_provider_action: str = None,
    password_login_action: str = None,
    oidc_provider_version: int = None,
):
    """Patch VCO (IAM) Login Settings

    Args:
        iam_name (str): iam_name
        oidc_provider (dict, optional): OIDC Provider data. Required for create, update actions.
        oidc_provider_id (str, optional): OIDC Provider ID. Required for update, delete, activate, deactivate actions.
        oidc_provider_action (str, optional): One of 'create', 'update', 'delete', 'activate', 'deactivate'.
        password_login_action (str, optional): One of 'enable', 'disable'.
        oidc_provider_version (int, optional): Version number for tracking OIDC operations.

    Raises:
        ValueError: if invalid combination of parameters is provided
    """
    if not (oidc_provider_action or password_login_action):
        return

    _config(True)
    custom_objs_api = k8c.CustomObjectsApi()
    custom_obj_args = dict(group="gig.tech", version="v1", namespace="iam", plural="iams")
    body = dict(
        apiVersion=f"{custom_obj_args['group']}/{custom_obj_args['version']}",
        kind="IAM",
        metadata=dict(name=iam_name),
        spec={},
    )

    previous = custom_objs_api.get_namespaced_custom_object(name=iam_name, **custom_obj_args)
    iam_args = previous["spec"]["iam_args"]

    # Clean all OIDC-related args to start with a clean slate
    iam_args = clean_oidc_args(iam_args)

    # Helper function to find and update argument values
    def update_or_append_arg(args, prefix, value):
        for i, arg in enumerate(args):
            if arg.startswith(prefix):
                args[i] = f"{prefix}{value}"
                return True
        args.append(f"{prefix}{value}")
        return False

    # Add version parameter if provided
    if oidc_provider_version is not None:
        update_or_append_arg(iam_args, "--oidc-provider-version=", str(oidc_provider_version))

    if oidc_provider_action:
        if oidc_provider_action not in ("create", "update", "delete", "activate", "deactivate"):
            raise ValueError(f"Invalid oidc_provider_action: {oidc_provider_action}")

        if oidc_provider_action != "create" and not oidc_provider_id:
            raise ValueError("Provider id is required.")

        # Update or add OIDC provider action
        update_or_append_arg(iam_args, "--oidc-provider-action=", oidc_provider_action)

        # Update or add OIDC provider ID if needed
        if oidc_provider_id and oidc_provider_action != "create":
            update_or_append_arg(iam_args, "--oidc-provider-id=", oidc_provider_id)

        # Update or add OIDC provider data for create/update
        if oidc_provider and oidc_provider_action in ("create", "update"):
            for key, value in oidc_provider.items():
                if key not in ("id", "active", "createdAt", "updatedAt") and value:  # Skip readonly and empty fields
                    update_or_append_arg(iam_args, f"--oidc-provider-{key.lower()}=", value)

    if password_login_action:
        if password_login_action not in ("enable", "disable"):
            raise ValueError(f"Invalid password_login_action: {password_login_action}")
        update_or_append_arg(iam_args, "--password-login-action=", password_login_action)

    body["spec"]["iam_args"] = iam_args
    body["spec"]["login_settings_updated_at"] = datetime.now(timezone.utc).isoformat()

    custom_objs_api.patch_namespaced_custom_object(name=iam_name, body=body, **custom_obj_args)
