<template>
  <page :loading="loading">
    <template #content>
      <Breadcrumbs :items="breadcrumbs"></Breadcrumbs>
      <v-dialog v-model="storageOnly" max-width="800" @keydown.esc="close">
        <template v-slot:default>
          <v-card>
            <v-card-title>
              <span class="headline"
                ><span class="i18n" id="vm.storage-only-location">{{
                  $t("vm.storage-only-location")
                }}</span></span
              >
            </v-card-title>
            <v-card-subtitle class="mt-2">
              <span style="font-size: 18px"
                >{{
                  $t(
                    "vm.the-specified-location-location-is-designated-as-a-storage-only-cloud-location-it-does-not-support-the-creation-of-virtual-machines",
                    [location]
                  )
                }}
              </span>
            </v-card-subtitle>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                variant="text"
                @click="$router.replace({ name: 'CloudspaceManagement' })"
                ><span class="i18n" id="admin.close">{{
                  $t("admin.close")
                }}</span></v-btn
              >
            </v-card-actions>
          </v-card>
        </template>
      </v-dialog>

      <v-form
        ref="form "
        style="width: 100%"
        :disabled="submitLoading || submitLoadingBatch"
      >
        <Note
          v-if="showNote"
          :dismissable="true"
          style="max-height: 80px; width: 100%"
          class="full-width"
        >
          <span
            class="i18n"
            id="vm.note-recommended-way-to-import-vm-from-s3-storage-is-via-cli"
            >{{
              $t(
                "vm.note-recommended-way-to-import-vm-from-s3-storage-is-via-cli"
              )
            }}</span
          >
          <a :href="docsLink">
            <span class="i18n" id="vm.cli-documentation">{{
              $t("vm.cli-documentation")
            }}</span></a
          >
        </Note>
        <Note
          v-if="showBackupNote"
          :dismissable="true"
          style="max-height: 80px; width: 100%"
          class="full-width"
        >
          <span
            class="i18n"
            id="vm.note-operation-system-type-should-match-the-vm-that-was-backed-up"
            >{{
              $t(
                "vm.note-operation-system-type-should-match-the-vm-that-was-backed-up"
              )
            }}</span
          >
        </Note>
        <v-stepper :model-value="step" class="elevation-0">
          <!-- Headers of steppers -->
          <v-stepper-header variant="flat" class="elevation-0">
            <v-stepper-item value="1">
              {{ currentStepText(1) }}
            </v-stepper-item>
            <v-divider></v-divider>
            <v-stepper-item value="2">
              {{ currentStepText(2) }}
            </v-stepper-item>
            <v-divider></v-divider>
            <v-stepper-item value="3">
              {{ currentStepText(3) }}
            </v-stepper-item>
            <v-divider></v-divider>
            <v-stepper-item value="4">
              {{ currentStepText(4) }}
            </v-stepper-item>
            <v-divider></v-divider>
            <v-stepper-item value="5">
              {{ currentStepText(5) }}
            </v-stepper-item>
          </v-stepper-header>
          <div class="stepper-button mt-5 ml-5" variant="flat">
            <v-btn
              v-if="step != 0"
              class="ml-n4 mt-n2"
              variant="text"
              color="primary"
              @click="stepBack"
              :disabled="submitLoading || submitLoadingBatch"
            >
              <v-icon>mdi-chevron-triple-left</v-icon>PREVIOUS
            </v-btn>
            <v-btn
              v-if="step < stepsHeaders[vmSource].length - 1"
              class="ml-n3 mt-n2 next"
              variant="text"
              color="primary"
              @click="stepForward"
            >
              NEXT
              <v-icon>mdi-chevron-triple-right</v-icon>
            </v-btn>
          </div>
          <!-- Content of steppers -->
          <v-row>
            <v-col cols="9">
              <v-stepper-window>
                <!-- step 1 -->
                <v-stepper-window-item variant="flat" value="1" class="ml-0">
                  <div class="mb-2 font-weight-bold">
                    <span class="i18n" id="vm.select-virtual-machine-source">{{
                      $t("vm.select-virtual-machine-source")
                    }}</span>
                  </div>
                  <v-radio-group
                    color="primary"
                    column
                    v-model="vmSource"
                    class="ml-2"
                  >
                    <v-radio value="image">
                      <template #label>
                        <span id="vm.create-from-an-image" class="i18n">{{
                          $t("vm.create-from-an-image")
                        }}</span>
                      </template>
                    </v-radio>
                    <v-radio value="cdrom">
                      <template #label>
                        <span id="vm.install-via-a-cdrom" class="i18n">{{
                          $t("vm.install-via-a-cdrom")
                        }}</span>
                      </template>
                    </v-radio>
                    <v-radio value="empty">
                      <template #label>
                        <span
                          id="vm.create-a-virtual-machine-without-operating-system-installed"
                          class="i18n"
                          >{{
                            $t(
                              "vm.create-a-virtual-machine-without-operating-system-installed"
                            )
                          }}</span
                        >
                      </template>
                    </v-radio>
                    <v-radio value="s3">
                      <template #label>
                        <span id="vm.import-from-s3" class="i18n">{{
                          $t("vm.import-from-s3")
                        }}</span>
                      </template>
                    </v-radio>
                    <v-radio value="snapshot">
                      <template #label>
                        <span id="vm.create-from-a-snapshot" class="i18n">{{
                          $t("vm.create-from-a-snapshot")
                        }}</span>
                      </template>
                    </v-radio>
                    <v-radio value="backup">
                      <template #label>
                        <span id="vm.create-from-cdrom-backup" class="i18n">{{
                          $t("vm.create-from-cdrom-backup")
                        }}</span>
                      </template>
                    </v-radio>
                    <v-radio value="bootDisk">
                      <template #label>
                        <span id="vm.create-from-boot-disk" class="i18n">{{
                          $t("vm.create-from-boot-disk")
                        }}</span>
                      </template>
                    </v-radio>
                  </v-radio-group>
                </v-stepper-window-item>
                <!-- step 2 -->
                <v-stepper-window-item
                  variant="flat"
                  value="2"
                  class="ml-0 pt-0"
                >
                  <div v-if="imageVmSource">
                    <p class="mb-2 font-weight-bold">
                      <span class="i18n" id="vm.select-image">{{
                        $t("vm.select-image")
                      }}</span>
                    </p>
                    <data-table
                      key="images"
                      class="row-clickable-table"
                      :headers="imageHeaders"
                      :items="images"
                      :loadingText="$t('vm.loading-images')"
                      v-model:page="imagePage"
                      :noDataText="$t('vm.no-images-available')"
                      itemKey="image_id"
                      :limit="10"
                      :loading="imagesLoading"
                      :rowClickEvent="imageRowClickEvent"
                      @refresh="getImages"
                      :singleSelect="true"
                      v-model:selectedRows="selectedImage"
                    >
                      <template v-slot:[`item.name`]="props">
                        <div class="image-title">
                          <img
                            class="image-logo"
                            v-if="props.item.logo"
                            :src="props.item.logo"
                          />
                          <img
                            class="image-logo"
                            v-else
                            src="@/assets/noimage.png"
                          />
                          <span class="image-name">
                            {{ props.item.name }}
                          </span>
                          <v-btn
                            class="ml-2 btn-shadow"
                            variant="text"
                            @click.stop="null"
                            :to="{
                              name: 'VMImageDetails',
                              params: {
                                imageId: props.item.image_id,
                                location: location
                              }
                            }"
                            target="_blank"
                          >
                            <v-icon size="small">mdi-open-in-new</v-icon>
                          </v-btn>
                          <copy-id
                            class="copy-id-beside-text"
                            :element="{
                              name: props.item.name,
                              id: props.item.image_id
                            }"
                          />
                        </div>
                      </template>
                    </data-table>
                  </div>
                  <div v-if="cdImageVmSource">
                    <p class="mb-2 font-weight-bold">
                      <span class="i18n" id="vm.select-cdrom-image">{{
                        $t("vm.select-cdrom-image")
                      }}</span>
                    </p>
                    <data-table
                      key="cdroms"
                      class="row-clickable-table"
                      :headers="cdImageHeaders"
                      :items="cdImages"
                      loading-text="Loading cdrom images..."
                      v-model:page="cdPage"
                      no-data-text="No cdrom images available"
                      itemKey="cdrom_id"
                      :limit="10"
                      :loading="cdImagesLoading"
                      :rowClickEvent="cdImageRowClickEvent"
                      @refresh="getCDROMImages"
                      :singleSelect="true"
                      v-model:selectedRows="selectedCdrom"
                    >
                      <template v-slot:[`item.name`]="props">
                        <div class="image-title">
                          <img
                            class="image-logo"
                            v-if="props.item.logo"
                            :src="props.item.logo"
                          />
                          <img
                            class="image-logo"
                            v-else
                            src="@/assets/noimage.png"
                          />
                          <span class="cdrom-name">{{ props.item.name }}</span>
                          <v-btn
                            variant="text"
                            class="ml-2 btn-shadow"
                            @click.stop="null"
                            :to="{
                              name: 'CDROMImageDetails',
                              params: {
                                cdromId: props.item.cdrom_id,
                                location: location
                              }
                            }"
                            target="_blank"
                          >
                            <v-icon size="small">mdi-open-in-new</v-icon>
                          </v-btn>
                          <copy-id
                            class="copy-id-beside-text"
                            :element="{
                              name: props.item.name,
                              id: props.item.cdrom_id
                            }"
                          />
                        </div>
                      </template>
                    </data-table>
                  </div>
                  <v-row v-if="vmSource == 's3'">
                    <v-col cols="6">
                      <v-text-field
                        v-model="s3.link"
                        :label="$t('vm.link')"
                        class="mt-2"
                        :placeholder="$t('vm.s3-server-link')"
                        required
                        variant="outlined"
                        density="compact"
                      >
                      </v-text-field>
                      <v-text-field
                        v-model="s3.key"
                        :label="$t('vm.key')"
                        :placeholder="$t('vm.access-key-to-the-server')"
                        required
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                      <v-text-field
                        v-model="s3.secret"
                        :label="$t('vm.secret')"
                        :placeholder="$t('vm.secret-to-access-the-server')"
                        type="password"
                        required
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                      <v-text-field
                        v-model="s3.region"
                        :label="$t('vm.region')"
                        :placeholder="$t('vm.region-of-the-s3-server')"
                        required
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                      <v-text-field
                        v-model="s3.bucket"
                        :label="$t('vm.bucket')"
                        :placeholder="$t('vm.name-of-the-bucket')"
                        required
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                      <v-text-field
                        v-model="s3.object_name"
                        :label="$t('vm.object-name')"
                        :placeholder="
                          $t('vm.name-of-the-stored-object-in-the-bucket')
                        "
                        required
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <div v-if="fromSnapshot || vmSource == 'bootDisk'">
                    <p class="font-weight-bold">
                      <span class="i18n" id="vm.select-boot-disk">{{
                        $t("vm.select-boot-disk")
                      }}</span>
                    </p>
                    <data-table
                      class="row-clickable-table"
                      :headers="disksHeaders"
                      :items="bootDisksFiltered"
                      :loadingText="$t('lvm.oading-disks')"
                      v-model:page="disksPage"
                      :noDataText="$t('vm.no-disks-available')"
                      itemKey="disk_id"
                      :limit="5"
                      :loading="disksLoading"
                      :rowClickEvent="diskRowClickEvent"
                      @refresh="listBootDisks"
                      :singleSelect="true"
                      v-model:selectedRows="selectedDisk"
                    >
                      <template v-slot:[`item.disk_name`]="props">
                        <span>{{ props.item.disk_name }}</span>
                        <copy-id
                          :element="{
                            name: 'Disk',
                            id: props.item.disk_id
                          }"
                        />
                      </template>
                      <template v-slot:[`item.cloudspace_id`]="props">
                        <span>{{ getCsName(props.item.cloudspace_id) }}</span>
                      </template>
                      <template v-slot:[`item.disk_size`]="props">
                        <span>{{ props.item.disk_size + " GiB" }}</span>
                      </template>
                    </data-table>
                    <v-row v-if="fromSnapshot" class="mt-5">
                      <v-col cols="12">
                        <p class="font-weight-bold mb-3">
                          <span class="i18n" id="vm.select-snapshot">{{
                            $t("vm.select-snapshot")
                          }}</span>
                        </p>
                        <v-row>
                          <v-checkbox
                            color="primary"
                            v-if="bootDisk_vm"
                            v-model="allVMDisks"
                            class="ml-3 mb-n4"
                          >
                            <template #label>
                              <span
                                class="i18n"
                                id="vm.clone-all-disks-created-along-with-the-selected-one"
                                >{{
                                  $t(
                                    "vm.clone-all-disks-created-along-with-the-selected-one"
                                  )
                                }}</span
                              >
                            </template>
                          </v-checkbox>
                        </v-row>
                        <v-row>
                          <v-checkbox
                            color="primary"
                            hide-details
                            class="ml-3 mb-3"
                            v-model="includeAutomatic"
                          >
                            <template #label
                              ><span
                                class="i18n"
                                id="vm.show-automatic-snapshots"
                                >{{ $t("vm.show-automatic-snapshots") }}</span
                              ></template
                            >
                          </v-checkbox>
                        </v-row>
                        <data-table
                          :headers="snapshotsHeaders"
                          :items="snapshots"
                          :loadingText="$t('vm.loading-snapshots')"
                          v-model:page="snapshotsPage"
                          :noDataText="
                            $t(
                              'vm.no-snapshots-available-for-boot-disk-bootdiskname',
                              [bootDiskName]
                            )
                          "
                          itemKey="snapshot_id"
                          :limit="5"
                          :loading="snapshotsLoading"
                          :rowClickEvent="snapshotRowClickEvent"
                          @refresh="listSnapshots(bootDiskId)"
                          :singleSelect="true"
                          v-model:selectedRows="selectedSnapshot"
                        />
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="vmSource == 'backup'" class="pt-7">
                    <div class="mb-2 font-weight-bold">
                      <span class="i18n" id="vm.select-backup-source">{{
                        $t("vm.select-backup-source")
                      }}</span>
                    </div>
                    <v-radio-group
                      column
                      color="primary"
                      v-model="backupSource"
                      class="ml-2"
                    >
                      <v-radio value="acronis">
                        <template #label>
                          <span
                            id="vm.create-from-acronis-backup"
                            class="i18n"
                            >{{ $t("vm.create-from-acronis-backup") }}</span
                          >
                        </template>
                      </v-radio>
                      <v-radio value="veeam">
                        <template #label>
                          <span id="vm.create-from-veeam-backup" class="i18n">{{
                            $t("vm.create-from-veeam-backup")
                          }}</span>
                        </template>
                      </v-radio>
                    </v-radio-group>
                  </div>
                </v-stepper-window-item>
                <!-- step 3 -->
                <v-stepper-window-item variant="flat" value="3">
                  <div class="mb-2 font-weight-bold">
                    <span class="i18n" id="vm.compute-size">{{
                      $t("vm.compute-size")
                    }}</span>
                  </div>
                  <v-row>
                    <v-col cols="8">
                      <slider
                        :maximum="64"
                        :minimum="1"
                        label="vCPUs"
                        class="align-center pb-4"
                        @onChange="changeVcpus"
                        textStyle="width: 100px"
                        :log="false"
                        color="primary"
                        :startPosition="vcpus"
                      />

                      <slider
                        height="48 px"
                        :label="$t('customer.memory-mib')"
                        :maximum="262144"
                        :minimum="minMemory"
                        class="align-center pb-4"
                        :log="true"
                        textStyle="width: 100px"
                        :multipleOf="128"
                        :logObj="memoryLogObj"
                        @onChange="changeMemoryValue"
                        :startPosition="memory"
                        color="primary"
                      />

                      <slider
                        v-if="
                          vmSource != 's3' &&
                          vmSource != 'snapshot' &&
                          vmSource != 'bootDisk' &&
                          minBootDiskSize
                        "
                        height="48 px"
                        :label="$t('customer.boot-disk-size-gib')"
                        :maximum="20000"
                        :minimum="minBootDiskSize"
                        :log="true"
                        textStyle="width: 100px"
                        :logObj="diskLogObj"
                        @onChange="changeBootDisk"
                        :startPosition="bootDiskSize"
                        color="primary"
                      />
                      <!-- TODO: uncomment when create vm api call in the g8 side accepts gpus   -->
                      <!-- 
                      <div class="mb-2 font-weight-bold" v-if="isTherevGPUsOnLocation && attachVgpuProfileAvailable"><span class='i18n' id='vm.attach-vgpu'>{{ $t('vm.attach-vgpu') }}</span></div>
                      <div class="mb-2 font-weight-bold" v-if="!isTherevGPUsOnLocation && attachVgpuProfileAvailable">{{ $t('vm.gpu-profiles-are-not-available-on-location-if-you-want-to-use-virtual-machines-with-virtual-gpu-contact-store-state-userinfo-vco_support_email', [location, $store.state.userInfo.vco_support_email]) }}</div>
                      <v-checkbox
                        color="primary"
                        v-if="isTherevGPUsOnLocation && attachVgpuProfileAvailable"
                        v-model="isThereGpu"
                        class="my-0 ml-2"
                      >
    <template #label><span class="i18n" id="vm.reserve-and-attach-gpu-profile-on-this-virtual-machine">{{$t('vm.reserve-and-attach-gpu-profile-on-this-virtual-machine')}}</span></template>
                      </v-checkbox>
                      <v-select
                        v-if="isThereGpu"
                        variant="outlined"
                        density="compact"
                        :item-title="formattedItemText"
                        item-value="id"
                        :label="$t('vm.available-vgpu-profiles')"
                        :items="vgpuProfiles"
                        v-model="vgpuProfile"
                        :loading="vgpuProfileLoading"
                      />
                      <v-text-field
                        v-if="isThereGpu"
                        placeholder=" "
                        :label="$t('gpus.vgpu-name')"
                        v-model="vgpuName"
                        hide-details
                        variant="outlined"
                        density="compact"
                      /> -->
                    </v-col>
                  </v-row>
                </v-stepper-window-item>
                <!-- step 4 -->
                <v-stepper-window-item variant="flat" value="4">
                  <page :loading="step4Loading" class="mt-n8">
                    <template #content>
                      <div v-if="fromSnapshot" class="mb-2 font-weight-bold">
                        <span class="i18n" id="vm.add-more-data-disks">{{
                          $t("vm.add-more-data-disks")
                        }}</span>
                      </div>
                      <div v-else class="mb-2 font-weight-bold">
                        <span class="i18n" id="vm.add-data-disks">{{
                          $t("vm.add-data-disks")
                        }}</span>
                      </div>
                      <v-row>
                        <v-col cols="12">
                          <help
                            topicId="diskCreation"
                            class="mb-5"
                            :title="$t('vm.data-disks')"
                          >
                            <span
                              class="i18n"
                              id="vm.data-disks-added-during-virtual-machine-creation-are-configured-as-virtual-defined-storage-which-is-redundant-across-multiple-servers-and-storage-devices-for-enhanced-reliability"
                              >{{
                                $t(
                                  "vm.data-disks-added-during-virtual-machine-creation-are-configured-as-virtual-defined-storage-which-is-redundant-across-multiple-servers-and-storage-devices-for-enhanced-reliability"
                                )
                              }}</span
                            >
                            <br />
                            <span
                              class="i18n"
                              id="vm.for-dedicated-physical-storage-please-add-such-disks-after-creating-the-virtual-machine-to-access-specific-hardware-resources"
                              >{{
                                $t(
                                  "vm.for-dedicated-physical-storage-please-add-such-disks-after-creating-the-virtual-machine-to-access-specific-hardware-resources"
                                )
                              }}</span
                            >
                          </help>
                        </v-col>
                        <v-col cols="6">
                          <v-slider
                            v-for="(input, k) in inputs"
                            :key="k"
                            variant="outlined"
                            density="compact"
                            :label="$t('cloudspace.disk-size-gib')"
                            v-model="input.dataDiskSliderPosition"
                            min="1"
                            max="100"
                            @click:append="deleteDisk(k)"
                            class="align-center pb-4 ml-2"
                            hide-details
                            single-line
                            :disabled="input.disabled"
                            color="primary"
                            @update:modelValue="setDataDiskValue(input)"
                          >
                            <template v-slot:append>
                              <v-text-field
                                :placeholder="$t('vm.data-disk-size-gib')"
                                append-inner-icon="mdi-delete"
                                @click:append-inner="deleteDisk(k)"
                                v-model="input.diskSize"
                                class="mt-0 pt-0"
                                type="number"
                                min="1"
                                max="20000"
                                hide-details
                                variant="outlined"
                                density="compact"
                                single-line
                                style="width: 115px"
                                :disabled="input.disabled"
                                @change="setDataDiskSliderPosition(input)"
                                @blur="setDataDiskSliderPosition(input, true)"
                                @keyup="setDataDiskSliderPosition(input)"
                              />
                            </template>
                          </v-slider>
                          <v-btn
                            class="mb-3"
                            variant="text"
                            color="primary"
                            @click="addNewDisk"
                          >
                            <v-icon class="ml-n4 mr-2" end dark>mdi-plus</v-icon
                            ><span class="i18n" id="vm.data-disk">{{
                              $t("vm.data-disk")
                            }}</span>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </template>
                  </page>
                </v-stepper-window-item>
                <!-- step 5 -->
                <v-stepper-window-item variant="flat" value="5">
                  <help
                    topicId="User_data_template"
                    :title="$t('user_data.Image-templates-title')"
                    id="data-template-help"
                    class="mb-9"
                    style="width: 800px"
                  >
                    <p>
                      {{ $t("user_data.user-data-help-title-1") }}
                      <a
                        href="https://cloudinit.readthedocs.io/en/latest/index.html"
                        target="_blank"
                        >Cloud-Init</a
                      >
                      {{ $t("user_data.user-data-help-title-2") }}
                    </p>
                    <h3>{{ $t("user_data.using-templates") }}</h3>
                    <ul>
                      <li>
                        <strong>{{
                          $t("user_data.predefined-templates")
                        }}</strong>
                        {{ $t("user_data.user-data-help-list-1-item-1") }}
                      </li>

                      <li>
                        <strong>{{
                          $t("user_data.user-data-help-list-1-head-2")
                        }}</strong>
                        {{ $t("user_data.user-data-help-list-1-item-2") }}
                      </li>
                      <li>
                        <strong>{{
                          $t("user_data.user-data-help-list-1-head-3")
                        }}</strong>
                        {{ $t("user_data.user-data-help-list-1-item-3") }}
                      </li>
                    </ul>
                    <h3>{{ $t("user_data.types-of-parameters") }}</h3>
                    <div class="ml-3">
                      <h4>{{ $t("user_data.regular-parameters") }}</h4>
                      <ul>
                        <li>
                          {{ $t("user_data.user-data-help-list-2-item-1") }}
                        </li>
                        <li>
                          {{ $t("user_data.user-data-help-list-2-item-2") }}
                        </li>
                        <li>
                          Ex:
                          <code
                            v-text="`hostname: &quot;{{hostname}}&quot;`"
                          ></code>
                          {{ $t("user_data.user-data-help-list-2-item-3") }}
                        </li>
                      </ul>

                      <h4>{{ $t("user_data.magic-parameters") }}</h4>
                      <ul>
                        <li>
                          {{ $t("user_data.user-data-help-list-3-item-1") }}
                        </li>
                        <li>
                          {{ $t("user_data.user-data-help-list-2-item-4") }}
                        </li>
                        <li>
                          {{ $t("user_data.user-data-help-list-3-item-2") }}
                          <ol>
                            <li
                              v-for="(param, index) in magicParameters"
                              :key="index"
                            >
                              <code v-text="param.param"></code>
                              <copy-id
                                :element="{
                                  id: `{{${param.param}}}`,
                                  command: true
                                }"
                              />: {{ param.descriptiion }}
                            </li>
                          </ol>
                        </li>
                      </ul>
                    </div>
                    <h3>{{ $t("user_data.how-to-use-templates") }}</h3>
                    <ul>
                      <li>
                        <strong>{{
                          $t("user_data.user-data-help-list-4-item-1")
                        }}</strong>
                        {{ $t("user_data.user-data-help-list-4-item-2") }}
                        <code>{{
                          $t("user_data.user-data-help-list-4-item-3")
                        }}</code>
                        {{ $t("user_data.user-data-help-list-4-item-5") }}
                      </li>
                      <li>
                        <strong>{{
                          $t("user_data.user-data-help-list-4-item-4")
                        }}</strong>
                        {{ $t("user_data.user-data-help-list-4-item-6") }}
                      </li>
                    </ul>
                  </help>
                  <div class="mb-2 font-weight-bold">
                    <span class="i18n" id="vm.add-configuration-details">{{
                      $t("vm.add-configuration-details")
                    }}</span>
                  </div>
                  <v-row>
                    <v-col cols="6">
                      <v-text-field
                        :label="$t('customer.name')"
                        :placeholder="$t('vm.virtual-machine-name')"
                        v-model="name"
                        ref="name"
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                      <v-select
                        v-if="showOsSelect"
                        variant="outlined"
                        density="compact"
                        :label="$t('vm.operating-system-type')"
                        :items="operatingSystemTypes"
                        v-model="operatingSystemType"
                        :loading="osTypesLoading"
                        color="primary"
                      />
                      <v-select
                        v-if="showOsSelect"
                        variant="outlined"
                        density="compact"
                        :label="$t('vm.operating-system-name')"
                        :items="operatingSystemNames"
                        v-model="operatingSystemName"
                        :loading="osNamesLoading"
                      />
                      <v-select
                        variant="outlined"
                        density="compact"
                        :label="$t('customer.boot-type')"
                        :items="bootTypes"
                        v-model="bootType"
                        :loading="bootTypeLoading"
                        :disabled="disableBootTypeSelect && vmSource == 'image'"
                      >
                        <template
                          #append-inner
                          v-if="disableBootTypeSelect && vmSource == 'image'"
                        >
                          <v-tooltip location="top">
                            <template #activator="{ props }">
                              <v-icon v-bind="props" color="primary"
                                >mdi-help-circle-outline</v-icon
                              >
                            </template>
                            <span
                              ><span
                                class="i18n"
                                id="vm.boot-type-is-defined-by-the-image"
                                >{{
                                  $t("vm.boot-type-is-defined-by-the-image")
                                }}</span
                              ></span
                            >
                          </v-tooltip>
                        </template>
                      </v-select>
                      <v-text-field
                        :label="$t('vm.description-optional')"
                        :placeholder="$t('vm.virtual-machine-description')"
                        v-model="description"
                        ref="description"
                        variant="outlined"
                        density="compact"
                      ></v-text-field>
                      <v-select
                        v-model="private_ip"
                        :items="availableIPAddresses"
                        variant="outlined"
                        density="compact"
                        :label="$t('vm.private-ip-address')"
                        :loading="IPAddressesLoading"
                        :disabled="IPAddressesLoading"
                      />
                    </v-col>

                    <v-col cols="8">
                      <v-switch
                        color="primary"
                        v-if="userDataAvailable && osType != 'Windows'"
                        v-model="advancedUserData"
                        class="my-0"
                      >
                        <template #label>
                          <span
                            class="i18n"
                            id="user_data.advanced-user-data"
                            >{{ $t("user_data.advanced-user-data") }}</span
                          >
                        </template>
                      </v-switch>
                      <template v-if="advancedUserData">
                        <template v-if="selectedImageData.user_data_templates">
                          <v-select
                            v-model="SelectedUserDataTemplateId"
                            item-title="label"
                            item-value="id"
                            :items="selectedImageData.user_data_templates"
                            variant="outlined"
                            density="compact"
                            :label="$t('user_data.user-data-template')"
                            :loading="bootTypeLoading"
                            :disabled="bootTypeLoading"
                          />
                          <div
                            class="mb-2 font-weight-bold"
                            v-if="user_parameters.length"
                          >
                            {{ $t("user_data.template-params") }}
                          </div>
                          <v-divider v-if="user_parameters.length"></v-divider>
                          <div
                            id="template-params"
                            v-if="user_parameters.length"
                          >
                            <v-row
                              v-for="(param, index) in user_parameters"
                              :key="index"
                            >
                              <v-col cols="6">
                                <help-text-field
                                  :help="
                                    param.description ||
                                    $t('customer.no-description')
                                  "
                                  :label="$t('admin.name')"
                                  v-model="param.name"
                                  variant="outlined"
                                  density="compact"
                                  readonly
                                />
                              </v-col>
                              <v-col cols="6">
                                <v-text-field
                                  :label="$t('admin.value')"
                                  v-model="param.default_value"
                                  variant="outlined"
                                  density="compact"
                                />
                              </v-col>
                            </v-row>
                          </div>
                        </template>
                        <code-section
                          height="200px !important"
                          :value="SelectedUserDataTemplate.content"
                          :lineNumbers="true"
                          mode="yaml"
                          @code-updated="userDataInputHandler"
                        />
                      </template>

                      <v-textarea
                        v-model="userData"
                        v-if="
                          !advancedUserData &&
                          userDataAvailable &&
                          osType != 'Windows'
                        "
                        variant="outlined"
                        density="compact"
                        :label="$t('vm.ssh-key-optional')"
                        placeholder=" "
                      />
                      <v-checkbox
                        v-if="vmSource != 'empty'"
                        color="primary"
                        v-model="startVm"
                        class="my-0"
                      >
                        <template #label>
                          <span
                            class="i18n"
                            id="vm.start-virtual-machine-s-immediately-after-creation"
                            >{{
                              $t(
                                "vm.start-virtual-machine-s-immediately-after-creation"
                              )
                            }}</span
                          >
                        </template>
                      </v-checkbox>

                      <v-checkbox
                        v-if="vmSource == 's3'"
                        color="primary"
                        v-model="s3.strict"
                        class="my-0"
                      >
                        <template #label>
                          <span
                            class="i18n"
                            id="vm.import-virtual-machine-as-close-as-possible-to-original-hardware"
                            >{{
                              $t(
                                "vm.import-virtual-machine-as-close-as-possible-to-original-hardware"
                              )
                            }}</span
                          >
                        </template>
                      </v-checkbox>
                    </v-col>
                  </v-row>

                  <v-row>
                    <v-col cols="auto">
                      <v-btn
                        color="primary"
                        :loading="submitLoading"
                        :disabled="submitLoadingBatch"
                        @click="createVirtualMachine"
                        >{{
                          `${vmSource != "s3" ? "Create" : "Import"}`
                        }}</v-btn
                      >
                    </v-col>
                    <v-col cols="auto">
                      <v-btn
                        variant="text"
                        color="primary"
                        :disabled="submitLoading"
                        :loading="submitLoadingBatch"
                        @click="showCard = !showCard"
                        >{{
                          `${vmSource != "s3" ? "Create" : "Import"} Batch`
                        }}</v-btn
                      >
                    </v-col>
                    <v-col cols="auto">
                      <v-btn
                        variant="text"
                        :disabled="submitLoading || submitLoadingBatch"
                        color="primary"
                        @click="close"
                        ><span class="i18n" id="admin.cancel">{{
                          $t("admin.cancel")
                        }}</span></v-btn
                      >
                    </v-col>
                  </v-row>
                </v-stepper-window-item>
              </v-stepper-window>
            </v-col>
            <v-col cols="3" class="container">
              <PriceWidget
                :disabled="step < 2"
                v-if="!isFixedPrice"
                :inputs="priceInputs"
              />
              <ResourcesWidget
                v-if="isFixedPrice && step >= 3 && !locationsLoading"
                :inputs="priceInputs"
                :resourceConsumption="locationConsumption"
              />
            </v-col>
          </v-row>
        </v-stepper>
        <div class="stepper-button mt-5 ml-5" variant="flat">
          <v-btn
            v-if="step != 0"
            class="ml-n4 mt-n2"
            variant="text"
            color="primary"
            @click="stepBack"
            :disabled="submitLoading || submitLoadingBatch"
          >
            <v-icon>mdi-chevron-triple-left</v-icon>PREVIOUS
          </v-btn>
          <v-btn
            v-if="step < stepsHeaders[vmSource].length - 1"
            class="ml-n3 mt-n2 next"
            variant="text"
            color="primary"
            @click="stepForward"
          >
            NEXT
            <v-icon>mdi-chevron-triple-right</v-icon>
          </v-btn>
        </div>
      </v-form>
      <v-dialog v-model="showCard" persistent max-width="600px">
        <v-card class="pa-2">
          <v-card-title>
            <span id="vm.batch-size" class="text-h6">{{
              $t("vm.batch-size")
            }}</span>
          </v-card-title>

          <v-card-text>
            <v-container>
              <v-row>
                <v-text-field
                  type="number"
                  min="0"
                  :max="availableIPAddresses.length"
                  class="custom-text-field"
                  variant="outlined"
                  v-model="vms_no"
                  :label="$t('vm.number-of-VMs-in-batch')"
                ></v-text-field>
              </v-row>
            </v-container>
          </v-card-text>

          <v-card-actions>
            <v-btn color="primary" variant="flat" @click="createVirtualMachine">
              <span class="i18n" id="vm.create-batch">{{
                $t("vm.create-batch")
              }}</span>
            </v-btn>
            <v-btn color="primary" variant="text" @click="showCard = !showCard">
              <span class="i18n" id="admin.cancel">{{
                $t("admin.cancel")
              }}</span>
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </template>
  </page>
</template>

<script>
  import { parse } from "yaml"
  import { PageComponent } from "../../mixins/page"
  import Log from "../Utilities/Log"
  import PriceWidget from "../Utilities/PriceWidget.vue"
  import ResourcesWidget from "../Utilities/ResourcesWidget.vue"
  import Note from "../Utilities/Note.vue"
  export default {
    name: "CreateVm",
    mixins: [PageComponent],
    components: { PriceWidget, Note, ResourcesWidget },
    data() {
      return {
        showCard: false,
        vms_no: 1,
        isThereGpu: false,
        vgpuProfiles: [],
        vgpuProfile: "",
        vgpuName: "",
        vgpuProfileLoading: false,
        attachVgpuProfileAvailable: true,
        step: 0,
        imagePage: 1,
        cdPage: 1,
        detailedPrice: true,
        imagesLoading: false,
        cdImagesLoading: false,
        submitLoading: false,
        submitLoadingBatch: false,
        disksLoading: false,
        snapshotsLoading: false,
        s3: {
          link: "",
          key: "",
          secret: "",
          region: "",
          bucket: "",
          object_name: "",
          strict: false
        },
        startVm: true,
        name: "",
        description: "",
        private_ip: "",
        disks: [],
        vcpus: 0,
        bootDiskSize: 1,
        memory: 128,
        iops: 0,
        userData: "",
        cloudspaceName: "",
        vmSource: "image",
        imageType: "",
        imageName: "",
        cdImageName: "",
        imageId: "",
        cdImageId: "",
        osType: "",
        inputs: [],
        minMemory: 128,
        minBootDiskSize: 1,
        imageHeaders: [
          { text: this.$t("customer.image"), value: "name" },
          { text: this.$t("customer.os-type"), value: "os_type" },
          { text: this.$t("customer.boot-disk-size"), value: "boot_disk_size" },
          { text: this.$t("customer.memory"), value: "memory" },
          { text: this.$t("vm.image-id"), value: "image_id" }
        ],
        images: [],
        cdImageHeaders: [
          { text: this.$t("customer.image"), value: "name" },
          { text: this.$t("customer.os-name"), value: "os_name" },
          { text: this.$t("customer.os-type"), value: "os_type" },
          { text: this.$t("vm.image-id"), value: "cdrom_id" }
        ],
        cdImages: [],
        bootDisks: [],
        snapshotsHeaders: [
          { text: this.$t("admin.name"), value: "snapshot_name" },
          { text: this.$t("vm.creation-time"), value: "creation_time" }
        ],
        snapshots: [],
        disksPage: 1,
        snapshotsPage: 1,
        price: "",
        currency: "",
        mu: "0",
        vcu: "0",
        su: "0",
        tu: "0",
        wu: "0",
        operatingSystems: [],
        operatingSystemNames: [],
        operatingSystemName: "",
        operatingSystemTypes: [],
        operatingSystemType: "",
        totalDiskSize: 0,
        userDataAvailable: true,
        osNamesLoading: true,
        osTypesLoading: true,
        diskId: "",
        snapshotId: "",
        bootDiskId: "",
        bootDiskName: "",
        cloudspaces: [],
        storageOnly: false,
        snapshotName: "",
        virtualMachines: [],
        allVMDisks: false,
        allDisks: [],
        disksFromSnapshot: [],
        step4Loading: false,
        memorySlider: 1,
        bootDiskSlider: 1,
        currentRoute: "",
        params: {},
        priceInputs: {},
        loading: true,
        includeAutomatic: false,
        bootDisk_vm: "",
        bootTypes: ["bios", "uefi", "uefi-secure-boot"],
        bootType: "bios",
        selectedImageData: {},
        bootTypeLoading: false,
        disableBootTypeSelect: false,
        stepsHeaders: {
          step1: [
            { step: "1", text: this.$t("vm.select-virtual-machine-source") },
            { step: "2", text: this.$t("vm.select-image"), code: "image" },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          image: [
            { step: "1", text: this.$t("vm.create-from-an-image") },
            { step: "2", text: this.$t("vm.select-image"), code: "image" },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          cdrom: [
            { step: "1", text: this.$t("vm.install-via-a-cdrom") },
            { step: "2", text: this.$t("vm.select-image"), code: "cdrom" },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          empty: [
            { step: "1", text: this.$t("vm.no-operating-system-installed") },
            { step: "2", text: this.$t("vm.no-image-selected") },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          s3: [
            { step: "1", text: this.$t("vm.import-from-s3") },
            { step: "2", text: this.$t("vm.s3-information"), code: "s3" },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            {
              step: "4",
              text: this.$t("vm.storage-disabled"),
              code: "storage"
            },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          snapshot: [
            { step: "1", text: this.$t("vm.create-from-snapshot") },
            { step: "2", text: this.$t("vm.select-snapshot"), code: "disk" },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          backup: [
            { step: "1", text: this.$t("vm.create-from-acronis-backup") },
            { step: "2", text: this.$t("vm.select-backup-source") },
            { step: "3", text: this.$t("vm.compute-size"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ],
          bootDisk: [
            { step: "1", text: this.$t("vm.create-from-boot-disk") },
            { step: "2", text: this.$t("vm.select-disk") },
            { step: "3", text: this.$t("vm.compute-size-7"), code: "size" },
            { step: "4", text: this.$t("cloudspace.storage"), code: "storage" },
            {
              step: "5",
              text: this.$t("customer.configuration-details"),
              code: "details"
            }
          ]
        },
        availableIPAddresses: [],
        IPAddressesLoading: false,
        backupSource: "acronis",
        consumption: [],
        locationsLoading: false,
        locationConsumption: {},
        selectedImage: [],
        selectedCdrom: [],
        selectedDisk: [],
        selectedSnapshot: [],
        SelectedUserDataTemplateId: null,
        advancedUserData: false,
        magicParameters: [
          {
            param: "cloudspace_id",
            descriptiion: this.$t("user_data.the-target-cloudspace-id")
          },
          {
            param: "g8_cloudspace_id",
            descriptiion: this.$t(
              "user_data.the-target-cloudspace-id-on-the-g8"
            )
          },
          {
            param: "external_network_id",
            descriptiion: this.$t(
              "user_data.the-target-cloudspace-external-network-id-address"
            )
          },
          {
            param: "external_network_ip",
            descriptiion: this.$t(
              "user_data.the-target-external-network-ip-address"
            )
          },
          {
            param: "private_network",
            descriptiion: this.$t(
              "user_data.the-target-cloudspace-private-network"
            )
          },
          {
            param: "customer_id",
            descriptiion: this.$t("user_data.your-customer-id")
          },
          {
            param: "g8_name",
            descriptiion: this.$t("user_data.the-target-location")
          },
          {
            param: "user_jwt",
            descriptiion: this.$t("user_data.your-jwt")
          },
          {
            param: "vco_domain",
            descriptiion: this.$t("user_data.the-vco-domain")
          },
          {
            param: "vm_name",
            descriptiion: this.$t("user_data.the-entered-vm-name")
          }
        ]
      }
    },
    created() {
      this.getLocationInfo()
      // TODO: uncomment when create vm api call in the g8 side accepts gpus
      // this.getAvailablevGPUs()
      this.listCloudspaces()
      this.getCloudspaceInfo()
      this.getImages()
      this.getCDROMImages()
      this.listBootDisks()
      this.listVirtualMachines()
      if (this.isFixedPrice) this.getLocationsConsumption()
      this.getAvailableIPAddresses()
      this.currentRoute = this.$route.name
    },
    computed: {
      SelectedUserDataTemplate() {
        if (this.selectedImageData.user_data_templates) {
          return (
            this.selectedImageData.user_data_templates.find(
              (temp) => temp.id == this.SelectedUserDataTemplateId
            ) || {}
          )
        }
        return {}
      },
      ssh_comment() {
        return this.activeCustomer + "@" + window.location.hostname
      },
      formattedItemText() {
        return (item) => {
          if (!item) return " "
          return `${item.vendor} , ${item.model} , ${item.description}`
        }
      },
      isTherevGPUsOnLocation() {
        return this.vgpuProfiles.length > 0
      },
      docsLink() {
        return (
          "https://" +
          window.location.host +
          `/docs/${localStorage.getItem("language")}/cliGuide`
        )
      },
      showNote() {
        return this.step == 1 && this.vmSource == "s3"
      },
      showBackupNote() {
        return this.step == 4 && this.vmSource == "backup"
      },
      cloudspaceId() {
        return this.$route.params.cloudspaceId
      },
      location() {
        return atob(this.cloudspaceId).split(":")[0]
      },
      breadcrumbs() {
        return [
          {
            text: this.$t("base.cloudspaces"),
            exact: true,
            to: {
              name: "Cloudspaces",
              params: { customerId: this.activeCustomer }
            }
          },
          {
            text: this.cloudspaceName,
            exact: true,
            to: { name: "CloudspaceManagement" },
            isResourceName: true,
            id: this.cloudspaceId
          },
          {
            text: this.$t("gpus.virtual-machines"),
            exact: true,
            to: { name: "VMs" }
          },
          { text: this.$t("admin.create"), disabled: true }
        ]
      },
      isWindowsVm() {
        return this.osType.toLowerCase() == "windows"
      },
      imageVmSource() {
        return this.vmSource == "image"
      },
      cdImageVmSource() {
        return this.vmSource == "cdrom"
      },
      s3Filled() {
        return (
          this.s3.link != "" &&
          this.s3.key != "" &&
          this.s3.secret != "" &&
          this.s3.region != "" &&
          this.s3.bucket != "" &&
          this.s3.object_name != ""
        )
      },
      fromSnapshot() {
        return this.vmSource == "snapshot"
      },
      memoryLogObj() {
        return {
          minPos: 1,
          maxPos: 100,
          minVal: this.minMemory,
          maxVal: 262144
        }
      },
      diskLogObj() {
        return {
          minPos: 1,
          maxPos: 100,
          minVal: this.minBootDiskSize,
          maxVal: 20000
        }
      },
      fromDetachedDisk() {
        return this.fromSnapshot && !this.bootDisk_vm
      },
      showOsSelect() {
        return (
          ["empty", "s3", "backup", "bootDisk"].includes(this.vmSource) ||
          this.fromDetachedDisk
        )
      },
      bootDisksFiltered() {
        if (this.vmSource == "bootDisk") {
          return this.bootDisks.filter((disk) => {
            return disk.vm_id ? false : disk
          })
        }
        return this.bootDisks
      },
      disksHeaders() {
        if (this.vmSource == "bootDisk") {
          return [
            { text: this.$t("customer.disk"), value: "disk_name" },
            { text: this.$t("vm.size"), value: "disk_size" }
          ]
        }
        return [
          { text: this.$t("customer.disk"), value: "disk_name" },
          { text: this.$t("admin.virtual-machine"), value: "vm_name" },
          { text: this.$t("admin.cloudspace"), value: "cloudspace_id" },
          { text: this.$t("vm.size"), value: "disk_size" }
        ]
      },
      magicParametersNames() {
        return this.magicParameters.map((item) => item.param)
      },
      user_parameters() {
        return this.SelectedUserDataTemplate?.parameters
          ? this.SelectedUserDataTemplate.parameters.filter(
              (item) => !this.magicParametersNames.includes(item.name)
            )
          : []
      }
    },
    watch: {
      // TODO: uncomment when create vm api call in the g8 side accepts gpus
      // isThereGpu(val) {
      //   if(!val) {
      //     this.vgpuProfile = ""
      //   }
      // },
      operatingSystemType(val) {
        this.osType = val
        this.operatingSystems.filter((el) => {
          if (el.os_type == val) {
            this.operatingSystemNames = el.os_names
            this.operatingSystemName = this.operatingSystemNames[0]
          }
        })
      },
      vmSource(val) {
        this.inputs = []
        if (val != "") this.step++
        switch (val) {
          case "image":
            this.imageId = ""
            this.userDataAvailable = true
            this.attachVgpuProfileAvailable = true
            this.osType = ""
            break
          case "cdrom":
            this.imageId = ""
            this.userDataAvailable = false
            this.attachVgpuProfileAvailable = true
            this.osType = ""
            break
          case "empty":
            this.imageId = ""
            this.step = 2
            this.userDataAvailable = false
            this.attachVgpuProfileAvailable = true
            this.getOperatingSystems()
            break
          case "backup":
            this.imageId = ""
            this.setDefaultValues()
            this.userDataAvailable = false
            this.attachVgpuProfileAvailable = true
            this.getOperatingSystems()
            break
          case "s3":
            this.imageId = ""
            this.userDataAvailable = false
            this.attachVgpuProfileAvailable = false
            this.getOperatingSystems()
            break
          case "snapshot":
            this.bootDisks = []
            this.snapshots = []
            this.bootDiskId = ""
            this.snapshotId = ""
            this.imageId = ""
            this.userDataAvailable = false
            this.attachVgpuProfileAvailable = true
            this.listBootDisks()
            this.getOperatingSystems()
            break
          case "bootDisk":
            this.setDefaultValues()
            this.getOperatingSystems()
            break
          default:
            return
        }
      },
      imageName(val) {
        if (val.length > 20) {
          this.imageName = val.substring(0, 20) + "..."
        }
      },
      inputs: {
        deep: true,
        handler() {
          this.calculateTotalDiskSize()
          this.calculatePrice()
        }
      },
      vcpus() {
        this.calculatePrice()
      },
      memory(val) {
        if (val > 0) this.calculatePrice()
      },
      bootDiskSize(val) {
        if (val > 0) {
          this.calculateTotalDiskSize()
          this.calculatePrice()
        }
      },
      allVMDisks(val) {
        if (val === true) {
          this.inputs = this.disksFromSnapshot.concat(this.inputs)
        }
        if (val === false) {
          this.inputs = this.inputs.filter((el) => {
            return !el.snapshotDisk || (el.snapshotDisk && el.boot)
          })
        }
      },
      includeAutomatic() {
        this.listsnapshots(this.bootDiskId)
      },
      bootDisk_vm(val) {
        if (!val) this.allVMDisks = false
      },
      advancedUserData() {
        this.userData = ""
        this.SelectedUserDataTemplateId = null
      }
    },
    filters: {
      price(val, step) {
        if (step < 3) return 0
        return val
      }
    },
    methods: {
      getAvailablevGPUs() {
        this.vgpuProfileLoading = true
        this.call(
          "listAvailableGPUProfiles",
          { customerId: this.activeCustomer, location: this.location },
          (response) => {
            this.vgpuProfiles = this.getAvailableGPUS(response.result)
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.vgpuProfileLoading = false
          }
        )
      },
      getLocationInfo() {
        this.call(
          "getCustomerLocation",
          { customerId: this.activeCustomer, location: this.location },
          (data) => {
            this.storageOnly = data.storage_only
          }
        )
      },
      getOperatingSystems() {
        this.call(
          "getOSNames",
          {},
          (response) => {
            this.operatingSystems = response.result
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.osNamesLoading = false
            this.call(
              "getOSTypes",
              {},
              (response) => {
                this.operatingSystemTypes = response
                this.operatingSystemType = this.operatingSystemTypes[0]
              },
              (err) => {
                this.showPopup("error", err.message)
              },
              () => {
                this.osTypesLoading = false
              }
            )
          }
        )
      },
      calculateTotalDiskSize() {
        this.totalDiskSize = parseInt(this.bootDiskSize)
        this.inputs.forEach((item) => {
          this.totalDiskSize +=
            isNaN(item.diskSize) || !item.diskSize ? 1 : parseInt(item.diskSize)
        })
      },
      getCloudspaceInfo() {
        this.call(
          "getCloudspaceInfo",
          { customerId: this.activeCustomer, cloudspaceId: this.cloudspaceId },
          (data) => {
            this.cloudspaceName = data.name
          }
        )
      },
      listCloudspaces() {
        this.call(
          "listCloudspaces",
          { customerId: this.activeCustomer, location: [this.location] },
          (data) => {
            this.cloudspaces = data.result
          }
        )
      },
      getImages() {
        this.imagesLoading = true
        this.call(
          "listCustomerImages",
          {
            customerId: this.activeCustomer,
            location: this.location,
            includeLogo: true
          },
          (data) => {
            this.images = data.result
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.imagesLoading = false
          }
        )
      },
      getCDROMImages() {
        this.cdImagesLoading = true
        this.call(
          "listCustomerCDROMImages",
          {
            customerId: this.activeCustomer,
            location: this.location,
            includeLogo: true
          },
          (data) => {
            this.cdImages = data.result
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.cdImagesLoading = false
          }
        )
      },
      getImageData(id) {
        this.bootTypeLoading = true
        this.disableBootTypeSelect = false
        this.call(
          "getImage",
          {
            customerId: this.activeCustomer,
            location: this.location,
            imageId: id
          },
          (data) => {
            this.selectedImageData = data
            if (data.boot_type && this.bootTypes.includes(data.boot_type)) {
              this.bootType = data.boot_type
              this.disableBootTypeSelect = true
            }
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.bootTypeLoading = false
          }
        )
      },
      stepForward() {
        if (this.step == 0) {
          if (this.vmSource == "") {
            setTimeout(() => {
              this.showPopup(
                "error",
                this.$t("vm.please-select-virtual-machine-source-first")
              )
            }, 10)
            return
          }
          if (this.vmSource == "empty" || this.vmSource == "backup") {
            this.setDefaultValues()
          }
          if (this.vmSource == "empty") {
            this.step = 1
            return
          }
        } else if (this.step == 1) {
          if (this.vmSource == "s3") {
            if (!this.s3Filled) {
              setTimeout(() => {
                this.showPopup(
                  "error",
                  this.$t(
                    "vm.please-fill-all-required-fields-in-s3-information"
                  )
                )
              }, 10)
              return
            }
            if (
              !(
                this.s3.link.startsWith("http://") ||
                this.s3.link.startsWith("https://")
              )
            ) {
              setTimeout(() => {
                this.showPopup(
                  "error",
                  this.$t("vm.please-specify-s3-url-protocol-http-or-https")
                )
              }, 10)
              return
            }
          } else if (this.fromSnapshot || this.vmSource == "bootDisk") {
            if (this.bootDiskId == "") {
              setTimeout(() => {
                this.showPopup(
                  "error",
                  this.$t("vm.please-select-boot-disk-first")
                )
              }, 10)
              return
            }
          } else if (this.vmSource == "backup") {
            if (this.backupSource == "") {
              setTimeout(() => {
                this.showPopup(
                  "error",
                  this.$t("vm.please-select-bacup-source-first")
                )
              }, 10)
              return
            }
          } else if (
            this.imageId == "" &&
            this.cdImageId == "" &&
            this.vmSource != "snapshot"
          ) {
            setTimeout(() => {
              this.showPopup("error", this.$t("vm.please-select-image-first"))
            }, 10)
            return
          }
        } else if (this.step == 2) {
          if (
            this.isThereGpu &&
            (this.vgpuProfile == "" || this.vgpuName == "")
          ) {
            setTimeout(() => {
              this.showPopup(
                "error",
                this.$t("vm.please-fill-vgpu-information-first")
              )
            }, 10)
            return
          }
          if (this.fromSnapshot && this.snapshotId == "") {
            setTimeout(() => {
              this.showPopup(
                "error",
                this.$t("vm.please-select-snapshot-first")
              )
            }, 10)
            return
          }
          if (this.vmSource == "s3") {
            this.step = 4
            return
          }
        } else if (this.step == 3) {
          this.calculateTotalDiskSize()
          this.calculatePrice()
        }
        this.step++
      },
      imageRowClickEvent(event, row) {
        let item = row.item
        this.selectedImage = [row.item]
        this.step++
        this.cdImageId = ""
        this.imageId = item.image_id
        this.imageName = item.name
        this.osType = item.os_type
        this.minMemory = item.memory
        this.minBootDiskSize = item.boot_disk_size
        this.totalDiskSize = item.boot_disk_size
        this.getImageData(item.image_id)
        this.calculatePrice()
      },
      cdImageRowClickEvent(event, row) {
        let item = row.item
        this.selectedCdrom = [row.item]
        this.step++
        this.imageId = ""
        this.imageName = ""
        this.cdImageName = item.name
        this.cdImageId = item.cdrom_id
        this.operatingSystemName = item.os_name
      },
      diskRowClickEvent(event, row) {
        let item = row.item
        this.selectedDisk = [row.item]
        this.bootDisk_vm = item.vm_id
        this.bootDiskName = item.disk_name
        this.bootDiskId = item.disk_id
        this.snapshotId = ""
        this.listsnapshots(item.disk_id)
        this.minMemory =
          this.getVMMemory(item.vm_id) >= 128
            ? this.getVMMemory(item.vm_id)
            : 128
      },
      snapshotRowClickEvent(event, row) {
        let item = row.item
        this.selectedSnapshot = [row.item]
        this.snapshotId = item.snapshot_id
        this.snapshotName = item.snapshot_name
        this.getSnapshotDetails(item.snapshot_id)
        this.step++
      },
      listBootDisks() {
        this.bootDisks = []
        this.disksLoading = true
        this.call(
          "listDisks",
          { customerId: this.activeCustomer, location: this.location },
          (data) => {
            this.allDisks = data.result
            this.bootDisks = data.result.filter((el) => {
              return el.disk_type == "BOOT"
            })
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.disksLoading = false
          }
        )
      },
      listsnapshots(diskId) {
        let includeAutomatic = this.includeAutomatic
        this.snapshots = []
        this.snapshotsLoading = true
        this.call(
          "listDiskSnapshots",
          {
            customerId: this.activeCustomer,
            location: this.location,
            diskId: diskId,
            includeAutomatic: this.includeAutomatic
          },
          (response) => {
            if (includeAutomatic == this.includeAutomatic) {
              this.snapshots = response.result.map((elem) => ({
                snapshot_name: elem.snapshot_name,
                creation_time: new Date(elem.creation_time * 1000),
                snapshot_id: elem.snapshot_id
              }))
            }
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.snapshotsLoading = false
          }
        )
      },
      getSnapshotDetails(snapshotId) {
        this.step4Loading = true
        this.call(
          "getDiskSnapshot",
          {
            customerId: this.activeCustomer,
            location: this.location,
            diskId: this.bootDiskId,
            snapshotId: snapshotId,
            allVmDisks: this.allVMDisks
          },
          (data) => {
            if (this.allVMDisks) {
              data.disks.forEach((disk) => {
                let diskInfo = this.allDisks.find((id) => {
                  return disk.disk_id == id.disk_id
                })
                this.inputs.push({
                  diskSize: diskInfo["disk_size"],
                  disabled: true,
                  snapshotDisk: true,
                  boot: diskInfo["disk_type"] == "BOOT" ? true : false,
                  iops: diskInfo.iotune.iops
                })
                if (diskInfo.disk_type != "BOOT") {
                  this.disksFromSnapshot.push({
                    diskSize: diskInfo["disk_size"],
                    disabled: true,
                    snapshotDisk: true,
                    boot: false,
                    iops: diskInfo.iotune.iops
                  })
                }
              })
            }
          },
          (err) => {
            this.showPopup("error", err)
          },
          () => {
            this.step4Loading = false
          }
        )
      },
      listVirtualMachines() {
        this.call(
          "listCloudspaceVirtualMachines",
          { customerId: this.activeCustomer, cloudspaceId: this.cloudspaceId },
          (data) => {
            this.virtualMachines = data.result
          }
        )
      },
      getCsName(cloudspaceId) {
        let csName = ""
        this.cloudspaces.forEach((cs) => {
          if (cloudspaceId == cs.cloudspace_id) {
            csName = cs.name
          }
        })
        return csName
      },
      getVMMemory(vmId) {
        let vmMemory = ""
        this.virtualMachines.forEach((vm) => {
          if (vmId == vm.vm_id) {
            vmMemory = vm.memory
          }
        })
        return vmMemory
      },
      addNewDisk() {
        this.inputs.push({
          diskSize: 1,
          disabled: false,
          snapshotDisk: false,
          boot: false,
          iops: 2000
        })
      },
      deleteDisk(index) {
        this.inputs.splice(index, 1)
      },
      VMValidInput() {
        this.disks = []
        if (this.name == "") {
          setTimeout(() => {
            this.showPopup(
              "error",
              this.$t("vm.please-enter-virtual-machine-name-first")
            )
          }, 10)
          return false
        }
        if (!this.advancedUserData && this.userData != "") {
          if (
            this.vmSource != "s3" &&
            !this.userData.match(
              /(ssh-rsa AAAAB3NzaC1yc2|ecdsa-sha2-nistp256 AAAAE2VjZHNhLXNoYTItbmlzdHAyNT|ecdsa-sha2-nistp384 AAAAE2VjZHNhLXNoYTItbmlzdHAzODQAAAAIbmlzdHAzOD|ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1Mj|ssh-ed25519 AAAAC3NzaC1lZDI1NTE5|ssh-dss AAAAB3NzaC1kc3)[0-9A-Za-z+/]+[=]{0,3}( .*)?/
            )
          ) {
            setTimeout(() => {
              this.showPopup(
                "error",
                this.$t("vm.please-enter-correct-ssh-key")
              )
            }, 10)
            return false
          }
          //check that there is no comment already
          if (this.userData.trim().split(/\s+/).length < 3) {
            this.userData = this.userData + " " + this.ssh_comment
          }
        }

        if (this.vmSource != "s3") {
          this.inputs.forEach((el) => {
            if (!el.snapshotDisk) {
              this.disks.push(el.diskSize)
            }
          })
        }
        this.params = {
          customerId: this.activeCustomer,
          cloudspaceId: this.cloudspaceId
        }
        return true
      },
      handleCreateVmResponse(response) {
        if (this.vms_no == 1) {
          this.params.vmId = response[0].vm_id
          this.$store.state.apiTasks.find(
            (x) => x.response && this.isEqualObj(x.response.data, response[0])
          ).redirectLink = { name: "VMManagement", params: this.params }

          this.showPopup(
            "success",
            this.$t("vm.virtual-machine-created-successfully")
          )
          this.$route.name === this.currentRoute
            ? this.$router.replace({
                name: "VMManagement",
                params: {
                  customerId: this.activeCustomer,
                  cloudspaceId: this.cloudspaceId,
                  vmId: response[0].vm_id
                }
              })
            : ""
        } else {
          this.$router.push({ name: "VMs" })
          this.showPopup(
            "success",
            this.$t("vm.virtual-machines-created-successfully")
          )
        }
      },
      createVirtualMachine() {
        if (this.vms_no > this.availableIPAddresses.length) {
          {
            setTimeout(() => {
              this.showPopup(
                "error",
                this.$t("vm.number-of-vms-couldnt-exceed-ip-limit")
              )
            }, 10)
          }
          return
        }
        if (this.vms_no < 1) {
          {
            setTimeout(() => {
              this.showPopup("error", this.$t("vm.invalid-batch-size"))
            }, 10)
          }
          return
        }
        if (this.showCard) this.showCard = false

        if (!this.VMValidInput()) {
          return
        }

        this.submitLoading = true

        let vmList = []
        if (this.vms_no > 1) {
          vmList = Array.from({ length: this.vms_no }, (_, i) => {
            return {
              name: `${this.name}_${i}`,
              privateIp: this.availableIPAddresses[i]
            }
          })
        } else {
          vmList.push({
            name: this.name,
            privateIp: this.private_ip
          })
        }

        if (this.vmSource == "image" || this.vmSource == "cdrom") {
          let ParameteruserData, payloaduserData
          if (this.advancedUserData) {
            ParameteruserData = null
            try {
              payloaduserData = parse(this.userData)
            } catch (error) {
              setTimeout(() => {
                this.showPopup(
                  "error",
                  this.$t("vm.incorrect-user-data") + ": \n" + error.message
                )
              }, 100)
              this.submitLoading = false
              return
            }
          } else {
            ParameteruserData = this.userData
            payloaduserData = null
          }
          this.multipleCall(
            "createVirtualMachine",
            vmList,
            {
              description: this.description != "" ? this.description : " ",
              diskSize: this.bootDiskSize,
              imageId: this.vmSource == "image" ? this.imageId : undefined,
              cdromId: this.vmSource == "cdrom" ? this.cdImageId : undefined,
              dataDisks:
                this.disks.length == 0 ? undefined : `${this.disks.toString()}`,
              vcpus: this.vcpus,
              // memory: this.memory % 2 == 0 ? (this.memory) : (this.memory + 1),
              memory:
                this.memory % 128 == 0
                  ? this.memory
                  : parseInt(this.memory) + parseInt(128 - (this.memory % 128)),
              userData: ParameteruserData,
              payload: {
                userdata: payloaduserData,
                UserDataParameters: this.SelectedUserDataTemplate.parameters
                  ? this.getParamsObject(
                      this.SelectedUserDataTemplate.parameters
                    )
                  : {}
                // TODO: uncomment when create vm api call in the g8 side accepts gpus
                // gpu_id: this.vgpuProfile,
                // vgpu_name: this.vgpuName
              },
              customerId: this.activeCustomer,
              cloudspaceId: this.cloudspaceId,
              bootType: this.bootType,
              startVm: this.startVm
            },
            { name: "name", privateIp: "privateIp" },
            (response) => {
              this.handleCreateVmResponse(response)
            },
            (err) => {
              this.showPopup("error", err.message)
            },
            () => {
              this.submitLoading = false
            }
          )
        } else if (this.vmSource == "s3") {
          this.multipleCall(
            "createVirtualMachineFromS3WithPayload",
            vmList,
            {
              payload: {
                link: this.s3.link,
                key: this.s3.key,
                secret: this.s3.secret,
                region: this.s3.region,
                bucket: this.s3.bucket,
                object_name: this.s3.object_name,
                strict: this.s3.strict,

                description: this.description != "" ? this.description : " ",
                os_type: this.operatingSystemType,
                os_name: this.operatingSystemName,
                vcpus: this.vcpus,
                memory:
                  this.memory % 128 == 0
                    ? this.memory
                    : parseInt(this.memory) +
                      parseInt(128 - (this.memory % 128)),
                boot_type: this.bootType
              },
              customerId: this.activeCustomer,
              cloudspaceId: this.cloudspaceId,
              startVm: this.startVm
            },
            {
              send_in_a_nested_field: {
                payload: { name: "name", privateIp: "privateIp" }
              }
            },
            (response) => {
              this.handleCreateVmResponse(response)
            },
            (err) => {
              this.submitLoading = false
              this.showPopup("error", err.message)
            }
          )
        } else if (
          this.vmSource == "empty" ||
          this.vmSource == "backup" ||
          this.vmSource == "bootDisk"
        ) {
          let acronis = false,
            veeam = false
          if (this.vmSource == "backup") {
            acronis = this.backupSource == "acronis" ? true : false
            veeam = this.backupSource == "veeam" ? true : false
          }
          let bootDiskId =
            this.vmSource == "bootDisk" ? this.bootDiskId : undefined
          this.multipleCall(
            "createVirtualMachine",
            vmList,
            {
              description: this.description != "" ? this.description : " ",
              diskSize: this.bootDiskSize,
              osType: this.operatingSystemType,
              osName: this.operatingSystemName,
              dataDisks:
                this.disks.length == 0 ? undefined : `${this.disks.toString()}`,
              vcpus: this.vcpus,
              memory:
                this.memory % 128 == 0
                  ? this.memory
                  : parseInt(this.memory) + parseInt(128 - (this.memory % 128)),
              customerId: this.activeCustomer,
              cloudspaceId: this.cloudspaceId,
              acronis: acronis,
              veeam: veeam,
              bootDiskId: bootDiskId,
              bootType: this.bootType,
              startVm: this.startVm
              // TODO: uncomment when create vm api call in the g8 side accepts gpus
              // payload: {
              //   gpu_id: this.vgpuProfile
              // },
            },
            { name: "name", privateIp: "privateIp" },
            (response) => {
              this.handleCreateVmResponse(response)
            },
            (err) => {
              this.submitLoading = false
              this.showPopup("error", err.message)
            }
          )
        } else if (this.fromSnapshot) {
          this.multipleCall(
            "createVirtualMachine",
            vmList,
            {
              description: this.description != "" ? this.description : " ",
              dataDisks:
                this.disks.length == 0 ? undefined : `${this.disks.toString()}`,
              vcpus: this.vcpus,
              memory: this.memory % 2 == 0 ? this.memory : this.memory + 1,

              bootDiskId: this.bootDiskId,
              allVmDisks: this.allVMDisks,
              snapshotId: this.snapshotId,
              customerId: this.activeCustomer,
              cloudspaceId: this.cloudspaceId,
              osType: this.fromDetachedDisk
                ? this.operatingSystemType
                : undefined,
              osName: this.fromDetachedDisk
                ? this.operatingSystemName
                : undefined,
              bootType: this.bootType,
              startVm: this.startVm,
              // TODO: uncomment when create vm api call in the g8 side accepts gpus
              // payload: {
              //   gpu_id: this.vgpuProfile
              // },
              redirect: "VMManagment",
              params: { key: "vmId", value: "vm_id" }
            },
            { name: "name", privateIp: "privateIp" },
            (response) => {
              this.handleCreateVmResponse(response)
            },
            (err) => {
              this.submitLoading = false
              this.showPopup("error", err.message)
            }
          )
        }
      },
      getParamsObject(paramsList) {
        return paramsList.reduce((obj, param) => {
          obj[param.name] = param.default_value
          return obj
        }, {})
      },
      calculatePrice() {
        if (this.bootDiskSize != 0) {
          this.iops = 2000
          this.inputs.forEach((input) => {
            this.iops += input.iops
          })
        }
        this.priceInputs = {
          memory:
            this.memory < this.minMemory
              ? this.minMemory
              : this.memory % 128 == 0
                ? this.memory
                : parseInt(this.memory) + parseInt(128 - (this.memory % 128)),
          vcpus: this.vcpus == "" || this.vcpus == 0 ? 1 : this.vcpus,
          disksize: this.totalDiskSize != 0 ? this.totalDiskSize : 1,
          iops: this.iops,
          isWindowsVm: this.isWindowsVm,
          customerId: this.activeCustomer,
          location: this.location
        }
      },
      stepBack() {
        if (this.step == "2" && this.vmSource == "empty") {
          return (this.step = this.step - 2)
        }
        if (this.step == "4" && this.vmSource == "s3") {
          return (this.step = this.step - 2)
        }
        return this.step--
      },
      setDataDiskSliderPosition(item, checkMin = false) {
        if (item.diskSize > 20000) {
          item.diskSize = 20000
        }
        if (checkMin)
          isNaN(item.diskSize) || item.diskSize < 1 ? (item.diskSize = 1) : ""
        var diskSize = new Log({
          minPos: 1,
          maxPos: 100,
          minVal: 1,
          maxVal: 20000
        })
        item.dataDiskSliderPosition = Math.round(
          diskSize.position(item.diskSize)
        )
      },
      setDataDiskValue(item) {
        var diskSize = new Log({
          minPos: 1,
          maxPos: 100,
          minVal: 1,
          maxVal: 20000
        })
        item.diskSize = Math.round(diskSize.value(item.dataDiskSliderPosition))
      },
      changeVcpus(val) {
        this.vcpus = val
      },
      changeMemoryValue(val) {
        this.memory = val
      },
      changeBootDisk(val) {
        this.bootDiskSize = val
      },
      setDefaultValues() {
        if (this.vmSource == "backup") {
          this.minMemory = 1024
          this.minBootDiskSize = 10
          this.totalDiskSize = 10
        } else if (this.vmSource == "bootDisk") {
          this.minMemory = 1024
        }
      },
      getAvailableIPAddresses() {
        this.IPAddressesLoading = true
        this.call(
          "getPrivateNetworkIPAddresses",
          { customerId: this.activeCustomer, cloudspaceId: this.cloudspaceId },
          (data) => {
            this.availableIPAddresses = data.ip_addresses.sort((a, b) => {
              return (
                parseInt(a.split(".").join("")) -
                parseInt(b.split(".").join(""))
              )
            })
            this.private_ip = this.availableIPAddresses[0]
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.IPAddressesLoading = false
          }
        )
      },
      getLocationsConsumption() {
        this.consumption = []
        this.locationsLoading = true
        this.call(
          "listCustomerLocationsConsumption",
          { customerId: this.activeCustomer },
          (data) => {
            this.consumption = data.result
            this.locationConsumption = data.result.find((item) => {
              return item.location == this.location
            })
          },
          (err) => this.showPopup("error", err.message),
          () => {
            this.locationsLoading = false
          }
        )
      },
      currentStepText(HeaderNumber) {
        if (this.step === 0) {
          return this.stepsHeaders["step1"][HeaderNumber - 1].text
        }
        return this.stepsHeaders[this.vmSource][HeaderNumber - 1].text
      },
      userDataInputHandler(val) {
        this.userData = val
      }
    },
    mounted() {
      this.loading = false
    }
  }
</script>

<style scoped>
  .container {
    height: 500px;
    position: relative;
    width: 250px;
  }

  .vertical-center {
    margin: 0;
    position: absolute;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
  }

  .vm-stepper {
    height: unset;
    min-height: 72px;
  }
  .step-content {
    display: block !important;
    transform-origin: center top 0px;
  }
  .v-input--is-disabled.v-select .v-icon {
    pointer-events: auto;
  }
  .image-logo {
    aspect-ratio: 1;
    width: 27px;
    border-radius: 50%;
    margin: 7px;
    margin-left: 0;
  }
  .image-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .image-name,
  .cdrom-name {
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .btn-shadow {
    align-items: center;
    justify-content: center;
    transition: box-shadow 0.3s;
  }
  .btn-shadow:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  #data-template-help h4,
  #data-template-help h3 {
    margin-top: 10px;
  }
  #data-template-help ul {
    margin-top: 7px;
    margin-left: 35px;
  }
  #data-template-help li {
    margin: 10px 0;
  }
  #template-params {
    max-height: 200px;
    overflow-y: scroll;
    overflow-x: hidden;
    padding: 5px;
    margin: 20px 0;
  }

  #template-params :deep(.v-input__details) {
    display: none !important;
  }
</style>
